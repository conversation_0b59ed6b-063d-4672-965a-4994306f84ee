---
title: Index
layout: layout.html
orderPath: /__index
eleventyNavigation:
  key: Index
---

# Autocheckin Frontend

Autocheckin is a hotelinking product that allows you to check in online.

## Getting started

First of all we install the dependencies of this project

```bash
npm install
```

Compiles and hot-reloads for development

```bash
npm run serve
```

You can also target the other environments directly with the following commands

```bash
npm run serve:dev
npm run serve:beta
npm run serve:prod
```

## Prerequesits

If you run autocheckin locally you will also need to have Hotelinking Api, Hotelinking Integrations, Hotelinking Autocheckin and Hotelinking Payments (optional) dockers running. To do this, follow the general setup instructions defined in our [Confluence](https://hotelinking.atlassian.net/wiki/spaces/MA/pages/2272264235/Configuracion+Initial+Hotelinking+-+2).

You will also need to set up a Brand with the Autocheckin product active in the Hotelinking App's private

![Autocheckin private config](src/assets/images/docs/autocheckin-private-config.png)

Once you have all this, you will be able to access the autocheckin locally at the following url. The brandID can be retrieved in the first column of the private

```bash
http://localhost:8080/{brandID}
```

To see in detail all the available configurations of this product together with their implications please check the following documentation of our [Confluence](https://hotelinking.atlassian.net/wiki/spaces/MA/pages/1365442592/Configuraci+n+de+AutoCheckin).

## Testing

### Run your unit tests

```bash
npm run test:unit
```

### Run your end-to-end tests

Make sure you have an instance of Autocheckin running locally.

```bash
npm run serve
```

You can then run individual or all of the tests end to end with this command

```bash
CYPRESS_HL_ENV=testing npx cypress run
CYPRESS_HL_ENV=testing npx cypress run --spec "**/{test}.spec.js"
```

#### Code Coverage

We have installed a code coverage for the e2e tests. To generate the report, when we start the server, we must do it in testing mode as follows. This is done so as not to taint the build, as it generates more code in the application itself.

```bash
NODE_ENV=testing npm run serve
```

Once this is done, you can run the tests normally as mentioned above.

⚠️ If a test is run individually it will provide a report of only that test, so it is normal that the rate of tested code is very low.

When the test execution is finished, a /coverage folder will be generated in the root of the project. If we want to see the final result visually, we can open the index.html file located in /coverage/Icov-report.

### Lints and fixes files

```bash
npm run lint
```
