---
title: Autocheckin Mock
layout: layout.html
eleventyNavigation:
  key: Autocheckin Mock
  order: 1
---
There are two different ways to mock your autocheckin app.

Both: Open .env file

Option 1
`VUE_APP_MOCK`

=> setting this variable on true will mock almost all requests, you can check which ones in *mocks/testHandlers.js*

---

Option 2
`VUE_APP_BRANDS_DEMO=""`

=> setting this variable with selected brands(id) will mock only those brands(id), you can check which ones in *demo/demoHandlers.js*
=> example: VUE_APP_BRANDS_DEMO="1,3,4" => only brands 1,3 and 4 will be mocked. Every other brand will make real requests.
=> Right now only mocks reservations, charges(integrations) and documents(hlautocheckin)