import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import VueSignaturePad from "vue-signature-pad";
import { i18n } from "./locales";
import Vuelidate from "vuelidate";
import { Amplify, Logger } from "@aws-amplify/core";
import APIConfiguration from "./AWS/api-amplify-configuration";
import AuthConfiguration from "./AWS/auth-amplify-configuration";
import GeoConfiguration from "./AWS/geo-amplify-configuration";
import CloudWatchLogger from "@/utils/CloudWatchLogger";
import "./assets/style/index.scss";
import {
	applyPolyfills,
	defineCustomElements,
} from "@aws-amplify/ui-components/loader";
import receptionUtils from "../src/utils/receptionUtils";

Vue.config.productionTip = false;

Vue.use(Vuelidate);
Vue.use(VueSignaturePad);

if (receptionUtils.setReceptionMode()) {
	const amplifyConfig = {
		Auth: {
			userPoolId: process.env.VUE_APP_RECEPTION_USER_POOL_ID,
			userPoolWebClientId:
				process.env.VUE_APP_RECEPTION_USER_POOL_WEB_CLIENT_ID,
			identityPoolId: process.env.VUE_APP_IDENTITYPOOL_ID,
			region: process.env.VUE_APP_AWS_REGION,
			mandatorySignIn: true,
			signUpVerificationMethod: "link",
		},
		API: APIConfiguration,
		geo: GeoConfiguration,
		AnalyticsTracker: {
			region: process.env.VUE_APP_AWS_REGION,
		},
	};
	Amplify.configure(amplifyConfig);

	applyPolyfills().then(() => {
		defineCustomElements(window);
	});

	Vue.config.ignoredElements = [/amplify-\w*/];
} else {
	const amplifyConfig = {
		API: APIConfiguration,
		Auth: AuthConfiguration,
		geo: GeoConfiguration,
		AnalyticsTracker: {
			region: process.env.VUE_APP_AWS_REGION,
			bufferSize: 100, //(optional) => Buffer size for events in number of items
			flushSize: 1, //(optional) => Number of events to be deleted from the buffer when flushed
			flushInterval: 1000, //(optional) => The interval in milliseconds to permorf a buffer check and flush if necessary
			resendLimit: 50, //(optional) => The limit for failed recording retries
		},
	};
	Amplify.configure(amplifyConfig);
}

Logger.LOG_LEVEL = process.env.VUE_APP_LOG_LEVEL
	? process.env.VUE_APP_LOG_LEVEL
	: "INFO";

if (process.env.VUE_APP_MOCK === "true") {
	const { worker } = require("../mocks/testBrowser");
	worker.start();
}
const BRAND_ID = window.location.pathname.split("/")[1];

const demoQueryParam = new URLSearchParams(window.location.search).get("demo");
const isDemoBrand =
	(demoQueryParam && demoQueryParam === "true") ||
	(BRAND_ID &&
		process.env.VUE_APP_BRANDS_DEMO?.split(",")?.includes(BRAND_ID)) ||
	store.getters["app/isDemoMode"];

if (isDemoBrand) {
	store.dispatch("app/TOGGLE_DEMO_MODE", true);
	if (store) {
		const { worker } = require("../mocks/demoBrowser");
		worker.start({
			onUnhandledRequest: "bypass",
		});
	}
}

// First add log wrapper and than let start vue
(async () => {
	if (process.env.VUE_APP_CLOUDWATCH_ENABLED === "true") {
		// Try catch to prevent not start Vue if the logger Factory fails
		try {
			// Start CloudWatchLogger to change console.logs functions to send logs to cloudwatch
			await new CloudWatchLogger({
				region: process.env.VUE_APP_AWS_REGION,
				group: process.env.VUE_APP_CLOUDWATCH_GROUP,
				levels: process.env.VUE_APP_CLOUDWATCH_LEVELS.split(","),
				accessKey: process.env.VUE_APP_LOG_WRITTER_ACCESS_KEY,
				secretKey: process.env.VUE_APP_LOG_WRITTER_SECRET_KEY,
			}).init();
		} catch (error) {
			console.error("Error starting Cloudwatch logger");
		}
	}
})().then(() => {
	const app = new Vue({
		router,
		store,
		i18n,
		render: (h) => h(App),
	}).$mount("#app");

	if (window.Cypress) {
		window.app = app;
	}
});
