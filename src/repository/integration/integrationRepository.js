import { API } from "@aws-amplify/api";
import Reservation from "@/entities/Reservation";
import logInfo from "../logInfo";
import { Tracker } from "@/tracker";
import errorCodes from "@/errorCodes";

const resource = "/brands";

export default {
	async getReservation(brandId, params, track = true) {

		logInfo("getReservation", { params });

		try {
			const response = await API.get(
				"integrations",
				`${resource}/${brandId}/reservations`,
				{
					queryStringParameters: params,
				},
			);
			console.info("Reservations", {
				params,
				brandId,
				response: response,
			});

			if (response.error) {
				throw response;
			}

			if (!response.data.length) {
				Tracker.recordRequest(
					"reservation",
					params,
					Tracker.createError({ code: errorCodes.EMPTY_RESERVATION }),
				);
				Tracker.recordCustomEvent("reservation_searched", {
					manual: params?.manual ?? true,
					params,
					response: Tracker.createError({ code: errorCodes.EMPTY_RESERVATION }),
				});

				return [];
			}

			const eligibilityResult =	this.handleEligibility(response.data);

			// Multiple reservations case
			if(Array.isArray(eligibilityResult)){
				if(eligibilityResult[0]?.action === "show modal"){
					// For the time being, we track the first reservation
					this.trackReservationError(params, eligibilityResult[0])
				} else {
					// For the time being, we track the first reservation
					if(track){
						this.trackReservationSearch(params, eligibilityResult[0]);
					}
				}
				// If all invalid with different localizers, we still return all reservations 
				return eligibilityResult.map((reservation) => new Reservation(reservation))
			}

			// Single reservation case (or multiple but all invalid with same localizer)
			const reservation = response.data.map((reservation) => new Reservation(reservation))
			
			if(eligibilityResult.eligible){
				if(track){
					this.trackReservationSearch(params, response.data[0]);
				}
				return reservation;
			} else {
				if(eligibilityResult.action === "show modal"){
					// Track the invalid reservation
					this.trackReservationError(params, response.data[0])
				} else {
					// If the check state was check_in or pre_checked_in, we track this as a valid reservation
					if(track){
						this.trackReservationSearch(params, response.data[0]);
					}
				}
				return {reservation: reservation, action: eligibilityResult.action, checkState: response.data[0].check_state};
			} 

		} catch (error) {
			console.error("getReservations endpoint failed", {
				error,
				params,
				brandId,
			});

			const trackerError = Tracker.createError({
				code:
					error?.error?.code ??
					error?.response?.data?.error?.code ??
					errorCodes.UNKNOWN,
				message: error?.error?.message ?? null,
			});

			Tracker.recordRequest("reservation", params, trackerError);
			Tracker.recordCustomEvent("reservation_searched", {
				manual: params?.manual ?? true,
				params,
				response: trackerError,
			});

			// If anything fails, return [] as no reservation found
			throw error;
		}
	},

	sanitizeReservation(response) {
		if (!response || response?.length === 0) {
			return {};
		}
		const holder =
			response.guests.find((guest) => {
				return guest.holder;
			}) ?? response.guests[0];

		return {
			holder: {
				name: holder.first_name,
				surname: holder.surname,
				second_surname: holder.second_surname,
				nationality: holder.nationality,
				document_type: holder.document_type,
				document_number: holder.document_id,
				pms_id: holder.pms_id,
			},
			res_id: response.res_id,
			res_localizer: response.res_localizer,
			brand_id: response.brand_id,
			check_in: response.check_in,
			check_out: response.check_out,
			booking_state: response.booking_state,
			check_state: response.check_state
		};
	},

	async updatePMSGuest(brandId, body) {
		logInfo("updatePMSGuest", { body });
		return API.put(
			"integrations",
			`${resource}/${brandId}/reservations/update`,
			{
				body,
			},
		);
	},

	async sendGuestToPMS(brandId, body) {
		const guests = body.guests.map((guest) => {
			return {
				name: guest.name,
				surname: guest.surname,
				second_surname: guest.second_surname,
				nationality: guest.nationality,
			};
		});
		const trackerParams = {
			...body,
			guests: guests,
		};

		try {
			logInfo("sendGuestToPMS", { body });
			const response = await API.post(
				"integrations",
				`${resource}/${brandId}/reservations/pre-check`,
				{
					body,
				},
			);

			if (response?.error) {
				throw response;
			}

			Tracker.recordRequest("checkin", trackerParams, response);
			return response;
		} catch (error) {
			const trackerError = Tracker.createError({
				code:
					error?.error?.code ??
					error?.response?.data?.error?.code ??
					errorCodes.UNKNOWN,
				message: error?.error?.message ?? null,
			});
			Tracker.recordRequest("checkin", trackerParams, trackerError);

			if (error?.error) {
				return error;
			} else {
				throw error;
			}
		}
	},

	sendComments(brandId, payload) {
		return API.post(
			"integrations",
			`${resource}/${brandId}/reservations/comments`,
			{
				body: payload,
			},
		);
	},

	async getLocalizerCharges(brandId, localizer) {
		const response = await API.get(
			"integrations",
			`${resource}/${brandId}/reservations/charges`,
			{
				queryStringParameters: { localizer },
			},
		);
		return response.data;
	},

	async trackReservationSearch(params, reservation) {
    Tracker.recordRequest("reservation", params, this.sanitizeReservation(reservation));
    Tracker.recordCustomEvent("reservation_searched", {
      manual: params?.manual ?? true,
      params,
      response: this.sanitizeReservation(reservation),
    });
  },

	async trackReservationError(params, reservation){
		Tracker.recordRequest(
			"reservation",
			params,
			Tracker.createError({
				code: errorCodes.INVALID_RESERVATION,
				message: this.handleInvalidReservationMessage(reservation),
			}),
		);
		Tracker.recordCustomEvent("reservation_searched", {
			manual: params?.manual ?? true,
			params,
			response: Tracker.createError({
				code: errorCodes.INVALID_RESERVATION,
				message: this.handleInvalidReservationMessage(reservation),
			}),
		});
	},

	handleEligibility(reservationArr) {
	
		if (reservationArr.length === 1) {
			const singleReservation = reservationArr[0];
			const eligibility = this.isEligible(singleReservation);
			if (eligibility === null) {
				return {
					eligible: false,
					action: this.handleNotEligible(singleReservation).action,
				};
			} else {
				return {eligible: true}
			}
		} else {
			const filteredResult = this.filterEligibleReservations(reservationArr)
			if(filteredResult[0].action && filteredResult.length === 1){
					return {
						eligible: false,
						action: filteredResult[0].action,
					};
			} else {
				return filteredResult
			}
		}
	},
	
	isEligible(reservation) {
		const checkState = reservation.check_state;
		const bookingState = reservation.booking_state;
		const bookingStateEligible = ["none", "reserved", null].includes(
			bookingState,
		);
	
		if (checkState === "none") {
			return reservation;
		} else if (checkState === null && bookingStateEligible) {
			return reservation;
		} else {
			return null;
		}
	},
	
	handleNotEligible(reservation) {
		const checkState = reservation.check_state;
		const bookingState = reservation.booking_state;
		const showModal =
			["cancelled", "no_show", "in_house", "checked_out"].includes(
				bookingState,
			) || ["pre_checked_out", "check_out"].includes(checkState);
	
		if(checkState === "pre_checked_in" || checkState === "check_in") {
			return {action: "redirect"};
		} else if (showModal) {
			return {action: "show modal"};
		}
	},
	
	filterEligibleReservations(reservations) {
		const eligibleReservations = reservations
			.map((reservation) => this.isEligible(reservation))
			.filter(Boolean);
			
		if(eligibleReservations.length !== 0){
			return eligibleReservations;
		} else if (reservations.length > 1){
			const localizer = reservations[0].res_localizer?.toLowerCase()
			const allSameLocalizer = reservations.every(reservation =>
				reservation.res_localizer?.toLowerCase() === localizer
			);
			if(allSameLocalizer){
			// If none of the reservations are eligible and they have the same localizer, return first one
				const firstReservation = reservations[0];
  			firstReservation.action = this.handleNotEligible(firstReservation).action;
  			return [firstReservation];
			} else {
			// If none of the reservations are elegible and they have different localizers, return all the reservations with actions so guest can choose
					const notEligibleWithActions = reservations.map(reservation => {
						return { ...reservation, action: this.handleNotEligible(reservation).action }
				})
				return notEligibleWithActions;
			}
		
		} else {
			return []
		}
	},

	handleInvalidReservationMessage(reservation){
		let reservationStatus;
		if(reservation.check_state !== null){
			reservationStatus = reservation.check_state
		} else {
			reservationStatus = reservation.booking_state
		}
		return `The reservation is invalid because the reservation status is ${reservationStatus}`
	}
	
};
