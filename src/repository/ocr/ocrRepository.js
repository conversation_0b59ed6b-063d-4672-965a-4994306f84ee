import { API } from "@aws-amplify/api";
import logInfo from "../logInfo";
import { Tracker } from "@/tracker";
import errorCodes from "@/errorCodes";

export default {
	async getImageInfo(payload, config, documentScanned) {
		const params = {
			country: payload.country,
			DNINationality: payload.nationality,
			images: payload.images,
			allowExpiredDocuments: config.allowExpiredDocuments,
			allowDrivingLicense: config.allowDrivingLicense,
			res_id: payload.res_id,
			check_in: payload.check_in,
			check_out: payload.check_out,
		};

		if (documentScanned === "signature") {
			params.isSignatureImage = true;
		}

		const trackerParams = { ...params };

		try {
			logInfo("getImageInfo");
			const response = await API.post(
				"ocr",
				`/brands/${payload.brandId}/scan`,
				{
					body: params,
				},
			);

			//send to the catch an invalid document response due to basis of business rules
			if (response.error) {
				throw response;
			}

			const saninitizedResponse = sanitizeOCRResponse(response);

			Tracker.recordRequest("scan", trackerParams, saninitizedResponse);
			Tracker.recordCustomEvent("guest_scanned", {
				params,
				response: saninitizedResponse,
			});

			return response;
		} catch (error) {
			const saninitizedResponse = sanitizeOCRResponse(error); //bussiness cases
			const trackerError = Tracker.createError({
				code:
					error?.response?.data?.error?.code ??
					error?.error?.code ??
					errorCodes.UNKNOWN_OCR,
				data: saninitizedResponse,
			});
			Tracker.recordRequest("scan", trackerParams, trackerError);
			Tracker.recordCustomEvent("guest_scanned", {
				params,
				response: trackerError,
			});

			if (error?.response?.data?.error?.code) {
				console.error("getImageInfo endpoint failed", { error });
				throw error;
			} else {
				return error;
			}
		}
	},
};

const sanitizeOCRResponse = (document) => {
	if (!document) {
		return {
			data: {},
		};
	}

	return {
		data: {
			...document.data,
			face: "hidden",
			signature: "hidden",
		},
	};
};
