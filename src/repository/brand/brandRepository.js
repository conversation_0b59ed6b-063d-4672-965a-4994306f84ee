import { API } from "@aws-amplify/api";
import logInfo from "../logInfo";
// import errorHandler from "../clients/axiosErrorhandler";
const resource = "/brands";

export default {
  getBrand(id) {
    logInfo("getBrand");
    return API.get("hotelinking", `${resource}/${id}/info`);
  },
  getBrandGDPR(id, lang) {
    logInfo("getBrandGDPR", { lang });
    return API.get("hotelinking", `${resource}/${id}/gdpr`, {
      queryStringParameters: {
        lang
      }
    });
  },
  getConfig(id) {
    logInfo("getConfig");
    return API.get(
      "hotelinking",
      `${resource}/${id}/products/21/configuration`
    );
  }
};
