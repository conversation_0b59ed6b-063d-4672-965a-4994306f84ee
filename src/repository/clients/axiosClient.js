import axios from "axios";

// Config
const baseDomain = process.env.VUE_APP_API_DOMAIN;
const baseURL = `${baseDomain}`;

// Create axios client
const axiosClient = axios.create({
	baseURL,
	headers: {
		Accept: "application/json",
		"Content-Type": "application/json",
	},
});

// Add a request interceptor
axiosClient.interceptors.request.use(
	function (config) {
		return config;
	},
	function (error) {
		console.error("Request rejected");
		return Promise.reject(error);
	},
);

// Add a response interceptor
axiosClient.interceptors.response.use(
	function (response) {
		return response;
	},
	function (error) {
		console.error("Response rejected");
		return Promise.reject(error);
	},
);

export default axiosClient;
