import { API } from "@aws-amplify/api";
import logInfo from "../logInfo";

const resource = "brands";

export default {
  getBrandDocumentsForCurrentGuest(brandId) {
    logInfo("getBrandDocumentsForCurrentGuest");
    return API.get("checkin", `/${resource}/${brandId}/documents`, {
      queryStringParameters: {
        active: 1
      }
    });
  },

  validatePhone(body) {
    logInfo("validatePhone", { body });
    return API.post("checkin", "/phones/check", {
      body: body
    });
  },

  sendPhoneCode(id, code) {
    logInfo("sendPhoneCode", { id, code });
    return API.put("checkin", `/phones/checked/${id}`, {
      body: { code }
    });
  },

  getEmailConfiguration(brandId) {
    logInfo("getEmailConfiguration");
    return API.get("checkin", `/brands/${brandId}/emails`);
  },

  createDocuments(brandId, body) {
    logInfo("createDocuments");
    return API.post("checkin", `/brands/${brandId}/documents`, {
      body: body
    });
  },

  sendDocuments(body) {
    return API.post("checkin", "/documents/send", {
      body: body
    });
  },

  getGuestDocument(documentId, lang, documentVariablesValues) {
    return API.get("checkin", `/guest-documents/${documentId}`, {
      queryStringParameters: {
        lang,
        ...documentVariablesValues
      }
    });
  },

  async getCustomizedText(brandId, textType, lang, active = true) {
    return API.get("checkin", `/brands/${brandId}/texts`, {
      queryStringParameters: {
        type_id: textType,
        lang,
        active
      }
    });
  }
};
