import { API } from "@aws-amplify/api";
export default {
  async getBrandIntegrations(brandId) {
    const response = await API.get(
      "payment",
      `/brands/${brandId}/integrations`,
      {}
    );
    return response.data;
  },

  async generatePaymentOrder(
    brandId,
    integrationId,
    amount,
    charges,
    additional
  ) {
    return await API.post(
      "payment",
      `/brands/${brandId}/integrations/${integrationId}/orders`,
      {
        body: {
          amount,
          charges: JSON.stringify(charges),
          additional: JSON.stringify(additional)
        }
      }
    );
  },

  async getOrder(brandId, integrationId, orderId) {
    return API.get(
      "payment",
      `/brands/${brandId}/integrations/${integrationId}/orders/${orderId}`
    );
  }
};
