<template>
  <div
    :style="cssVariables"
    class="status degrade rounded border border-gray-200 flex flex-col p-4"
  >
    <div class="flex w-full text-sm" v-if="!uploadedDocument">
      <identificationCard class="dni" />
      <span>{{ identificationCardTranslation}} </span>
    </div>
    <div class="flex sm:flex-1  w-full sm:w-auto" v-else>
      <div class="flex flex-col w-16 md:w-20 items-center">
        <img
          v-bind:src="imageLink"
          data-test="showDocumentModal"
          @click="handleShowDocumentEvent"
          class="cursor-pointer"
        />
        <span data-test="scannedPart" class="text-xs text-gray-400 mt-2">{{
          scannedPartLiteral
        }}</span>
      </div>
      <div class="flex flex-col flex-1 px-3">
        <div class="flex justify-between items-center text-xs text-gray-400">
          <span>{{ $t("documentUploader.scannedDocument") }}</span>
          <div
            class="completed flex justify-between items-center text-green-400 text-xs"
          >
            <span class="mr-1">{{ $t("documentUploader.completed") }}</span>
            <completed />
          </div>
        </div>

        <span class="text-xs font-semibold uppercase mb-2"
          >{{ documentTypeLiteral }}
        </span>
        <span class="text-xs"
          >{{ scannedPartInfoLiteral }}
          <!--TODO: when the Dominican Republic driver's license feature is developed, the following logic will have to be modified, since the driver's license may have a back part. In that case, we will also have to ask for it -->
          <span
            class="font-bold"
            v-if="missingBackPart"
            >{{ $t("documentUploader.missingBackPart") }}</span
          >
          <!-- TODO: when the Dominican Republic driver's license feature is developed, the following logic will have to be modified, since the driver's license may have a back part. In that case, it will have to be previously scanned -->
          <span
            class="font-bold"
            v-if="signatureRequired"
            >{{ $t("documentUploader.signatureRequired") }}</span
          >
          <span class="font-bold" v-if="showAllRequiredDocumentsLiteral"
            >{{ $t("documentUploader.allRequiredInformation") }}
          </span>
        </span>
      </div>
    </div>

    <div class="button-wrapper flex flex-col m-auto w-11/12 p-4">
      <btn @click.native="handleClickEvent" data-test="uploadDocument">{{
        $t(
          `${
            uploadedDocument ? "documentUploader.scanAgain" : scanButtonLiteral
          }`
        )
      }}</btn>
      <btn
        color="red"
        data-test="deleteDocument"
        v-if="uploadedDocument"
        @click.native="handleDeleteDocumentEvent"
        >{{ $t("documentUploader.delete") }}</btn
      >
    </div>
  </div>
</template>
<script>
import identificationCard from "@/assets/images/icons/identification-card.svg";
import completed from "@/assets/images/icons/completed.svg";
import cssVariables from "@/mixins/cssVariables";
import btn from "@/components/shared/button.component";
import { mapState } from "vuex";
export default {
  name: "documentUploader",
  components: { identificationCard, completed, btn },
  props: {
    type: {
      type: String,
      default: "front"
    },
    uploadedDocument: {
      type: Object,
      default: () => {}
    },
    allRequiredDocuments: {
      type: Boolean,
      default: false
    },
    scannedWithSignature:{
      type: Boolean,
    },
    backPartScanned:{
      type: Boolean,
    },
  },
  mixins: [cssVariables],
  computed: {
    imageLink() {
      return `data:image/jpeg;base64,${this.uploadedDocument?.content}`;
    },
    showAllRequiredDocumentsLiteral() {
      return (
      this.allRequiredDocuments &&
      (this.uploadedDocument?.title == "passport" ||
      this.uploadedDocument?.title == "identity_card_back" ||
      this.uploadedDocument?.title == "driving_license" ||
      this.uploadedDocument?.title == "residence_permit_back" ||
      this.uploadedDocument?.title == "signature")
      ) 
    },
    showUploadBackPartLiteral() {
      return ['identity_card_front', 'residence_permit_front'].includes(this.uploadedDocument?.title)
    },
    missingBackPart() {
      const signatureRequiredCase = this.config.identity_document_signature_required && 
              (this.uploadedDocument?.title == 'identity_card_front' || this.uploadedDocument?.title == 'residence_permit_front')  &&
              !this.backPartScanned

      const noSignatureRequiredCase = !this.config.identity_document_signature_required &&
              (this.uploadedDocument?.title === 'identity_card_front'|| this.uploadedDocument?.title == 'residence_permit_front') &&
              !this.allRequiredDocuments
      
      return (signatureRequiredCase || noSignatureRequiredCase);
    },
    signatureRequired(){
      const bothSidesDocuments = (this.uploadedDocument?.title === 'identity_card_front' || this.uploadedDocument?.title === 'residence_permit_front') && this.backPartScanned && !this.scannedWithSignature
      const oneSideDocuments = (this.uploadedDocument?.title === 'passport' || this.uploadedDocument?.title === 'driving_license') && !this.scannedWithSignature

      return (this.config.identity_document_signature_required && (bothSidesDocuments || oneSideDocuments));
    },
    scannedPartLiteral() {
      const parts = {
        identity_card_front: this.$t("documentUploader.front"),
        identity_card_back: this.$t("documentUploader.back"),
        passport: this.$t("documentUploader.front"),
        driving_license: this.$t("documentUploader.front"),
        residence_permit_front: this.$t("documentUploader.front"),
        residence_permit_back: this.$t("documentUploader.back"),

      };
      return parts[this.uploadedDocument?.title] || "";
    },
    documentTypeLiteral() {
      const types = {
        identity_card_front: this.$t("documentUploader.identityDocument"),
        identity_card_back: this.$t("documentUploader.identityDocument"),
        passport: this.$t("documentUploader.passport"),
        driving_license: this.$t("documentUploader.drivingLicense"),
        residence_permit_front: this.$t("documentUploader.residencePermit"),
        residence_permit_back: this.$t("documentUploader.residencePermit"),
        signature: this.$t("documentUploader.signature")
      };

      return (
        types[this.uploadedDocument?.title] || types["identity_card_front"]
      );
    },
    
    scanButtonLiteral() {
      const types = {
        front: this.$t("documentUploader.frontPartButton"),
        back: this.$t("documentUploader.backPartButton"),
        signature: this.$t("documentUploader.signatureButton")
      };

      return types[this.type] || types["front"];
    },
    scannedPartInfoLiteral() {
      const types = {
        front: this.$t("documentUploader.frontPartScanned"),
        back: this.$t("documentUploader.backPartScanned"),
        signature: this.$t("documentUploader.signaturePartScanned"),
      };
      return types[this.type] || types["front"];
    },
    ...mapState("brand", ["config"]),
    identificationCardTranslation() {
      if (this.config.allow_driving_license) {
        return this.$t(`documentUploader.${this.type}PartWithDrivingLicense`);
      } else {
        return this.$t(`documentUploader.${this.type}Part`);
      }}, 
  },
  methods: {
    async handleClickEvent() {
      return this.$emit("handleClickEvent", { side: this.type });
    },
    async handleShowDocumentEvent() {
      return this.$emit("handleShowDocumentEvent", { image: this.imageLink });
    },
    async handleDeleteDocumentEvent() {
      return this.$emit("handleDeleteDocumentEvent", {
        documentType: this.uploadedDocument?.title
      });
    }
  }
};

</script>
<style lang="scss" scoped>
.button-wrapper {
  & > * {
    @apply text-sm py-2 px-6 w-full max-w-md m-auto;
  }
  & > *:not(:last-child) {
    @apply mb-3;
  }
}
.completed svg g {
  @apply stroke-current;
}

.main-color-link {
  color: var(--bgColor);
}
.dni {
  width: 40px;
  min-width: 40px;
}
.disabled {
  cursor: default;
  opacity: 0.4;
}
</style>
