<template>
  <div 
    id="scroll-button"
    v-show="showScrollButton"
    class="fixed bottom-32 right-1 flex flex-col items-end py-2 px-3 transform rotate-90 origin-bottom rounded-full pointer-events-none"
    :style="{ backgroundColor: brandColor}"
  >
    <div class="transform rotate-180 text-white flex" >
      <span>
        {{ $t("privacy.scrollButton") }}
      </span>
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 transform rotate-90">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l7.5-7.5 7.5 7.5m-15 6l7.5-7.5 7.5 7.5" />
      </svg>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from "vuex";
export default {
  name: "scrollButton",
  data() {
    return {
      brandColor: "",
      isAcceptButtonShowing: false,
    }
  },
  props: {
    showGDPRText: {
      type: Boolean,
      default: false
    },
    GDPRTextLoaded:{
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.brandColor = this.mainColor
    window.addEventListener('scroll', this.checkButtonVisibility);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.checkButtonVisibility);
  },
  computed: {
    ...mapState("header", ["fixed"]),
    ...mapGetters("brand", ["mainColor"]),
    showScrollButton() {
      const isHeaderShowing = this.fixed;
      const isAcceptButtonShowing = this.isAcceptButtonShowing;
      const isScreenScrollable = document.body.scrollHeight != window.innerHeight;
      
      return this.GDPRTextLoaded 
      ? (isScreenScrollable && !isHeaderShowing && !isAcceptButtonShowing) 
       || (this.showGDPRText && !isHeaderShowing ) 
      : false;
    }
  },
  methods:{
    checkButtonVisibility() {
      const acceptButton = document.getElementById('acceptButton');
      if (acceptButton) {
        const rect = acceptButton.getBoundingClientRect();
        const isVisible = (
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
        this.isAcceptButtonShowing = isVisible
      }
    },
  },
}
</script>