<template>
  <div
    :style="cssVariables"
    class="document-button degrade rounded border border-gray-200 p-4 flex flex-row relative items-center cursor-pointer relative"
    :class="{
      'border-green-400 cursor-default': roomCompleted
    }"
  >
    <bed class="bed-icon" />
    <span class="text-sm uppercase font-black main-color-link ml-4">
      {{ $t(`room.${roomType}`) }}
    </span>
    <div
      v-show="roomCompleted"
      class="completed bg-green-400 flex flex items-center justify-center absolute h-full p-2"
    >
      <completed />
    </div>
  </div>
</template>
<script>
import completed from "@/assets/images/icons/completed.svg";
import bed from "@/assets/images/icons/bed.svg";
import cssVariables from "@/mixins/cssVariables";
export default {
  name: "room",
  mixins: [cssVariables],
  components: { completed, bed },
  props: {
    roomType: {
      type: String,
      required: true
    },
    roomCompleted: {
      type: Boolean,
      required: true
    }
  }
};
</script>
<style lang="scss" scoped>
.completed {
  right: 0;
  bottom: 0;
}
.bed-icon {
  width: 40px;
  min-width: 40px;
}
.main-color-link {
  color: var(--bgColor);
}
</style>
