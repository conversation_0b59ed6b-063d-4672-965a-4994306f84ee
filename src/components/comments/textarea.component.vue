<template>
  <div class="flex flex-col">
    <textarea
      class=" rounded rounded-bl-none rounded-tl-none border-2 border-gray-200 focus:outline-none focus:border-green-400 flex-auto p-2"
      id="comments"
      :placeholder="placeholder"
      v-model="inputValue"
      rows="6"
      :maxlength="isMaxLengthActive"
      @input="inputChanged"
    >
    </textarea>
    <span
      class="text-xs text-right pt-2"
      v-if="maxChars"
      data-test="maxLengthProgress"
    >
      {{ textLength }}
    </span>
  </div>
</template>
<script>
export default {
  name: "textAreaComponent",

  data() {
    return {
      inputValue: "",
      hasError: true,
      length: 0
    };
  },
  computed: {
    textLength() {
      return `${this.length}/${this.maxChars}`;
    },
    isMaxLengthActive() {
      return this.maxChars || null;
    }
  },
  props: {
    name: {
      type: String,
      required: true
    },
    text: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    placeholder: {
      type: String
    },
    maxChars: {
      type: Number,
      default: 0
    }
  },
  methods: {
    inputChanged() {
      this.length = this.inputValue?.length;
      this.$emit("inputChanged", {
        value: this.inputValue,
        type: this.type
      });
    }
  }
};
</script>
