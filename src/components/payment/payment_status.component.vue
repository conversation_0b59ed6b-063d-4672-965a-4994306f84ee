<template>
  <div
    class="degrade rounded border border-gray-200 status text-left w-full p-4 flex flex-row"
  >
    <x v-if="!isPaid" class="status-icon mt-1.5" />
    <completed v-if="isPaid" class="status-icon mt-1.5" />
    <div class="status-text">
      <p class="font-bold uppercase">{{ status }}</p>
      <p v-show="!isPaid" class="font-bold uppercase">{{ error }}</p>
      <p v-show="!isPaid" class="mt-2 mb-4 text-xs">
        {{ $t(`${errorLocale}.${error}`) }}
      </p>
      <p class="uppercase text-xs mt-2">Reference:</p>
      <p class="font-bold uppercase text-xs">{{ orderId }}</p>
    </div>
  </div>
</template>
<script>
import x from "@/assets/images/icons/x.svg";
import completed from "@/assets/images/icons/completed.svg";
import { mapState } from "vuex";
export default {
  name: "paymentStatus",
  components: {
    x,
    completed
  },
  computed: {
    ...mapState("payment", ["platformMethodAndPaymentData"]),
    errorLocale() {
      return `${this.platformMethodAndPaymentData.platform}${this.platformMethodAndPaymentData.method}errors`;
    }
  },
  props: {
    isPaid: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      required: true
    },
    error: {
      type: String,
      required: true
    },
    orderId: {
      type: String
    }
  }
};
</script>
<style lang="scss" scoped>
.status-icon {
  display: block;
  width: 30px;
  max-width: 30px;
  min-width: 30px;
  g {
    stroke: gray;
  }
}
</style>
