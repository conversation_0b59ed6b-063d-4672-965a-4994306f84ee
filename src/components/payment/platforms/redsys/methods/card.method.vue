<template>
  <div class="flex items-center justify-center h-full">
    <div ref="form" v-html="form" />
  </div>
</template>
<script>
import { mapState } from "vuex";
import repositoryFactory from "../../../../../repository/repositoryFactory";
import curatePaymentData from "../../../../../services/payment/curatePaymentData";
import stopLoading from "../../../../../mixins/stopLoading";

const payment = repositoryFactory.get("payment");
export default {
  name: "redsys-card-method",
  data() {
    return {
      form: ""
    };
  },
  mixins: [stopLoading],
  computed: {
    ...mapState("brand", ["brandId", "name", "mainColor"]),
    ...mapState("payment", [
      "integrations",
      "platformMethodAndPaymentData",
      "charges"
    ])
  },
  created() {
    let paymentData = curatePaymentData(this.charges);
    payment
      .generatePaymentOrder(
        this.brandId,
        this.integrations[0].id,
        this.platformMethodAndPaymentData.amount,
        paymentData,
        {
          lang: this.$i18n.locale,
          currency: this.charges?.currency,
          isMock: process.env.VUE_APP_BRANDS_DEMO?.split(",")?.includes(
            this.brandId
          )
        }
      )
      .then(async response => {
        this.form = response.data.form;
        //Store order to get information later
        await this.$store.dispatch("payment/SET_ORDER", response.data);
        setTimeout(() => {
          document.getElementById("redsys_form").submit();
        }, 500);
      })
      .catch(error => {
        console.error(error);
      });
  }
};
</script>
