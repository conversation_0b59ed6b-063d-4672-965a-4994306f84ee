<template>
  <div class="flex items-center justify-center h-full"></div>
</template>
<script>
import { mapState } from "vuex";
import repositoryFactory from "../../../../../repository/repositoryFactory";
import curatePaymentData from "../../../../../services/payment/curatePaymentData";
const payment = repositoryFactory.get("payment");
export default {
  name: "paynopain-card-method",
  data() {
    return {
      url: false
    };
  },
  computed: {
    ...mapState("brand", ["brandId", "name", "mainColor"]),
    ...mapState("payment", [
      "integrations",
      "platformMethodAndPaymentData",
      "charges"
    ])
  },
  async created() {
    //get only paid reservations ids
    let paymentData = curatePaymentData(this.charges);

    payment
      .generatePaymentOrder(
        this.brandId,
        this.integrations[0].id,
        this.platformMethodAndPaymentData.amount,
        paymentData,
        {
          isMock: process.env.VUE_APP_BRANDS_DEMO?.split(",")?.includes(
            this.brandId
          ),
          brandId: this.brandId,
          hotelName: this.name,
          mainColor: this.mainColor,
          backLink: `${process.env.VUE_APP_URL}/${this.brandId}/payment`,
          texts: {
            header: this.$t("paynopaincardmethod.header"),
            title: this.$t("paynopaincardmethod.title"),
            cardHolder: this.$t("paynopaincardmethod.cardHolder"),
            cardNumber: this.$t("paynopaincardmethod.cardNumber"),
            expiration: this.$t("paynopaincardmethod.expiration"),
            cvv: this.$t("paynopaincardmethod.cvv"),
            backLinkText: this.$t("paynopaincardmethod.backLinkText"),
            security: this.$t("paynopaincardmethod.security")
          }
        }
      )
      .then(async response => {
        //Store order to get information later
        await this.$store.dispatch("payment/SET_ORDER", response.data);
        //redirects to payment form
        window.location.href = `${process.env.VUE_APP_PAYNOPAIN_PAYMENT_URL}${response.data.token}?lang=${this.$i18n.locale}`;
      })
      .catch(error => {
        console.error(error);
      });
  }
};
</script>
