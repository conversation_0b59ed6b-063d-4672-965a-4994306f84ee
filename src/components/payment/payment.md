---
title: Payments
layout: layout.html
orderPath: /__payments
eleventyNavigation:
  key: Payments
  order: 1
---


# Payments

## User experience flow diagram

Simple user flow diagram

```mermaid
stateDiagram-v2
state routeState <<choice>>
state paymentState <<choice>>
[*] --> PaymentPage
PaymentPage --> routeState
    routeState --> PaymentProform: Payment selected
    routeState --> [*]: If no payments selected, or error
PaymentProform --> PaymentPlatform: User clicks on "pay" button
PaymentPlatform --> paymentState
    paymentState --> PaymentResult: User filled card form
    paymentState --> PaymentPage: User clicked on go back
PaymentResult --> [*]: End of payment process
```


## Tecnical diagram

Explanation between Autocheckin and Payment Platform


```mermaid
sequenceDiagram
    participant Autocheckin
    participant Payment Platform
    participant Payment Lambda
    Autocheckin->>Payment Platform: User clicks on "pay" button
    loop Payment Platform
        Payment Platform->>Payment Platform: User fills card form
    end
    Payment Platform->>Autocheckin: Sends an "OK" or "KO"
    Payment Platform->>Payment Lambda: Send payment info
    Note left of Autocheckin: Continue autocheckin flow
    Note right of Payment Lambda: Process payment
```
