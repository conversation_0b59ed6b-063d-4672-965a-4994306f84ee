<template>
  <div class="proform">
    <div
      class="proform-header flex items-center bg-gray-50 border border-gray-200 rounded-t p-4 font-black justify-between"
    >
      <div class="uppercase text-xs w-2/6 lg:w-1/6">
        {{ $t("proformcomponent.quantity") }}
      </div>
      <div class="uppercase text-xs flex-1">
        {{ $t("proformcomponent.description") }}
      </div>
      <div class="uppercase text-xs w-2/6 text-right">
        {{ $t("proformcomponent.amount") }}
      </div>
    </div>
    <div
      class="proform-body border-r border-l border-gray-200 text-xs w-100 py-4"
    >
      <div
        v-for="(reservationInvoice, index) in allInvoices"
        :key="index"
        class="invoice"
        data-test="product-item"
      >
        <div
          v-for="(product, index) in reservationInvoice.products"
          :key="index"
          class="product flex flex-row py-2 px-4"
        >
          <div class="uppercase text-xs w-2/6 lg:w-1/6">
            {{ product.quantity }}
          </div>
          <div class="uppercase text-xs flex-1">
            {{ product.description }}
          </div>
          <div class="uppercase text-xs w-2/6 text-right font-black">
            {{ product.total }} {{ currency }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="proform-footer bg-gray-50 border border-gray-200 rounded-b p-4 uppercase"
    >
      <p class="text-right w-full text-xs mb-1" data-test="total">
        <span class="mr-2"> {{ $t("proformcomponent.total") }}</span>
        {{ total }}
        {{ currency }}
      </p>
      <p class="text-right w-full text-xs" data-test="paid">
        <span class="mr-2">{{ $t("proformcomponent.paid") }}</span>
        {{ received }}
        {{ currency }}
      </p>
      <p class="font-black text-right w-full text-sm mt-4" data-test="remain">
        <span class="mr-2">{{ $t("proformcomponent.remain") }}</span>
        <span>{{ remain }} {{ currency }}</span
        ><br />
        <small class="text-xs font-light lowercase">{{
          $t("proformcomponent.taxes")
        }}</small>
      </p>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";

export default {
  name: "proform",
  props: {
    reservations: {
      type: Array,
      required: true
    },
    currency: {
      type: String
    }
  },
  computed: {
    ...mapState("payment", ["charges"]),
    allInvoices() {
      let invoices = [];
      this.reservations.forEach(reservation => {
        this.charges.reservations.forEach(pendingReservation => {
          if (pendingReservation.id === reservation.id) {
            pendingReservation.invoices.forEach(invoice => {
              invoices.push(invoice);
            });
          }
        });
      });
      return invoices;
    },
    total() {
      let total = 0;
      this.allInvoices.forEach(invoice => {
        total += invoice.total;
      });
      return total;
    },
    received() {
      let total = 0;
      this.allInvoices.forEach(invoice => {
        total += invoice.received;
      });
      return total;
    },
    remain() {
      let total = 0;
      this.allInvoices.forEach(invoice => {
        total += Number(parseFloat(invoice.remain).toFixed(2));
      });
      this.$emit("remainCalculated", total);
      return total;
    }
  }
};
</script>
