<template>
  <div
    v-if="amount > 0"
    :style="cssVariables"
    :class="{
      'payment-selected': reservation.selected
    }"
    class="payment-option focus:outline-none status degrade rounded border border-gray-200 p-4 flex flex-row relative w-full"
    @click="select"
  >
    <div
      v-show="reservation.selected"
      class="completed flex items-center justify-center absolute h-full p-2"
    >
      <completed />
    </div>
    <bed />
    <div
      class="payment-option-details uppercase text-xs font-black text-gray-400 ml-4 flex-grow text-left"
    >
      <ul>
        <li class="mb-2" v-show="selectedReservation">
          <span class="text-red-500 font-black">{{
            $t("paymentoptioncomponent.thisReservation")
          }}</span>
        </li>
        <li>
          <span class="mr-2">{{ $t("paymentoptioncomponent.nights") }}</span>
          <span class="font-black text-black"> {{ reservationDetails && reservationDetails.res_nights ? reservationDetails.res_nights : "" }}
          </span>
        </li>
        <li>
          <span class="mr-2">{{ $t("paymentoptioncomponent.room") }}</span>
          <span class="font-black text-black">{{
            reservationDetails.res_room_type
          }}</span>
        </li>
        <li>
          <span class="mr-2">{{ $t("paymentoptioncomponent.adults") }}</span>
          <span class="font-black text-black">{{
            reservationDetails.res_adults
          }}</span>
        </li>
        <li v-if="reservationDetails.res_children">
          <span class="mr-2">{{ $t("paymentoptioncomponent.kids") }}</span>
          <span class="font-black text-black">{{
            reservationDetails.res_children
          }}</span>
        </li>
      </ul>
      <div class="uppercase text-sm font-black mt-2">
        <span class="mr-2">{{ $t("paymentoptioncomponent.total") }}</span>
        <span class="text-black">{{ amount }} {{ getCurrency }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import cssVariables from "@/mixins/cssVariables";
import { mapState } from "vuex";
import bed from "@/assets/images/icons/bed.svg";
import completed from "@/assets/images/icons/completed.svg";
export default {
  name: "paymentOption",
  mixins: [cssVariables],
  components: { bed, completed },
  props: {
    reservation: {
      type: Object
    },
    currency: {
      type: [String, Boolean]
    }
  },
  computed: {
    ...mapState("reservations", ["data", "reservationSelected"]),
    reservationDetails() {
      return this.data.find(reservation => {
        return reservation.res_id === this.reservation.id;
      });
    },
    selectedReservation() {
      return this.reservationSelected.res_id === this.reservation.id;
    },
    amount() {
      let amount = 0;
      this.reservation.invoices.forEach(invoice => {
        amount += invoice.amount;
      });
      return amount;
    },
    getCurrency() {
      return this.currency || null;
    }
  },
  methods: {
    select() {
      this.$emit("selected", this.reservation);
    }
  }
};
</script>
<style scoped lang="scss">
.payment-selected {
  border-color: var(--bgColor);
  @apply border;
}

.completed {
  right: 0;
  bottom: 0;
  background-color: var(--bgColor);
}

.payment-option {
  cursor: pointer;
}
</style>
