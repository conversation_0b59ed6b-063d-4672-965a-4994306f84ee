<template>
  <div
    :style="cssVariables"
    :class="{
      'payment-selected': localizer.selected
    }"
    class="py-6 payment-option focus:outline-none status degrade rounded border border-gray-200 p-4 flex flex-row relative w-full items-center"
    @click="select"
  >
    <div
      v-show="localizer.selected"
      class="completed flex items-center justify-center absolute h-full p-2"
    >
      <completed />
    </div>
    <hotel />
    <div class="uppercase text-sm font-black ml-5">
      <span class="mr-2 text-gray-400">{{
        $t("paymentoptioncomponent.total")
      }}</span>
      <span class="text-black">{{ localizer.amount }} {{ currency }}</span>
    </div>
  </div>
</template>
<script>
import hotel from "@/assets/images/icons/hotel.svg";
import completed from "@/assets/images/icons/completed.svg";
import cssVariables from "@/mixins/cssVariables";

export default {
  name: "localizerOption",
  components: { hotel, completed },
  mixins: [cssVariables],
  props: {
    localizer: {
      type: Object
    },
    currency: {
      type: [String, Boolean],
      default: () => null
    }
  },
  methods: {
    select() {
      this.$emit("selected");
    }
  }
};
</script>
<style scoped lang="scss">
.payment-selected {
  border-color: var(--bgColor);
  @apply border;
}

.completed {
  right: 0;
  bottom: 0;
  background-color: var(--bgColor);
}

.payment-option {
  cursor: pointer;
}
</style>
