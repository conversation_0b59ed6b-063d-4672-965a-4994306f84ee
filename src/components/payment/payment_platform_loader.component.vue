<template>
  <div class="method-wrapper flex-grow h-full w-full">
    <component v-bind:is="currentMethod"></component>
  </div>
</template>
<script>
import paynopainCard from "./platforms/paynopain/methods/card.method";
import redsysCard from "./platforms/redsys/methods/card.method";
import { mapState } from "vuex";

export default {
  name: "payment-platform-loader",
  components: { paynopainCard, redsysCard },
  computed: {
    ...mapState("payment", ["platformMethodAndPaymentData"]),
    currentMethod: function() {
      let current = undefined;
      switch (this.platformMethodAndPaymentData.platform) {
        case "paynopain":
          if (this.platformMethodAndPaymentData.method === "card")
            current = "paynopainCard";
          break;
        case "redsys":
          if (this.platformMethodAndPaymentData.method === "card")
            current = "redsysCard";
          break;
      }
      return current;
    }
  }
};
</script>
