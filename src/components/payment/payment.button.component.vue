<template>
  <button
    v-show="buttonText !== 'not defined'"
    @click="$emit('methodSelected', method)"
    type="button"
    :style="cssVariables"
    :class="
      `btn text-white font-bold py-3 px-5 rounded w-full shadow-lg focus:outline-none uppercase bg-${color}-600 hover:bg-${color}-400`
    "
  >
    {{ buttonText }}
  </button>
</template>
<script>
import cssVariables from "@/mixins/cssVariables";
export default {
  name: "paymentButton",
  mixins: [cssVariables],
  props: {
    color: {
      type: String,
      default: null
    },
    method: {
      type: Object,
      required: true
    }
  },
  computed: {
    buttonText() {
      if (this.method.method)
        return this.$t(`paymentbuttoncomponent.${this.method.method}`);
      return "not defined";
    }
  }
};
</script>
<style lang="scss">
.btn {
  background-color: var(--bgColor);
  &:hover {
    background-color: var(--darkenBgColor);
  }
  &:disabled {
    opacity: 0.2;
    cursor: default;
    &:hover {
      background-color: var(--bgColor);
    }
  }
}
</style>
