<template>
  <button
    @click="$emit('selected', { name: 'NoPaymentOption' })"
    :style="cssVariables"
    :class="{
      'payment-selected': selected === true
    }"
    class="focus:outline-none status degrade rounded border border-gray-200 p-4 flex flex-row relative items-center w-full"
  >
    <div
      v-show="selected"
      class="completed flex items-center justify-center absolute h-full p-2"
    >
      <completed />
    </div>
    <x />
    <span class="uppercase text-xs font-black ml-4 flex-grow text-left"
      >{{ $t("nopaymentoptioncomponent.first") }} <br />
      <small class="text-gray-400">{{
        $t("nopaymentoptioncomponent.second")
      }}</small>
    </span>
  </button>
</template>
<script>
import cssVariables from "@/mixins/cssVariables";
import x from "@/assets/images/icons/x-gray.svg";
import completed from "@/assets/images/icons/completed.svg";
export default {
  name: "NoPaymentOption",
  mixins: [cssVariables],
  components: { x, completed },
  props: {
    selected: {
      type: Boolean,
      default: false
    }
  }
};
</script>
<style scoped lang="scss">
.payment-selected {
  border-color: var(--bgColor);
  @apply border;
}
.completed {
  right: 0;
  bottom: 0;
  background-color: var(--bgColor);
}
</style>
