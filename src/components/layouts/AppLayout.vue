<template>
  <div
    id="app"
    class="h-full w-full bg-black flex flex-col text-gray-700"
    :style="styles"
  >
  <demoHeader/>
    <div class="bg-color" />
    <loader />

    <checkin-header :logo="$store.state.brand.logo" />
    <main class="main-content main-content-white" :class="{'relative overflow-x-hidden': this.$route.name === 'AdvancedScan' }">
      <subheader />
      <router-view />
    </main>
    <Footer v-if="showFooter" />
    <AppButtons v-if="showAppButtons" />
  </div>
</template>
<script>
import checkinHeader from "@/components/shared/checkinHeader.component";
import loader from "@/components/shared/loading.component";
import startLoading from "@/mixins/startLoading";
import convertToRgb from "@/mixins/convertToRgb";
import Footer from "@/components/shared/footer.component";
import AppButtons from "@/components/shared/appButtons.component";
import subheader from "@/components/shared/subheader.component";
import demoHeader from "@/components/shared/demoHeader.component";

export default {
  data() {
    return {
      showFooter: false,
      showAppButtons: false
    };
  },
  created() {  
    this.showFooter = this.$route.meta?.step?.() >= 1;
    this.showAppButtons = this.$route.meta?.step?.() >= 2 
  },
  watch: {
    $route: function(value) {
      this.showFooter = value.meta?.step?.() >= 1;
      this.showAppButtons = value.meta?.step?.() >= 2
    }
  },
  components: {
    checkinHeader,
    loader,
    Footer,
    subheader,
    AppButtons,
    demoHeader
  },
  mixins: [convertToRgb, startLoading],
  computed: {
    styles() {
      let backgroundImage = "backgroundImage_sm";
      if (window.innerWidth >= 960) backgroundImage = "backgroundImage_md";
      if (window.innerWidth >= 1200) backgroundImage = "backgroundImage_lg";

      return {
        "background-image":
          "url(" + this.$store.state.brand[backgroundImage] + ")",
        "background-size": "cover",
        "background-position": "center",
        "background-color": `${this.hexToRgb(
          this.$store.state.brand.mainColor
        )}`,
        "background-blend-mode": "soft-light",
        "--bgColor": this.$store.state.brand.mainColor
      };
    }
  }
};
</script>
