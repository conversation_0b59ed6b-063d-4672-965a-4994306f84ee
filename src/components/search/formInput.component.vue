<template>
  <component
    ref="component"
    v-if="active"
    @inputChanged="inputChanged($event)"
    @showModalInfo="showModalInfo($event)"
    @autocompleteSelect="autocompleteSelect($event)"
    :is="input"
    :inputError="inputError"
    :hasSimilarGuestError="hasSimilarGuestError"
    v-bind="componentProps"
  />
</template>
<script>
import defaultInput from "./inputs/defaultInput.component";
import selectInput from "./inputs/selectInput.component";
import dateInput from "./inputs/dateInput.component";
import numberInput from "./inputs/numberInput.component";
import timeInput from "./inputs/timeInput.component.vue";
import autocompleteInput from "./inputs/autocompleteInput.component.vue";
import emailInput from "./inputs/emailInput.component.vue";
import phoneInput from "./inputs/phoneInput.component.vue";
import checkboxInput from "./inputs/checkboxInput.component.vue";

export default {
  name: "inputField",
  components: {
    defaultInput,
    selectInput,
    dateInput,
    numberInput,
    timeInput,
    autocompleteInput,
    emailInput,
    phoneInput,
    checkboxInput,
  },
  data() {
    return {
      hasError: false,
    };
  },
  props: {
    active: {
      type: Boolean,
      default: true,
    },
    name: {
      type: String,
      required: true,
      default: "",
    },

    infoModal: {
      type: Object,
      default() {
        return {};
      },
    },
    inputName: {
      type: String,
      default: "",
    },
    inputError: {
      type: Object,
      default() {
        return {
          error: false,
          input: this.inputName,
          label: "",
        };
      },
    },
    type: {
      type: String,
      default: "text",
    },
    options: {
      type: Array,
      default: () => [],
    },
    icon: {
      type: String,
      default: "",
    },
    value: {
      type: null,
    },
    placeholder: {
      type: String,
    },
    minLength: {
      type: String,
      default: "0",
    },
    maxLength: {
      type: String,
      default: "255",
    },
    optional: {
      type: Boolean,
      default: false,
    },
    checkOnStart: {
      type: Boolean,
      default: false,
    },
    disableFutureDates: {
      type: Boolean,
      default: false,
    },
    countryCode: {
      type: String,
      default: "",
    },
    documentType: {
      type: String,
      default: "",
    },
    documentNationality: {
      type: String,
      default: "",
    },
    allowsFreeText: {
      type: Boolean,
      default: false,
    },
    debounce: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: "",
    },
    browserAutocomplete: {
      type: Boolean,
      default: false,
    },
    autocompleteData: {
      type: Object,
      default: null,
    },
    hasSimilarGuestError: {
      type: Boolean,
      default: null,
    },
  },
  computed: {
    input() {
      const inputList = {
        select: "selectInput",
        date: "dateInput",
        time: "timeInput",
        autocomplete: "autocompleteInput",
        email: "emailInput",
        phone: "phoneInput",
        number: "numberInput",
        checkbox: "checkboxInput",
      };
      return inputList[this.type] || "defaultInput";
    },
    componentProps() {
      let props = {};
      props.active = this.active;
      props.icon = this.icon;
      props.name = this.name;
      props.infoModal = this.infoModal;
      props.inputName = this.inputName;
      props.value = this.value;
      props.placeholder = this.placeholder;
      props.minLength = this.minLength;
      props.maxLength = this.maxLength;
      props.optional = this.optional;
      props.checkOnStart = this.checkOnStart;
      props.disableFutureDates = this.disableFutureDates;
      props.countryCode = this.countryCode;
      props.documentType = this.documentType;
      props.documentNationality = this.documentNationality;
      props.allowsFreeText = this.allowsFreeText;
      props.disabled = this.disabled;
      props.label = this.label;

      this.type === "select" || this.type === "autocomplete"
        ? (props.options = this.options)
        : (props.type = this.type);

      props.debounce = this.debounce;
      props.browserAutocomplete = this.browserAutocomplete;
      props.autocompleteData = this.autocompleteData ?? {};
      return props;
    },
  },
  methods: {
    inputChanged(event) {
      this.$emit("inputChanged", event);
    },
    showModalInfo(event) {
      this.$emit("showModalInfo", event);
    },
    clearData() {
      this.$refs.component.clearData();
    },
    autocompleteSelect(event) {
      this.$emit("autocompleteSelect", event);
    },
    emailValidation(emailValidation) {
      this.$refs.component.emailValidation(emailValidation);
    },
  },
};
</script>
