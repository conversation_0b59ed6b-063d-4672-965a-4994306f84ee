<template>
  <div>
    <modal name="inputInfo">
      {{ modalMessage }}
    </modal>

    <form
     @submit.prevent="submitForm" >
      <form-input
        :ref="input.name"
        @inputChanged="handleInputChanged($event, input)"
        @showModalInfo="showModalInfo($event)"
        v-for="(input, index) in inputs"
        :key="index"
        :class="{ 'mb-6': inputs.length - 1 > index }"
        :active="!input.hasOwnProperty('active') || input.active === 'true'"
        :name="$t(`searchFormComponent.${input.name}.title`)"
        :infoModal="$t(`searchFormComponent.${input.name}.modal`)"
        :icon="input.icon"
        :type="input.type"
        :value="input.value"
        :options="input.select"
        :minLength="input.minLength"
        :maxLength="input.maxLength"
        :inputName="input.name"
        placeholder="..."
        :check-on-start="input.checkOnStart ? true : false"
        :timeLimitCheckin="timeLimitCheckin"
        :closeTimeLimitCheckin="closeTimeLimitCheckin"
        :allowTimeLimit="allowTimeLimit"
      />
    </form>
  </div>
</template>
<script>
import formInput from "@/components/search/newInputs/formInput.component.vue";
import modal from "@/components/shared/modal.component";
import { mapActions } from "vuex";
export default {
  name: "searchForm",
  components: { formInput, modal },
  data() {
    return {
      modalMessage: ""
    };
  },
  props: {
    inputs: {
      type: Array
    },
    formGroup: {
      type: [Number, String]
    },
    timeLimitCheckin: {
      default: null
    },
    closeTimeLimitCheckin: {
      default: null
    },
    allowTimeLimit:{
      type: Boolean,
      default: false
    }
  },
  created(){
    this.inputs.sort((a, b) => a.position - b.position);
  },
  methods: {
    ...mapActions("modal", [
      "VISIBLE",
      "SET_BUTTON_MESSAGE",
      "SET_TYPE",
      "SET_TITLE",
      "SET_NAME"
    ]),
    submitForm(){
      this.$emit('child-form-submitted')
    },
    async handleInputChanged(event, input) {
      input.value = event.value;
      input.error = event.error;
      await this.$emit("handleFormChanged", this.formGroup);
    },
    async showModalInfo($event) {
      let modal = this.$t(`searchFormComponent.${$event}.modal`);
      await this.SET_TYPE("info");
      await this.SET_NAME("inputInfo");
      await this.SET_TITLE(modal.title);
      await this.SET_BUTTON_MESSAGE(modal.button);
      this.modalMessage = modal.message;
      await this.VISIBLE(true);
    }
  }
};
</script>
