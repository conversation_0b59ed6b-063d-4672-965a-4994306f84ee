---
title: Inputs
layout: layout.html
eleventyNavigation:
  key: Inputs
  parent: Components
---


# Inputs

We define common inputs so that they are seen in the same way throughout the application and the same logic is applied.

We currently have all these types of input components:

{% assign navPages = collections.all | eleventyNavigation: "Inputs" %}
{{ navPages | eleventyNavigationToHtml }}

## Usage

Practically we always use one wrapper for all inputs, the formInputComponent. This component is given the type of input along with the other properties and it will paint the corresponding one.

```js
<template>
  <form-input
    :ref="input.name"
    @inputChanged="inputChanged($event, input)"
    @autocompleteSelect="autocompleteSelect($event, input)"
    class="mb-6"
    v-for="input in inputDataList"
    :key="input.index"
    :active="input.active == 'true'"
    :optional="input.required === 'false'"
    :name="$t(`validatedata.${input.name}`)"
    :countryCode="selectedCountry"
    :type="input.type"
    :value="input.value"
    :options="input.options"
    :inputName="input.name"
    :minLength="input.minLength"
    :maxLength="input.maxLength"
    :documentType="documentTypeSelected"
    :documentNationality="nationalitySelected"
    placeholder="..."
    :allowsFreeText="input.allowsFreeText || false"
    :check-on-start="queryParams.manualProcess ? false : true"
    :disableFutureDates="inputsWithDissableFutureDates.includes(input.name)"
    :inputError="inputErrorInfo"
    :debounce="input.name === 'document_number'"
    :disabled="input.disabled"
  />
</template>
<script>
import formInput from "@/components/search/formInput.component";

export default {
  name: "myComponent",
  components: {
    formInput
  },

  ...
```

As can be seen in the example above, inputs are often referenced in the code and painted with an array of inputs objects. This is because they are configured from the back to simplify the process. The following format is used:

```json
[
  {
    active: "true"
    name: "birthday_date"
    position: 4
    required: "true"
    type: "date"
  },
  {
    active: "true",
    maxLength: "40",
    minLength: "2",
    name: "second_surname"
    position: 3
    required: "false"
    type: "text"
  }
]
```

## Props

By grouping different types of inputs, a lot of properties can be passed. In the following list I list those common to all inputs and in the definition of each of them the specific ones will be mentioned.

|     Prop     |                                     Description                                  |  Type   | Default |
|--------------|----------------------------------------------------------------------------------|---------|---------|
| active       | Indicates whether to display the input or not                                    | Boolean |  true   |
| type         | Define the type of input (text/email/date/autocomplete/number/phone/select/time) | String  | "text"  |
| value        | Predefines the value of the input when loading it                                |   Any   |  null   |
| placeholder  | Predefines the placeholder when the input has no value                           | String  |  null   |
| name         | Name of the input, the value will be printed in the input label                  | String  |  null   |
| minLength    | Minimum length of input value                                                    | String  |   "0"   |
| maxLength    | Minimum length of input value                                                    | String  |  "255"  |
| optional     | Indicates whether it is mandatory to fill in the input                           | Boolean |  false  |
| checkOnStart | Indicates whether field validation should be applied when loading the input      | Boolean |  false  |
