<template>
  <label class="block">
    <span class="text-gray-700 font-black uppercase text-sm">{{ name }}</span>
    <span
      v-if="optional"
      class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
      >{{ $t("inputs.optional") }}</span
    >
    <div class="flex mt-1 w-full relative">
      <div
        class="flex-auto px-4 flex items-center justify-center bg-gray-200  rounded-tr-none rounded-br-none"
        :class="{
          'bg-red-400': $v.inputValue.$error,
          'bg-green-400':
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
          'bg-gray-200': isOptionalEmpty
        }"
      >
        <component
          :is="cIcon"
          :class="{
            'white-svg':
              $v.inputValue.$error ||
              (!$v.inputValue.$error &&
                $v.inputValue.$dirty &&
                !isOptionalEmpty)
          }"
        />
      </div>
      <transition name="bounce">
        <component
          :ref="name"
          class="absolute right-6 top-6"
          :is="'check'"
          v-show="
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty
          "
        />
      </transition>
      <input
        type="number"
        :placeholder="placeholder"
        class="rounded rounded-bl-none rounded-tl-none block w-full p-4 border-2 border-gray-200 focus:outline-none"
        :class="{
          'border-red-400': $v.inputValue.$error,
          'border-green-400':
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
          'border-gray-200': isOptionalEmpty
        }"
        @input="inputChanged($event.target.value)"
        v-model="inputValue"
        :data-test="safeName"
      />
    </div>
  </label>
</template>
<script>
import clock from "@/assets/images/icons/clock.svg";
import { requiredIf } from "vuelidate/lib/validators";
import inputIcons from "@/mixins/inputIcons";
import check from "@/assets/images/icons/check.svg";
import createSafeName from "@/mixins/createSafeName";
import pencil from "@/assets/images/icons/pencil.svg";

export default {
  name: "inputField",
  data() {
    return {
      inputValue: "",
      hasError: true
    };
  },

  mixins: [inputIcons, createSafeName],
  components: { clock, check, pencil },

  props: {
    name: {
      type: String,
      required: true
    },
    inputName: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: ""
    },
    value: {
      type: String
    },
    placeholder: {
      type: String
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    },
    countryCode: {
      type: String,
      default: ""
    }
  },
  computed: {
    isOptionalEmpty() {
      return this.optional && !this.inputValue;
    }
  },
  mounted() {
    if (this.value !== "") {
      this.inputValue = this.value;
    }
    if (this.checkOnStart) this.$v.inputValue.$touch();
    this.inputChanged(this.inputValue || "");
  },

  validations() {
    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      })
    };

    return {
      inputValue: validate
    };
  },
  methods: {
    inputChanged(value = "") {
      this.inputValue = value;
      if (value) {
        this.$v.inputValue.$touch();
      }
      this.hasError = this.$v.inputValue.$error;
      this.$emit("inputChanged", {
        value: this.inputValue,
        error: this.hasError,
        type: this.type
      });
    },

    clearData() {
      this.inputValue = "";
      this.$v.inputValue.$reset();
    }
  }
};
</script>
<style lang="scss">
.white-svg > g {
  stroke: white;
}
input[type="number"] {
  -moz-appearance: textfield;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
