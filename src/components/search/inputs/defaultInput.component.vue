<template>
  <div>
    <div>
      <label
        class="block"
        @click.prevent
        :for="browserAutocomplete ? safeName : ''"
      >
        <span class="text-gray-700 font-black uppercase text-sm">{{
          name
        }}</span>
        <span
          v-if="optional"
          class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
          >{{ $t("inputs.optional") }}</span
        >
        <btn
          v-if="Object.keys(infoModal).length != 0"
          @click.native="$emit('showModalInfo', inputName)"
          class="info-button rounded-full float-right"
          data-test="info-button-help"
          >?</btn
        >
      </label>
    </div>
    <div>
      <div
        class="flex bg-gray-200 rounded mt-1 border-2 border-gray-200"
        :class="{
          'border-red-400': $v.inputValue.$error,
          'border-green-400':
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
          'border-gray-200': isOptionalEmpty,
        }"
      >
        <div
          class="flex-auto px-4 flex items-center justify-center bg-gray-200"
          :class="{
            'bg-red-400': $v.inputValue.$error,
            'bg-green-400':
              !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
            'bg-gray-200': isOptionalEmpty,
          }"
        >
          <component
            :is="cIcon"
            :class="{
              'white-svg':
                $v.inputValue.$error ||
                (!$v.inputValue.$error &&
                  $v.inputValue.$dirty &&
                  !isOptionalEmpty),
            }"
          />
        </div>
        <!-- Avoid autocomplete on the real input -->
        <input v-if="!browserAutocomplete" v-show="false" autocomplete="on" />
        <input
          :type="type"
          :placeholder="placeholder"
          :name="autocompleteData.name"
          :autocomplete="
            inputName === 'street_number'
              ? 'off'
              : autocompleteData.autocomplete
          "
          class="block w-full p-4 focus:outline-none rounded-none"
          :class="{ disabled: disabled }"
          @input="manageInputChanged($event.target.value)"
          v-model="inputValue"
          :data-test="safeName"
          :disabled="disabled"
        />
        <div
          class="input-transition flex justify-center items-center px-4 min-w-min"
          :class="disabled ? 'bg-gray-200' : 'bg-white'"
        >
          <transition name="bounce" mode="out-in">
            <component :is="'errorx'" v-show="$v.inputValue.$error" />
          </transition>
          <transition name="bounce" mode="in-out">
            <component
              :is="'check'"
              v-show="
                !$v.inputValue.$error &&
                $v.inputValue.$dirty &&
                !isOptionalEmpty
              "
            />
          </transition>
        </div>
      </div>
      <p class="text-xs font-black mt-1">
        <span
          class="error-message"
          v-if="$v.inputValue.$error && !inputValue"
          >{{ $t("inputs.emptyValueError") }}</span
        >
       
        <span
          class="error-message"
          v-else-if="
            $v.inputValue.$error && (inputValue || '').trim().length < minLength
          "
          >{{ $t("inputs.minLengthError", { number: minLength }) }}</span
        >
        <span
          class="error-message"
          v-else-if="
            $v.inputValue.$error && (inputValue || '').trim().length > maxLength
          "
          >{{ $t("inputs.maxLengthError", { number: maxLength }) }}</span
        >
        <span
          class="error-message"
          v-else-if="$v.inputValue.$error && this.safeName === 'postal_code'"
          >{{ $t("inputs.postalCodeError") }}</span
        >
        <span
          class="error-message"
          v-else-if="
            $v.inputValue.$error &&
            this.safeName === 'document_support_number' &&
            !$v.inputValue.customDocumentSupportNumberCheck
          "
        >
          {{ $t("inputs.invalidDocumentSupportNumber") }}
        </span>
        <span
          class="error-message"
          v-else-if="$v.inputValue.$error && !$v.inputValue.customDocumentCheck"
        >
          {{ $t("inputs.invalidDocumentNumber") }}
        </span>
        <span
          class="error-message"
          v-else-if="$v.inputValue.$error && hasSimilarGuestError"
          >{{ $t("inputs.documentNumberDuplicated") }}</span
        >
      </p>
    </div>
  </div>
</template>
<script>
import pencil from "@/assets/images/icons/pencil.svg";
import arroba from "@/assets/images/icons/arroba.svg";
import secret from "@/assets/images/icons/secret.svg";
import users from "@/assets/images/icons/users.svg";
import calendar from "@/assets/images/icons/calendar.svg";
import errorx from "@/assets/images/icons/x.svg";
import check from "@/assets/images/icons/check.svg";
import btn from "@/components/shared/button.component";
import { requiredIf, email } from "vuelidate/lib/validators";
import inputIcons from "@/mixins/inputIcons";
import createSafeName from "@/mixins/createSafeName";
import validateDocuments from "@/services/documents/validateDocuments";
import debounce from "lodash/debounce";
import {
  postcodeValidator,
  postcodeValidatorExistsForCountry,
} from "postcode-validator";
import { countryList } from "@/utils/countries";
export default {
  name: "inputField",
  data() {
    return {
      inputValue: "",
      hasError: false,
      isDebounced: true,
    };
  },
  mixins: [inputIcons, createSafeName],
  components: {
    pencil,
    arroba,
    secret,
    users,
    calendar,
    errorx,
    check,
    btn,
  },
  props: {
    name: {
      type: String,
      required: true,
    },
    infoModal: {
      type: Object,
      default() {
        return {};
      },
    },
    inputName: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      required: true,
    },
    icon: {
      type: String,
      default: "",
    },
    value: {
      type: String,
    },
    placeholder: {
      type: String,
    },
    minLength: {
      type: String,
      default: "0",
    },
    maxLength: {
      type: String,
      default: "255",
    },
    optional: {
      type: Boolean,
      default: false,
    },
    countryCode: {
      type: String,
      default: "",
    },
    checkOnStart: {
      type: Boolean,
      default: false,
    },
    documentType: {
      type: String,
      default: "",
    },
    documentNationality: {
      type: String,
      default: "",
    },
    debounce: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    browserAutocomplete: {
      type: Boolean,
      default: false,
    },
    autocompleteData: {
      type: Object,
      default: null,
    },
    hasSimilarGuestError: {
      type: Boolean,
      default: null, 
    },
  },
  computed: {
    isOptionalEmpty() {
      return this.optional && !this.inputValue;
    },
  },
  created() {
    this.debouncedCallback = debounce((value) => {
      this.isDebounced = true;
      this.inputChanged(value);
    }, 500);
  },
  mounted() {
    if (this.value !== "") {
      this.inputValue = this.value;
    }
    if (this.checkOnStart) this.$v.inputValue.$touch();

    this.inputChanged(this.inputValue || "");
  },
  validations() {
    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      }),
      customBetween: this.customBetween,
    };

    if (this.type === "email") validate.email = email;

    // * Todo: Remove this, this logic should not be managed here
    if (this.inputName === "document_number" && this.isDebounced) {
      validate.customDocumentCheck = this.customDocumentCheck;
      validate.similarGuestError = () => {
        return !this.hasSimilarGuestError;
      };
    }

    if (
      this.inputName === "postal_code" &&
      !this.isOptionalEmpty &&
      this.countryCode !== ""
    ) {
      validate.postalCodeCheck = this.postalCodeCheck;
    }

    if (this.inputName === "document_support_number") {
      validate.customDocumentSupportNumberCheck =
        this.customDocumentSupportNumberCheck;
    }

    return {
      inputValue: validate,
    };
  },

  methods: {
    inputChanged(value = "") {
      this.inputValue = value.trimStart();
      if (this.inputValue.endsWith("  ")) {
        this.inputValue = this.inputValue.slice(0, -1);
      }
      if (value) {
        this.$v.inputValue.$touch();
      }
      this.hasError = this.$v.inputValue.$error;
      this.$emit("inputChanged", {
        value: this.inputValue.trim(),
        error: this.hasError,
        type: this.type,
      });
    },
    manageInputChanged(value) {
      if (this.debounce) {
        this.isDebounced = false;
        this.debouncedCallback(value);
      } else {
        this.inputChanged(value);
      }
    },
    customBetween(value) {
      if (!value) {
        return true;
      }
      value = value.trim();
      let length = value.length;
      if (this.optional && length == 0) {
        return true;
      }
      if (length > this.maxLength || length < this.minLength) {
        return false;
      }
      return true;
    },

    customDocumentCheck(value) {
      return validateDocuments(
        this.documentType,
        this.documentNationality,
        value
      );
    },

    customDocumentSupportNumberCheck(value) {
      if (!value) return true;
      const regex = /^[A-Za-z]{3}\d{6}$/;
      return regex.test(value.trim());
    },

    postalCodeCheck(value) {
      if (!value) {
        return true;
      }
      value = value.trim();
      let selectedCountry = countryList.find(
        (countries) => countries.code == this.countryCode
      );
      let country = selectedCountry?.iso2Code;
      if (postcodeValidatorExistsForCountry(country)) {
        return postcodeValidator(value, country);
      }
      return true;
    },
  },
};
</script>
<style lang="scss">
.info-button {
  height: 25px;
  width: 25px;
  padding: 0 !important;
}

.error-message {
  @apply text-red-400;
}

.white-svg > g {
  stroke: white;
}

input,
select {
  &:disabled {
    opacity: 0.4;
    cursor: default;
  }
}
</style>
