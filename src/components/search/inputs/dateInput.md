---
title: Date Input
layout: layout.html
eleventyNavigation:
  key: Date
  parent: Inputs
---

# Date Input

The dateInputComponent will allow us to write a date manually in a specific format or with the help of a calendar.
To do this, we will make use of the [vue2-datepicker](https://www.npmjs.com/package/vue2-datepicker) library.

![Date Input](/src/assets/images/docs/dateInput.png)

## Usage

Please read the general input documentation to see how all inputs are used in the application.

## Props

Remember that there are common properties defined in the parent page. Here are the properties specific to this input

|           Prop          |                                     Description                                  |  Type   | Default |
|-------------------------|----------------------------------------------------------------------------------|---------|---------|
| disableFutureDates      | Indicates whether future dates should be disabled when selecting a date          | Boolean |  false  |

## Events

It defines the different events that are launched towards the parent, who can be listened to and act accordingly:

- **inputChanged**: When a value is selected, it will close the datepicker and fire the event with the selected value indicating if there is a validation error.

## Validations

Different validations of the input values will be performed. If these characteristics are not passed, the input will be displayed with a red outline to indicate this to the user:

- **Required**: If not passed the optional prop will check that the value is not empty.
- **Format**: The format entered is checked for correctness (YYYYY-MM-DD).
- **Greater than today**: Only applies if the input is called *date_of_issue*. It simply checks that the date entered is after than the current date.
- **Lower than today**: Only applies if the input is called *date_of_expiry*. It simply checks that the date entered is before than the current date.
- **Too Old**: Only applies if the input is called *birthday_date*. Check that the age entered is not unrealistic.
- **Pax Type**: Only applies if the input is called *birthday_date*. Check that the age entered corresponds to the pax type (adult, child).
