<template>
  <div>
    <div>
      <label class="block relative" :for="browserAutocomplete ? safeName : ''">
        <span class="text-gray-700 font-black uppercase text-sm">{{ name }}</span>
        <span
          v-if="optional"
          class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
          >{{ $t("inputs.optional") }}</span
        >
        <btn
          v-if="Object.keys(infoModal).length != 0"
          @click.native="$emit('showModalInfo', inputName)"
          class="info-button rounded-full float-right"
          data-test="info-button-help"
          >?</btn
        >
      </label> 
    </div>
    <div class="block relative" >
      <div
        class="flex bg-gray-200 rounded mt-1 border-2 border-gray-200"
        :class="{
          'border-red-400': $v.inputValue.$error,
          'border-green-400':
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
          'border-gray-200': isOptionalEmpty
        }"
      >
        <div
          class="flex-auto px-4 flex items-center justify-center bg-gray-200"
          :class="{
            'bg-red-400': $v.inputValue.$error,
            'bg-green-400':
              !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
            'bg-gray-200': isOptionalEmpty
          }"
        >
          <component
            :is="cIcon"
            :class="{
              'white-svg':
                $v.inputValue.$error ||
                (!$v.inputValue.$error &&
                  $v.inputValue.$dirty &&
                  !isOptionalEmpty)
            }"
          />
        </div>
        <!-- Avoid autocomplete on the real input -->
        <input v-if="!browserAutocomplete" v-show="false" autocomplete="on" > 
        <input
          type="search"
          @input="onChange"
          @click="handleClickInside"
          :data-test="safeName"
          :name="browserAutocomplete ? safeName : ''"
          :autocomplete="browserAutocomplete ? 'on' : 'off'"
          :disabled="disabled"
          v-model="inputValue"
          class="block w-full p-4 focus:outline-none rounded-none"
        />
        <div
          class="input-transition flex justify-center items-center px-4 bg-white min-w-min"
          :class="disabled ? 'bg-gray-200' : 'bg-white'"
        >
          <transition name="bounce" mode="out-in">
            <component :is="'errorx'" v-show="$v.inputValue.$error" />
          </transition>
          <transition name="bounce" mode="in-out">
            <component
              :is="'check'"
              v-show="
                !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty
              "
            />
          </transition>
        </div>
      </div>
      <ul
        id="autocomplete-results"
        v-show="isOpen && this.results.length > 0"
        class="rounded rounded-bl-none rounded-tl-none absolute block w-full border-2 border-gray-200 focus:outline-none max-h-64 overflow-scroll z-10"
        :data-test="`${safeName}-list`"
      >
        <li
          v-for="(result, index) in results"
          :key="index"
          @click.stop.prevent="setResult(result)"
          class="autocomplete-result p-2 bg-white hover:bg-gray-200 border-b-1"
          :data-test="`${safeName}-option`"
        >
          {{ result.name }}
        </li>
      </ul>
      <p class="text-xs font-black mt-1">
        <span class="error-message" v-if="$v.inputValue.$error && !inputValue">{{
          $t("inputs.emptyValueError")
        }}</span>
      </p>
    </div>
  </div>
</template>

<script>
import pencil from "@/assets/images/icons/pencil.svg";
import arroba from "@/assets/images/icons/arroba.svg";
import secret from "@/assets/images/icons/secret.svg";
import users from "@/assets/images/icons/users.svg";
import errorx from "@/assets/images/icons/x.svg";
import check from "@/assets/images/icons/check.svg";
import btn from "@/components/shared/button.component";
import inputIcons from "@/mixins/inputIcons";
import createSafeName from "@/mixins/createSafeName";
import { requiredIf } from "vuelidate/lib/validators";

export default {
  name: "inputField",
  mixins: [inputIcons, createSafeName],
  components: { pencil, arroba, secret, users, check, errorx, btn },
  props: {
    name: {
      type: String,
      required: true
    },
    infoModal: {
      type: Object,
      default() {
        return {};
      }
    },
    inputName: {
      type: String,
      default: ""
    },
    icon: {
      type: String,
      default: ""
    },
    value: {
      type: String
    },
    placeholder: {
      type: String,
      default: "..."
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      required: false,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    allowsFreeText: {
      type: Boolean,
      default: false
    },
    browserAutocomplete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isOpen: false,
      results: [],
      selectedResult: "",
      inputValue: "",
      forceEnter: false,
      isLoading: false,
      hasError: false
    };
  },
  computed: {
    isOptionalEmpty() {
      return this.optional && !this.inputValue;
    }
  },
  watch: {
    options: {
      handler(value, oldValue) {
        if (value.length !== oldValue.length) {
          this.results = value;
          this.isLoading = false;
        }
      }
    }
  },
  mounted() {
    document.addEventListener("click", this.handleClickOutside);
    if (this.options.length > 0) {
      this.results = this.options;
    }
    if (this.value) {
      this.selectedOption = this.options.find(
        option =>
          option.value?.toLowerCase() ==
          (this.value?.value ?? this.value)?.toLowerCase()
      );
      if (!this.allowsFreeText) {
        this.selectedResult = this.selectedOption ?? null;
        this.inputValue = this.selectedOption?.name ?? "";
      } else {
        this.inputValue = this.selectedOption?.name ?? this.value;
      }
      this.$v.inputValue.$touch();
      this.hasError = this.$v.inputValue.$error;
      this.results = this.options;
    }
    if (this.checkOnStart) {
      this.$v.inputValue.$touch();
    }
  },
  validations() {
    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      }),
      customBetween: this.customBetween
    };

    if (!this.allowsFreeText) {
      validate = {
        ...validate,
        selectedResult: function() {
          if (this.isOptionalEmpty) {
            return true;
          }
          return this.selectedResult != "";
        }
      };
    }
    return {
      inputValue: validate
    };
  },
  destroyed() {
    document.removeEventListener("click", this.handleClickOutside);
  },
  methods: {
    setResult(result) {
      this.isOpen = false;
      this.selectedResult = result;
      this.$v.inputValue.$touch();
      this.inputValue = result.name;
      this.hasError = this.$v.inputValue.$error;
      this.$emit("autocompleteSelect", {
        value: result.value,
        selectedOption: result,
        results: this.results,
        error: this.hasError
      });
    },
    async onChange(e) {
      // On android devices with an older version of chrome it seems to send too many events.
      // With this we check that the input is only changed if the event is triggered by a user action.
      if (
        e.isTrusted === undefined ||
        e.isTrusted ||
        process.env.JEST_WORKER_ID !== undefined ||
        window.Cypress
      ) {
        if (e.isTrusted === undefined) {
          console.error("Is trusted event API not implemented");
        }
        // Fix for android devices with old Chrome browser.
        // For some reason the v-model property does not work correctly and does not update the value
        if ((!this.inputValue && e.target.value) || this.forceEnter) {
          this.inputValue = e.target.value;
          this.forceEnter = true;
        }
      }
      await this.handleInputChange();
    },

    async handleInputChange() {
      if (this.inputValue !== "") {
        this.results = this.options;
        this.isOpen = true;
      } else {
        this.isOpen = false;
        this.results = [];
      }
      this.selectedResult = "";
      this.$v.inputValue.$touch();
      this.hasError = this.$v.inputValue.$error;
      this.$emit("inputChanged", {
        value: this.inputValue,
        error: this.hasError
      });
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        if (this.selectedResult == "" && !this.allowsFreeText) {
          this.inputValue = "";
          this.results = this.options;
          this.$emit("inputChanged", {
            value: this.inputValue,
            error: this.$v.inputValue.$error
          });
        }
        this.isOpen = false;
      }
    },
    handleClickInside(event) {
      if (this.$el.contains(event.target)) {
        this.isOpen = true;
      }
    },
    customBetween(value) {
      if(!value){
        return true
      }
      value = value.trim();
      let length = value.length;
      if (
        !this.optional &&
        (length > this.maxLength || length < this.minLength)
      ) {
        return false;
      }
      return true;
    }
  }
};
</script>
<style lang="scss">
.info-button {
  height: 25px;
  width: 25px;
  padding: 0 !important;
}
.error-message {
  @apply text-red-400;
}

.white-svg > g {
  stroke: white;
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
}
</style>