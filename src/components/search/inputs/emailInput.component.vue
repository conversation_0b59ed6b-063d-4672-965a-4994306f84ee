<template>
  <label class="block" @click.prevent>
    <span class="text-gray-700 font-black uppercase text-sm">{{ name }}</span>
    <span
      v-if="optional"
      class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
      >{{ $t("inputs.optional") }}</span
    >
    <btn
      v-if="Object.keys(infoModal).length != 0"
      @click.native="$emit('showModalInfo', inputName)"
      class="info-button rounded-full float-right"
      data-test="info-button-help"
      >?</btn
    >
    <div
      class="flex bg-gray-200 rounded mt-1 border-2 border-gray-200"
      :class="{
        'border-red-400': $v.inputValue.$error,
        'border-green-400':
          !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
        'border-gray-200': isOptionalEmpty
      }"
    >
      <div
        class="flex-auto px-4 flex items-center justify-center bg-gray-200"
        :class="{
          'bg-red-400': $v.inputValue.$error,
          'bg-green-400':
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
          'bg-gray-200': isOptionalEmpty
        }"
      >
        <component
          :is="cIcon"
          :class="{
            'white-svg':
              $v.inputValue.$error ||
              (!$v.inputValue.$error &&
                $v.inputValue.$dirty &&
                !isOptionalEmpty)
          }"
        />
      </div>
      <!-- Avoid autocomplete on the real input -->
      <input v-show="false" autocomplete="on" />
      <input
        :type="type"
        :placeholder="placeholder"
        :name="'email'"
        autocomplete="on"
        class="block w-full p-4 focus:outline-none rounded-none"
        @input="inputChanged($event.target.value)"
        v-model="inputValue"
        :data-test="safeName"
      />
      <div
        class="input-transition flex justify-center items-center px-4 bg-white min-w-min"
      >
        <transition name="bounce" mode="out-in">
          <component :is="'errorx'" v-show="$v.inputValue.$error" />
        </transition>
        <transition name="bounce" mode="in-out">
          <component
            :is="'check'"
            v-show="
              !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty
            "
          />
        </transition>
      </div>
    </div>
    <p class="text-xs font-black mt-1">
      <span class="error-message" v-if="$v.inputValue.$error && !inputValue">{{
        $t("inputs.emptyValueError")
      }}</span>
      <span
        class="error-message"
        v-else-if="
          $v.inputValue.$error && (validateEmail || !$v.inputValue.validFormat)
        "
      >
        {{ $t("senddocuments.emailModalTitle") }}
      </span>
    </p>
  </label>
</template>

<script>
import pencil from "@/assets/images/icons/pencil.svg";
import arroba from "@/assets/images/icons/arroba.svg";
import secret from "@/assets/images/icons/secret.svg";
import users from "@/assets/images/icons/users.svg";
import calendar from "@/assets/images/icons/calendar.svg";
import errorx from "@/assets/images/icons/x.svg";
import check from "@/assets/images/icons/check.svg";
import btn from "@/components/shared/button.component";
import { requiredIf, email } from "vuelidate/lib/validators";
import inputIcons from "@/mixins/inputIcons";
import createSafeName from "@/mixins/createSafeName";

export default {
  name: "emailInput",
  data() {
    return {
      inputValue: "",
      validateEmail: null,
      hasError: true
    };
  },
  mixins: [inputIcons, createSafeName],
  components: { pencil, arroba, secret, users, calendar, errorx, check, btn },
  props: {
    name: {
      type: String,
      required: true
    },
    infoModal: {
      type: Object,
      default() {
        return {};
      }
    },
    inputName: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: ""
    },
    value: {
      type: String
    },
    placeholder: {
      type: String
    },
    minLength: {
      type: String,
      default: "0"
    },
    maxLength: {
      type: String,
      default: "255"
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isOptionalEmpty() {
      return this.optional && !this.inputValue;
    }
  },
  mounted() {
    if (this.value !== "") {
      this.inputValue = this.value;
    }
    if (this.checkOnStart) this.$v.inputValue.$touch();

    this.inputChanged(this.inputValue || "");
  },
  validations() {
    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      }),
      validFormat: email
    };
    if (this.validateEmail) {
      validate.isValid = () => this.validateEmail.valid;
    }

    return {
      inputValue: validate
    };
  },
  methods: {
    inputChanged(value = "") {
      this.inputValue = value.trimStart();
      this.validateEmail = null;
      if (this.inputValue.endsWith("  ")) {
        this.inputValue = this.inputValue.slice(0, -1);
      }
      if (value) {
        this.$v.inputValue.$touch();
      }
      this.validEmail = true;
      this.hasError = this.$v.inputValue.$error;
      this.$emit("inputChanged", {
        value: this.inputValue.trim(),
        error: this.hasError,
        type: this.type
      });
    },
    async emailValidation(emailValidation) {
      this.validateEmail = { ...emailValidation };
    }
  }
};
</script>

<style scoped></style>
