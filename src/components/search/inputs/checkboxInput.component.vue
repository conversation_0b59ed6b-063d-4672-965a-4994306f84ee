<template>
  <div class="mb-6">
    <label class="flex" :for="inputName">
      <input
        type="checkbox"
        :id="inputName"
        class="form-checkbox h-5 w-5 focus:outline-none"
        :value="inputValue"
        @change="inputChanged"
        data-test="safeName"
      />
      <span v-if="label" class="text-sm ml-2">{{ label }}</span>
    </label>
  </div>
</template>

<script>
import createSafeName from "@/mixins/createSafeName";
export default {
  name: "CheckboxInput",
  mixins: [createSafeName],
  data() {
    return {
      inputValue: false,
      hasError: false
    };
  },
  props: {
    inputName: {
      type: String,
      default: ""
    },
    value: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ""
    },
    optional: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    async inputChanged() {
      this.inputValue = !this.inputValue;
      return await this.$emit("inputChanged", {
        value: this.inputValue,
        name: this.inputName,
        error: this.hasError
      });
    }
  }
};
</script>
