<template>
  <label class="block">
    <span
      v-if="optional"
      class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
      >{{ $t("inputs.optional") }}</span
    >
    <div class="flex mt-1 w-full relative">
      <div
        class="flex-auto px-4 flex items-center justify-center bg-gray-200 rounded rounded-tr-none rounded-br-none"
        :class="{
          'bg-red-400': $v.inputValue.$error,
          'bg-green-400': !$v.inputValue.$error && $v.inputValue.$dirty
        }"
      >
        <component
          :is="cIcon"
          :class="{
            'white-svg':
              $v.inputValue.$error ||
              (!$v.inputValue.$error && $v.inputValue.$dirty)
          }"
        />
      </div>
      <transition name="bounce">
        <component
          class="absolute right-6 top-6"
          :is="'check'"
          v-show="!$v.inputValue.$error && $v.inputValue.$dirty"
        />
      </transition>
      <input
        type="time"
        :placeholder="placeholder"
        class="rounded rounded-bl-none rounded-tl-none block w-full p-4 border-2 border-gray-200 focus:outline-none"
        :class="{
          'border-red-400': $v.inputValue.$error,
          'border-green-400': !$v.inputValue.$error && $v.inputValue.$dirty
        }"
        @input="inputChanged"
        v-model="$v.inputValue.$model"
        :data-test="safeName"
        @click="onClick"
      />
    </div>
  </label>
</template>
<script>
import clock from "@/assets/images/icons/clock.svg";
import { requiredIf } from "vuelidate/lib/validators";
import inputIcons from "@/mixins/inputIcons";
import check from "@/assets/images/icons/check.svg";
import createSafeName from "@/mixins/createSafeName";
import pencil from "@/assets/images/icons/pencil.svg";

export default {
  name: "inputField",
  data() {
    return {
      inputValue: "",
      hasError: true
    };
  },

  mixins: [inputIcons, createSafeName],
  components: { clock, check, pencil },

  props: {
    name: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: ""
    },
    value: {
      type: null
    },
    placeholder: {
      type: String
    },
    optional: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.inputChanged();
  },

  validations() {
    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      })
    };

    return {
      inputValue: validate
    };
  },
  methods: {
    // We put 00:00 as default value in the arrivalTime input, we make this because sometimes the guest only put hours and forget about minutes, and doing this way we'll always have a value for all this fields
    onClick() {
      this.inputValue = "00:00";
      this.inputChanged();
    },
    inputChanged() {
      this.hasError = this.$v.inputValue.$error;

      this.$emit("inputChanged", {
        value: this.inputValue,
        error: this.hasError,
        type: this.type
      });
    },

    clearData() {
      this.inputValue = "";
      this.$v.inputValue.$reset();
    }
  }
};
</script>
<style scoped lang="scss">
.white-svg > g {
  stroke: white;
}
input[type="time"]::-webkit-calendar-picker-indicator {
  background: none;
  display: none;
}
</style>
