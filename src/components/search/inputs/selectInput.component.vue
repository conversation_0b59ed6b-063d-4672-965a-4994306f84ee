<template>
  <label class="block">
    <span class="text-gray-700 font-black uppercase text-sm">{{ name }}</span>
    <span
      v-if="optional"
      class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
      >{{ $t("inputs.optional") }}</span
    >
    <div
      class="flex bg-gray-200 rounded mt-1 border-2 border-gray-200"
      :class="{
        'border-red-400': $v.inputValue.$error,
        'border-green-400': !$v.inputValue.$error && $v.inputValue.$dirty
      }"
    >
      <div
        class="flex-auto px-4 flex items-center justify-center bg-gray-200"
        :class="{
          'bg-red-400': $v.inputValue.$error,
          'bg-green-400': !$v.inputValue.$error && $v.inputValue.$dirty
        }"
      >
        <component
          :is="cIcon"
          :class="{
            'white-svg':
              $v.inputValue.$error ||
              (!$v.inputValue.$error && $v.inputValue.$dirty)
          }"
        />
      </div>
      <select
        :style="{ 
          backgroundImage:
            'url(' + require('@/assets/images/icons/br_down.webp') + ')'
        }"
        :placeholder="placeholder"
        :data-test="safeName"
        :disabled="disabled"
        @change="inputChanged"
        v-model="inputValue"
        class="block w-full p-4 focus:outline-none bg-white"
      >
        <option value="0" selected="true" disabled="true"
          >{{ $t("inputs.defaultOption") }}
        </option>
        <option
          v-for="(option, index) in options"
          :key="index"
          :value="option.value"
          :selected="value === option.value"
          >{{ getDisplayName(option) }}
        </option>
      </select>
      <div
        class="input-transition flex justify-center items-center px-4 bg-white min-w-min"
        :class="disabled ? 'bg-gray-200' : 'bg-white'"
      >
        <transition name="bounce" mode="out-in">
          <component :is="'errorx'" v-show="$v.inputValue.$error" />
        </transition>
        <transition name="bounce" mode="in-out">
          <component
            :is="'check'"
            v-show="!$v.inputValue.$error && $v.inputValue.$dirty"
          />
        </transition>
      </div>
    </div>
  </label>
</template>
<script>
import { mapState } from "vuex";
import { countryTranslationOverrides } from "@/utils/countryTranslationOverrides";
import pencil from "@/assets/images/icons/pencil.svg";
import arroba from "@/assets/images/icons/arroba.svg";
import secret from "@/assets/images/icons/secret.svg";
import users from "@/assets/images/icons/users.svg";
import errorx from "@/assets/images/icons/x.svg";
import check from "@/assets/images/icons/check.svg";
import { requiredIf } from "vuelidate/lib/validators";
import inputIcons from "@/mixins/inputIcons";
import createSafeName from "@/mixins/createSafeName";
export default {
  name: "inputField",
  data() {
    return {
      inputValue: "0",
      hasError: true
    };
  },
  mixins: [inputIcons, createSafeName],
  components: { pencil, arroba, secret, users, check, errorx },
  props: {
    name: {
      type: String,
      required: true
    },
    options: {
      type: Array,
      required: true
    },
    icon: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String
    },
    value: {
      type: [String, Number]
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    },
    inputName: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  created() {
    if (this.value) {
      this.inputValue = this.value;
    }

    if (this.checkOnStart) {
      this.$v.inputValue.$touch();
      this.inputChanged();
    }
  },
  validations() {
    const checkIfIsEmptyOption = value => (value == 0 ? false : true);

    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      }),
      checkIfIsEmptyOption
    };

    return {
      inputValue: validate
    };
  },
  computed:{
		...mapState("brand", {
      country: "country",
    }),
	},
  watch:{
    value(newValue) {
      this.inputValue = newValue || "0";
    }
  },
  methods: {
    inputChanged(result) {
      if (result) {
        this.inputValue = result.target?.value ?? result.value;
      }
      this.$v.inputValue.$touch();
      this.hasError = this.$v.inputValue.$error;
      this.$emit("inputChanged", {
        value: this.inputValue,
        error: this.hasError
      });
    },
    getDisplayName(option) {
    const overrides = countryTranslationOverrides[this.country]?.[this.$root.$i18n.locale];
    return overrides?.[option.value] ?? option.name
    },
  }
};
</script>
