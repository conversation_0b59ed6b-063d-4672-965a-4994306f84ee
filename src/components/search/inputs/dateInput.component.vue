<template>
  <div class="date-input">
    <label class="block">
      <span class="text-gray-700 font-black uppercase text-sm">{{ name }}</span>
      <span
        v-if="optional"
        class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
        >{{ $t("inputs.optional") }}</span
      >
      <div class="flex mt-1 w-full relative">
        <transition name="bounce">
          <component
            class="absolute right-6 top-6"
            :is="'errorx'"
            v-show="this.hasError"
          />
        </transition>
        <transition name="bounce">
          <component
            class="absolute right-6 top-6 z-20"
            :is="'check'"
            v-show="!this.hasError && $v.inputValue.$dirty"
          />
        </transition>
        <div
          class="flex-auto px-4 flex items-center justify-center bg-gray-200 rounded rounded-tr-none rounded-br-none"
          :class="{
            'bg-red-400': this.hasError,
            'bg-green-400': !this.hasError && $v.inputValue.$dirty
          }"
        >
          <component
            :is="'calendar'"
            style="stroke:white;"
            :class="{
              'white-svg':
                $v.inputValue.$error ||
                (!$v.inputValue.$error && $v.inputValue.$dirty)
            }"
          />
        </div>

        <date-picker
          type="date"
          :lang="lang"
          :input-attr="{ id: this.safeName }"
          v-model="inputValue"
          valueType="format"
          class="rounded rounded-bl-none rounded-tl-none block w-full p-4 border-2 border-gray-200 focus:outline-none"
          :class="{
            'border-red-400': this.hasError,
            'border-green-400': !this.hasError && $v.inputValue.$dirty,
            'bg-gray-200': disabled
          }"
          :disabled="disabled"
          :disabled-date="disabledDates" 
          @input="inputChanged($event)"
          :ref="`datepicker-${safeName}`"
          @clear="clear"
          :default-value="inputValue"
          :placeholder="formattedPlaceholder"
          :format="countryFormat()"
          :data-test="safeName"
        />
      </div>
      <p class="text-xs  font-black mt-1">
        <span
          class="error-message"
          data-test="empty-value-error"
          v-if="this.hasError && !inputValue"
        >
          {{ $t("inputs.emptyValueError") }}
        </span>

        <span
          class="error-message"
          v-if="
            this.hasError &&
              this.errorType == 'date_of_issue' &&
              inputValue &&
              $v.inputValue.isValidDate
          "
          data-test="date_of_issue_error"
        >
          {{ $t("inputs.date.greaterThanTodayError", { name: nameToLower }) }}
        </span>

        <span
          class="error-message"
          v-if="
            this.hasError &&
              this.errorType == 'date_of_expiry' &&
              inputValue &&
              $v.inputValue.isValidDate
          "
          data-test="date_of_expiry_error"
        >
          {{
            $t("inputs.dateFormatError")
          }}
        </span>

        <span
          class="error-message"
          v-if="
            this.hasError &&
              this.errorType == 'birthday_date' &&
              inputValue &&
              $v.inputValue.isValidDate &&
              this.isTooOld 
          "
          data-test="birthday_date_error"
        >
          {{ $t("inputs.date.tooOld") }}
        </span>

        <span
          class="error-message"
          v-if="
            this.hasError &&
              this.errorType == 'birthday_date' &&
              inputValue &&
              this.guestData.pax_type == 'AD' &&
              $v.inputValue.isValidDate &&
              !this.isTooOld
          "
          data-test="birthday_date_error"
        >
          {{
            $t("inputs.date.incorrectPaxError", {
              paxType: showPaxInfo
            })
          }}
        </span>

        <span
          class="error-message"
          v-if="
            this.hasError &&
              (this.errorType == 'birthday_date' ||
                this.errorType == 'child-birthday-input') &&
              inputValue &&
              this.guestData.pax_type != 'AD' &&
              $v.inputValue.isValidDate &&
              !this.isTooOld
          "
          data-test="birthday_date_error"
        >
          {{
            $t("inputs.date.incorrectPaxError", {
              paxType: showPaxInfo
            })
          }}
        </span>
        <span
          class="error-message"
          data-test="date-format-error"
          v-if="this.hasError && !$v.inputValue.isValidDate && inputValue"
        >
          {{ $t("inputs.dateFormatError") }}
        </span>
      </p>
    </label>
  </div>
</template>
<script>
import DatePicker from "vue2-datepicker";
import "vue2-datepicker/index.css";
import "vue2-datepicker/locale/es";
import "vue2-datepicker/locale/en";
import { mapState, mapGetters } from "vuex";
import { required } from "vuelidate/lib/validators";
import calendar from "@/assets/images/icons/calendar.svg";
import errorx from "@/assets/images/icons/x.svg";
import check from "@/assets/images/icons/check.svg";
import moment from "moment";
import createSafeName from "@/mixins/createSafeName";
import isPaxType from "@/mixins/isPaxType";
import { isAfter, isBefore, subDays, subYears, addDays } from "date-fns"
import { COUNTRY_CODE, EMITTED_VALUE, DEFAULT_FORMAT, AMERICAN_FORMAT} from "@/mixins/dateFormat.js"
import dateFormat from "@/mixins/dateFormat";
import guestConfig from "@/mixins/guestConfig";

const isValidDate = function (value) {
	const format = this.countryFormat();
	const date = moment(value, format, true);
	return date.isValid();
};

export default {
  name: "dateInput",
  components: { DatePicker, calendar, errorx, check },
  mixins: [createSafeName, isPaxType, dateFormat, guestConfig],
  data() {
    return {
      inputValue: "",
      hasError: false,
      errorType: "",
      lang: this.$root.$i18n.locale,
    };
  },
  props: {
    name: {
      type: String,
      required: true
    },
    inputName: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: ""
    },
    value: {
      type: null
    },
    placeholder: {
      type: String
    },
    minLength: {
      type: String,
      default: "0"
    },
    maxLength: {
      type: String,
      default: "255"
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    },
    disableFutureDates: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    const inputElement = document.querySelector(`#${this.safeName}`);
    if (inputElement) {
      inputElement.addEventListener("input", () => {
        let valueDate = inputElement.value;
        valueDate = this.autoFormatDate(valueDate);
        this.inputValue = valueDate;
        this.inputChanged(this.inputValue);
      });

      inputElement.addEventListener("keydown", (event) => {
        this.isBackspace = event?.key === "Backspace"
      });
      inputElement.addEventListener("keyup", () => {
        this.isBackspace = false;
      });
      }
      if (this.value && this.value !== "") {
        this.inputValue = this.value;

        if (moment(this.inputValue, EMITTED_VALUE, true).isValid()) {
          this.inputValue = moment(this.inputValue, EMITTED_VALUE).format(this.countryFormat());
        } 
      }

    if (this.checkOnStart) {
      this.$v.inputValue.$touch();
      this.inputChanged(this.inputValue);
    }
  },

  validations() {
    let validate = {
      required,
      isValidDate
    };

    return {
      inputValue: validate
    };
  },
  
  computed: {
    ...mapState("brand", ["config"]),
    ...mapState("brand", { placeCountry: "country"}),
    ...mapGetters("guest", { guestData: "getSelectedGuest" }),
    ...mapState("reservations", ["reservationSelected"]),

    //Translations on placeholder, wether the brand is Brazilian or not.
    formattedPlaceholder() {
      return COUNTRY_CODE.includes(this.placeCountry) 
        ? this.$t('validatedata.placeHolderAmerican') 
        : this.$t('validatedata.placeHolder')
      ;
    },

    formattedValue() {
      if (moment(this.inputValue, EMITTED_VALUE, true).isValid()) {
        return  this.inputValue;
      } else {
        return  moment(this.inputValue, this.countryFormat()).format(EMITTED_VALUE);
      }
    },

    nameToLower() {
      return this.name.toLowerCase();
    },
    isTooOld() {
      return isBefore(this.formattedValue, subYears(new Date(), 120))      
    },

    isTooYoung() {
      return isAfter(this.formattedValue, addDays(new Date(), 1))      
    },
    showPaxInfo() {
      let pax_type = this.guestData?.pax_type;
      return this.$t(`statusComponent.${pax_type}`).toLowerCase();
    },
    guestAge() {
      const checkInDate = this.reservationSelected.check_in;
      return moment(checkInDate).diff(moment(this.formattedValue), "years");
    },
    disabledDates(){
      return date => {
        const currentDate = new Date();
        const inputName = this.inputName;
        return(
            date >= currentDate && this.disableFutureDates) //Disables future dates only if it's checked
        || (date >= currentDate && inputName === 'date_of_issue') //Disables future Dates on "date of issue" inputs
        || (date <= new Date().setFullYear(currentDate.getFullYear() - 120)) //Disables all dates 120 years before
        || (date <= new Date((new Date().setDate(new Date().getDate()-1))) && inputName === 'date_of_expiry' && !this.config.allow_expired_documents)/*Disables past dates on date of expiry inputs, allows today*/
      }
    }
  },
  methods: {
    clear() {
      this.$emit("inputChanged", {
        value: "",
        error: true
      });
    },
    inputChanged(event) {
      this.$v.inputValue.$touch();
      this.hasError = this.$v.inputValue.$error || this.isNotAValidValue();

      if (this.isBackspace && this.inputValue) {
        this.inputValue = this.inputValue.slice(0, -1).replace(/-$/, "");
        this.isBackspace = false
        return;
      }
      let value = "";
      if (event && event.target) {
        value = event.target.value;
      } else if (typeof event === "string" || typeof event === "number") {
        value = String(event);
      }
      const formattedValue = this.autoFormatDate(value);
      this.$nextTick(() => {
        this.inputValue = formattedValue;
        document.querySelector(`#${this.safeName}`).value = formattedValue;
      });
      if (!this.hasError) {
        this.$refs[`datepicker-${this.safeName}`].closePopup();
      } else {
        this.$refs[`datepicker-${this.safeName}`].openPopup();
        this.errorType = this.inputName;
      }
      this.$emit("inputChanged", {
        value: this.formattedValue,
        error: this.hasError
      });
    },
  autoFormatDate(value) {
      if (typeof value !== 'string') {
        value = value != null ? String(value) : '';
      }
      if (!value) return "";

      const sanitizedValue = value.replace(/\D/g, "");
      const day = sanitizedValue.slice(0, 2);
      const month = sanitizedValue.slice(2, 4);
      const year = sanitizedValue.slice(4, 8);

      let formattedDate = "";

      if (day.length === 2) {
        formattedDate += day + "-";
      } else {
        formattedDate += day;
      }

      if (month.length === 2) {
        formattedDate += month + "-";
      } else {
        formattedDate += month;
      }

      formattedDate += year;
      return formattedDate;
    },
    // This makes the error handling depending on the input name
     isNotAValidValue() {
      //TODO: Change switch to map
      switch (this.inputName) {
        // issue > today && issue < expiry
        case "date_of_issue":
        case "child-birthday-input":
          return isAfter(this.formattedValue, new Date());
        // expiry > today
        case "date_of_expiry":
          //Shows error if allow expired documents is false and date value is before today
          if (isBefore(this.formattedValue, subDays(new Date(), 1)) && this.config.allow_expired_documents == "false" ) {
            return false;
          }
          if (!this.config.allow_expired_documents) {
            const checkInDate = this.reservationSelected.check_in;
            return isBefore(this.formattedValue, subDays(new Date(checkInDate),1));
          }
          return false;
        // Depends on pax_type. Currently, we take ADULT_AGE as reference
        case "birthday_date": {
          const ADULT_AGE = this.getAdultAge()
            const checkInDate = this.reservationSelected.check_in;
            const isChild = this.isChild(this.formattedValue, checkInDate, ADULT_AGE);
            let pax_type = this.guestData?.pax_type;

            if (pax_type == "AD" || pax_type == null) {
              return this.isTooOld || this.isTooYoung;
            }

            return !isChild || this.isTooOld;
          }
        default:
          return false;
      }
    }
  }
};
</script>
<style lang="scss">
.mx-datepicker {
  width: 100%;

  i {
    display: none;
  }
}

.mx-input {
  display: inline-block !important;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
  padding-left: 0 !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  background-color: transparent !important;
  border: transparent !important;
  border-radius: 0 !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.mx-icon-clear {
  right: 35px;
}
.white-svg > path {
  stroke: white;
}
</style>
