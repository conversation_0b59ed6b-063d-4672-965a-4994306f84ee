<template>
  <label class="block" @click.prevent ref="phone_wrapper">
    <span class="text-gray-700 font-black uppercase text-sm">{{ name }}</span>
    <span
      v-if="optional"
      class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
      >{{ $t("inputs.optional") }}</span
    >
    <div
      class="flex rounded mt-1 border-2 border-gray-200 "
      :class="{
        'border-red-400': $v.inputValue.$error,
        'border-green-400':
          !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
        'border-gray-200': isOptionalEmpty
      }"
    >
      <div
        class="flex-auto px-4 flex items-center justify-center bg-gray-200"
        :class="{
          'bg-red-400': $v.inputValue.$error,
          'bg-green-400':
            !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty,
          'bg-gray-200': isOptionalEmpty
        }"
      >
        <component
          :is="'phone'"
          :class="{
            'white-svg':
              $v.inputValue.$error ||
              (!$v.inputValue.$error &&
                $v.inputValue.$dirty &&
                !isOptionalEmpty)
          }"
        />
      </div>

      <vue-tel-input
        :key="key"
        :placeholder="placeholder"
        :allCountries="phoneCountries"
        :defaultCountry="selectedCountry || this.placeCountry"
        validCharactersOnly
        styleClasses="block w-full focus:outline-none p-4"
        :autoDefaultCountry="false"
        :dropdownOptions="dropdownOptions"
        :style="{
          border: 'none',
          boxShadow: 'none',
        }"
        :class="disabled ? 'bg-gray-200' : 'bg-white'"
        v-model="formattedInputValue"
        mode="national"
        :inputOptions="inputProps"
        :data-test="safeName"
        :disabled="disabled"
        @input="inputChanged"
        @country-changed="countryChanged"
      />
      <div
        class="input-transition flex justify-center items-center px-4 bg-white min-w-min"
        :class="disabled ? 'bg-gray-200' : 'bg-white'"
      >
        <transition name="bounce" mode="out-in">
          <component :is="'errorx'" v-show="$v.inputValue.$error" />
        </transition>
        <transition name="bounce" mode="in-out">
          <component
            :is="'check'"
            v-show="
              !$v.inputValue.$error && $v.inputValue.$dirty && !isOptionalEmpty
            "
          />
        </transition>
      </div>
    </div>
    <p class="text-xs font-black mt-1">
      <span class="error-message" v-if="$v.inputValue.$error && !inputValue">{{
        $t("inputs.emptyValueError")
      }}</span>
    </p>
  </label>
</template>
<script>
import { requiredIf } from "vuelidate/lib/validators";
import inputIcons from "@/mixins/inputIcons";
import check from "@/assets/images/icons/check.svg";
import errorx from "@/assets/images/icons/x.svg";
import createSafeName from "@/mixins/createSafeName";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/dist/vue-tel-input.css";
import { phoneCountries } from "@/utils/countries";
import phone from "@/assets/images/icons/phone.svg";
import { mapState } from "vuex";

export default {
  name: "phoneInput",
  data() {
    return {
      inputValue: "",
      formattedInputValue: "",
      key: 0,
      country: this.selectedCountry,
      hasError: false,
      validPhoneNumber: null,
      dropdownWidth: null,
      runCount: 0
    };
  },

  mixins: [inputIcons, createSafeName],
  components: { check, errorx, VueTelInput, phone },
  props: {
    name: {
      type: String,
      required: true
    },
    inputName: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      required: true
    },
    value: {
      type: String
    },
    placeholder: {
      type: String
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    },
    countryCode: {
      type: String,
      default: ""
    },
    browserAutocomplete: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState("brand", { placeCountry: "country"}),
    isOptionalEmpty() {
      return this.optional && !this.inputValue;
    },
    phoneCountries: () => phoneCountries,
    inputProps() {
      return {
        autocomplete: this.browserAutocomplete ? 'on' : 'off',
        autofocus: false,
        "aria-describedby": "",
        id: "",
        maxlength: 25,
        name: this.inputName,
        showDialCode: false,
        placeholder: this.placeholder,
        readonly: false,
        required: !this.optional,
        tabindex: 0,
        type: "tel"
      };
    },
    dropdownOptions() {
      return {
        disabled: false,
        showDialCodeInList: true,
        showDialCodeInSelection: true,
        showFlags: false,
        showSearchBox: false,
        tabindex: 0,
        width: this.dropdownWidth
      };
    },
    isPhoneValidationAvailable() {
      return !this.isOptionalEmpty;
    },
    selectedCountry() {
      return (
        phoneCountries.find(
          country =>
            this.countryCode == country.iso2 || this.countryCode == country.iso3
        )?.iso2 || this.countryCode
      );
    }
  },

  mounted() {
    window.addEventListener("resize", this.getWidth);
    this.dropdownWidth = this.$refs?.phone_wrapper?.clientWidth + "px";
    if (this.value !== "") {
      this.inputValue = this.value;
    }
    if (this.checkOnStart) this.$v.inputValue.$touch();
    this.inputChanged(this.value || "");

    // Disable autocomplete on chromium
    const telephoneInput = document.getElementsByName("telephone")[0];
    if (telephoneInput && !this.browserAutocomplete) {
      const inputElement = document.createElement("input");
      inputElement.setAttribute("autocomplete", "on");
      inputElement.style.display = "none";
      telephoneInput.parentNode.insertBefore(inputElement, telephoneInput);
      telephoneInput.name = (Math.random() + 1).toString(36).substring(7);
    }
  },
  unmounted() {
    window.removeEventListener("resize", this.getWidth);
  },
  watch: {
    countryCode: function(newValue, oldValue) {
      if(this.runCount > 1 || this.formattedInputValue.length === 0){ // First condition controlls autocomplete on change, second condition controlls autocomplete on first time
        const foundCountry = phoneCountries.find(
          country => country.iso3 === newValue || country.iso2 === newValue
          );
          if (newValue && newValue !== oldValue && this.$route.name === "ValidateData") {
            this.key++; // Changing key because defaultCountry is not a reactive property, so adding key makes re render
            this.country = foundCountry?.iso2;
            this.inputChanged("", foundCountry);
          }
        }
        this.runCount ++
        },
    deep: true
  },

  validations() {
    const isValidPhone = () => {
      return this.isPhoneValidationAvailable ? this.validPhoneNumber : true;
    };

    let validate = {
      required: requiredIf(() => {
        return !this.optional;
      }),
      isValidPhone
    };

    return {
      inputValue: validate
    };
  },
  methods: {
    inputChanged(value, countryOptions) {
      const sanitizedValue = String(value)
      this.getWidth();
      this.inputValue = countryOptions?.nationalNumber || sanitizedValue;
      this.formattedInputValue = countryOptions?.formmatedNumber || sanitizedValue;
      this.validPhoneNumber = countryOptions?.valid || false;
      if (countryOptions?.countryCode) {
        this.country = countryOptions?.countryCode;
      }

      if (sanitizedValue) {
        this.$v.inputValue.$touch();
      }

      this.hasError = this.$v.inputValue.$error;
      this.$emit("inputChanged", {
        value: this.inputValue,
        country: this.country,
        dialCode: "+" + countryOptions?.countryCallingCode,
        error: this.hasError
      });
    },

    countryChanged(country) {
      this.country = country?.iso2;
    },

    getWidth() {
      this.dropdownWidth = this.$refs?.phone_wrapper?.clientWidth + "px";
    },
    clearData() {
      this.inputValue = "";
      this.$v.inputValue.$reset();
    }
  }
};
</script>
<style lang="scss">
.vti__dropdown-list {
  @apply rounded rounded-bl-none rounded-tl-none block w-full p-4 border-2 border-gray-200 focus:outline-none;
}
.vti__dropdown-list.below {
  top: 42px;
  left: -70px;
  @apply p-0 font-light;
}
.vti__flag-wrapper {
  display: none;
}
.vti__dropdown-item {
  @apply p-2;
}
.vti__dropdown {
  @apply p-0;
}
.vti__country-code {
  @apply mx-1 text-base;
}
</style>
