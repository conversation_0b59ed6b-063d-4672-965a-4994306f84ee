<template>
  <component
    ref="component"
    v-if="active"
    :is="input"
    :value="value"
    v-bind="componentProps"
    @inputChanged="inputChanged($event)"
    @showModalInfo="showModalInfo($event)"
  />
</template>
<script>
import defaultInput from "@/components/search/newInputs/defaultInput.component";
import dateInput from "@/components/search/newInputs/dateInput.component";
import emailInput from "@/components/search/newInputs/emailInput.component.vue";

export default {
  name: "inputField",
  components: {
    defaultInput,
    dateInput,
    emailInput
  },
  props: {
    active: {
      type: Boolean,
      default: true
    },
    name: {
      type: String,
      required: true
    },
    infoModal: {
      type: Object,
      default() {
        return {};
      }
    },
    inputName: {
      type: String,
      default: ""
    },
    inputError: {
      type: Object,
      default() {
        return {
          error: false,
          input: this.inputName,
          label: ""
        };
      }
    },
    type: {
      type: String,
      default: "text"
    },
    options: {
      type: Array,
      default: () => []
    },
    icon: {
      type: String,
      default: ""
    },
    value: {
      type: null
    },
    placeholder: {
      type: String
    },
    minLength: {
      type: String,
      default: "0"
    },
    maxLength: {
      type: String,
      default: "255"
    },
    optional: {
      type: Boolean,
      default: false
    },
    checkOnStart: {
      type: Boolean,
      default: false
    },
    disableFutureDates: {
      type: Boolean,
      default: false
    },
    countryCode: {
      type: String,
      default: ""
    },
    documentType: {
      type: String,
      default: ""
    },
    documentNationality: {
      type: String,
      default: ""
    },
    allowsFreeText: {
      type: Boolean,
      default: false
    },
    debounce: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ""
    },
    timeLimitCheckin: {
      default: null
    },
    closeTimeLimitCheckin:{
      default: null
    },
    allowTimeLimit:{
      type: Boolean,
      default: false
    }
  },
  computed: {
    input() {
      const inputList = {
        date: "dateInput",
        email: "emailInput",
        phone: "phoneInput",
        number: "numberInput"
      };

      return inputList[this.type] || "defaultInput";
    },
    componentProps() {
      let props = {};
      props.active = this.active;
      props.icon = this.icon;
      props.name = this.name;
      props.infoModal = this.infoModal;
      props.inputName = this.inputName;
      props.value = this.value;
      props.placeholder = this.placeholder;
      props.minLength = this.minLength;
      props.maxLength = this.maxLength;
      props.optional = this.optional;
      props.checkOnStart = this.checkOnStart;
      props.disableFutureDates = this.disableFutureDates;
      props.countryCode = this.countryCode;
      props.documentType = this.documentType;
      props.documentNationality = this.documentNationality;
      props.allowsFreeText = this.allowsFreeText;
      props.disabled = this.disabled;
      props.label = this.label;
      props.type = this.type;
      props.timeLimitCheckin = this.timeLimitCheckin;
      props.closeTimeLimitCheckin = this.closeTimeLimitCheckin;
      props.allowTimeLimit = this.allowTimeLimit;

      props.debounce = this.debounce;
      return props;
    }
  },
  methods: {
    inputChanged(event) {
       this.$emit("inputChanged", event);
    },
    showModalInfo(event) {
      this.$emit("showModalInfo", event);
    },
    resetInput() {
      this.$refs?.component?.resetInput();
    },
    autocompleteSelect(event) {
      this.$emit("autocompleteSelect", event);
    },
    emailValidation(emailValidation) {
      this.$refs.component.emailValidation(emailValidation);
    }
  }
};
</script>
