---
title: Status
layout: layout.html
eleventyNavigation:
  key: Status
  parent: Components
---

# Components - Status

This component calculates the current status of every guest. It is displayed in the status view.

## Props

The main property is *pax*, but it also has some computed properties that are responsible to display the updated information about every guest.

|         Prop          |                                             Description                                        |  Type   | Default |
|-----------------------|------------------------------------------------------------------------------------------------|---------|---------|
| pax                   | Contains the full data that the PMS returns about the guest                                    | Object  |    -    |
| isPaxHolder           | Is the result after calculating if pax is holder and if config has show holder active          | Boolean |  false  |
| isPaxValidated        | Is equal to pax processCompleted or pax validated, depending if config allows partial check-in | Boolean |  false  |
| showPaxInfo           | Returns user friendly info about the pax                                                       | String  | "Adult" |