<template>
  <button
    @mouseenter="toogleHover()"
    @mouseleave="toogleHover()"
    :style="cssVariables"
    class="status degrade rounded border w-full"
    :disabled="!isReceptionMode ? isPaxValidated : !hasToSign"
    :class="{
      'border-green-400 cursor-default':
        (isPaxValidated && !isReceptionMode) || (isReceptionMode && !hasToSign),
      'cursor-pointer':
        (!isPaxValidated && !isReceptionMode) || (isReceptionMode && hasToSign),
      'cursor-default': isReceptionMode && !hasToSign
    }"
  >
    <div class="flex flex-grow">
      <div class="status-item flex m-auto p-4">
        <adult v-if="pax.pax_type === 'AD'" data-test="adult" />
        <adult v-if="pax.pax_type === null" data-test="adult" />
        <junior v-if="pax.pax_type === 'JR'" data-test="junior" />
        <kid v-if="pax.pax_type === 'CH'" data-test="child" />
        <baby v-if="pax.pax_type === 'BB'" data-test="baby" />
      </div>
      <div class="pax-data flex flex-col pl-4 flex-grow justify-center m-auto">
        <span
          class="text-xs uppercase text-gray-400 text-left my-auto"
          data-test="holder"
        >
          {{ showPaxInfo }}
        </span>
        <span
          v-if="isPaxValidated"
          data-test="pax-validated"
          class="text-sm uppercase font-black text-left"
          :class="{ 'main-color-link': isReceptionMode && hasToSign }"
        >
          {{ getGuestName(pax) }}
        </span>
        <span
          v-else-if="
            (config.partial_checkin && !pax.validated) ||
            (!config.partial_checkin && !pax.processCompleted)
          "
          :class="{
            'none-validate-pax-span':
              (!isPaxValidated && !isReceptionMode) ||
              (isReceptionMode && hasToSign),
            'disabled-pax-span': isReceptionMode && !hasToSign
          }"
          data-test="pax-no-validated"
        >
          {{ paxText }}
        </span>
      </div>
      <div
        class="bg-green-400 p-2 flex flex-col items-center justify-center"
        v-if="isPaxValidated && !isReceptionMode"
      >
        <completed />
      </div>
    </div>
  </button>
</template>
<script>
import adult from "@/assets/images/icons/adult.svg";
import junior from "@/assets/images/icons/junior.svg";
import kid from "@/assets/images/icons/kid.svg";
import baby from "@/assets/images/icons/baby.svg";
import completed from "@/assets/images/icons/completed.svg";
import cssVariables from "@/mixins/cssVariables";
import getGuestName from "@/mixins/getGuestName";
import { mapState, mapGetters } from "vuex";
import { getGuestAgeOnCheckin } from "@/utils/guestUtils";
export default {
  name: "status",
  mixins: [cssVariables, getGuestName],
  components: { adult, junior, kid, baby, completed },
  data() {
    return {
      hover: false,
    };
  },
  props: {
    pax: {
      type: Object,
      required: true,
    },
  },
  computed: {
    ...mapState("brand", ["config"]),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("app", ["isReceptionMode"]),
    isPaxValidated() {
      return this.config.partial_checkin
        ? this.pax?.validated
        : this.pax?.processCompleted;
    },
    isPaxHolder() {
      return this.config.show_holder && this.pax?.holder;
    },
    showPaxInfo() {
      let pax_type = this.pax?.pax_type;

      if (pax_type !== "AD" && pax_type !== null) {
        return this.$t(`statusComponent.${pax_type}`);
      }

      return `${this.$t("statusComponent.AD")} ${
        this.isPaxHolder ? ` - ${this.$t("statusComponent.holder")}` : ""
      }`;
    },
    hasToSign() {
      if (this.pax?.signedDocuments || !this.reservationSelected) {
        return false;
      }

      if (this.config.children_process_on_reception) {
        return true;
      }
      if (this.config.scan_children_like_adults) {
        return true;
      }
      if (this.pax?.pax_type === "AD") { //adults
        return true;
      }
      
      return getGuestAgeOnCheckin(
          this.reservationSelected.check_in,
          this.pax?.birthday_date
        ) > this.config.child_required_identity_documents_age
     
   
    },
    paxText() {
      if (this.isReceptionMode) {
        if (this.hover && this.hasToSign) { 
          return this.$t("statusComponent.sign");
        }
        return this.getGuestName(this.pax) || this.$t("reservations.free");
      } else {
        return !this.hover
          ? this.getGuestName(this.pax) || this.$t("reservations.free")
          : this.$t("statusComponent.checkIn");
      }
    },
  },
  methods: {
    toogleHover() {
      this.hover = !this.hover;
    },
  },
};
</script>
<style lang="scss">
.main-color-link {
  color: var(--bgColor);
}
.none-validate-pax-span {
  @apply text-sm uppercase font-black main-color-link text-left;
}
.disabled-pax-span {
  @apply text-sm uppercase font-black text-left;
}

svg > .svg-avatar {
  fill: none;
  stroke: #bebebe;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 1.5px;
}
</style>
