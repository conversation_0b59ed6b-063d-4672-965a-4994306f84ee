<template>
  <a
    :href="link"
    target="_blank"
    class="rounded flex justify-center items-center mb-6"
    :style="styles"
  >
    <span class="link-text px-10 py-5 font-black text-white text-center">
      {{ $t("redirectLink.text", { hotel_name: hotelData.name }) }}</span
    >
  </a>
</template>
<script>
export default {
  props: {
    hotelData: {
      type: Object,
      default: () => {
        return {
          name: "",
          background: ""
        };
      }
    },
    link: {
      type: String,
      default: ""
    }
  },
  computed: {
    styles() {
      return {
        "background-image": "url(" + this.hotelData.background + ")",
        "background-size": "cover",
        "background-position": "center",
        "background-repeat": "no-repeat",
        "background-blend-mode": "soft-light"
      };
    }
  }
};
</script>
<style lang="scss">
.link-text {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
