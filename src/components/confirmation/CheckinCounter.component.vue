<template>
  <p
    class="text-center my-2 uppercase text-xs absolute inset-x-0 top-9"
    :style="{ color: this.mainColor }"
  >
    {{ $t("confirmation.checkIn") }}
    <span data-test="checkinCounter">
      {{ checkedGuests }} {{ $t("confirmation.outOf") }} {{ totalGuests }}
    </span>
  </p>
</template>
<script>
import { mapGetters, mapState } from "vuex";
export default {
  name: "CheckinCounter",
  props: {
    type: {
      type: String,
      default: "p"
    }
  },
  computed: {
    ...mapGetters("guest", { completedGuestsList: "getCompletedGuests" }),
    ...mapGetters("brand", ["mainColor"]),
    ...mapState("guest", { guestList: "list" }),
    checkedGuests() {
      return this.completedGuestsList.length;
    },
    totalGuests() {
      return this.guestList.length;
    }
  }
};
</script>
