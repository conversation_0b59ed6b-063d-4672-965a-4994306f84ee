<template>
  <div
    :style="cssVariables"
    class="document-button degrade rounded border border-gray-200 p-4 flex flex-row relative items-center cursor-pointer relative"
    :class="{
      'border-green-400 cursor-default': isRead
    }"
  >
    <document-sign class="document-icon" />
    <span class="text-sm uppercase font-black main-color-link ml-4">
      {{ document.title }}
    </span>
    <div
      v-show="isRead"
      class="completed bg-green-400 flex flex items-center justify-center absolute h-full p-2"
    >
      <completed />
    </div>
  </div>
</template>
<script>
import cssVariables from "@/mixins/cssVariables";
import documentSign from "@/assets/images/icons/document-sign.svg";
import completed from "@/assets/images/icons/completed.svg";
export default {
  name: "document",
  mixins: [cssVariables],
  components: { documentSign, completed },
  props: ["document"],
  computed: {
    isRead() {
      return this.document.read === true;
    }
  }
};
</script>
<style lang="scss" scoped>
.main-color-link {
  color: var(--bgColor);
}
.document-icon {
  width: 40px;
  min-width: 40px;
  fill: #a8a8a8;
}
.completed {
  right: 0;
  bottom: 0;
}
</style>
