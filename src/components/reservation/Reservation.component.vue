<template>
  <div
    class="degrade reservation rounded border border-gray-200 bg-gray-100 p-8 relative h-full"
    :style="cssVariables"
  >
    <div
      class="circle circle-left rounded rounded-tl-none rounded-bl-none rounded-tr-full rounded-br-full border border-l-0 border-gray-200"
    />
    <div
      class="circle circle-right rounded rounded-br-none rounded-tr-none rounded-tl-full rounded-bl-full border border-r-0 border-gray-200"
    />
    <div class="line w-full border border-gray-200 border-dashed" />
    <div class="flex flex-col h-full">
      <transition
        name="fadeLeft"
        @leave="flipReservation = false"
        @after-leave="flipGuests = true"
      >
        <div
          style="animation-duration: 0.3s"
          class="items flex flex-grow h-full flex-col"
          v-show="!showGuests && flipReservation"
        >
          <item
            v-if="reservation.res_localizer"
            :label="$t('reservations.localizer')"
            :name="reservation.res_localizer"
          />
          <item
            v-if="reservation.guests"
            icon="users"
            :label="$t('reservations.users')"
            :linkName="$t('reservations.show')"
            @openGuests="showGuests = true"
          />
          <item
            v-if="reservation.res_room_type"
            icon="bed"
            :label="$t('reservations.room')"
            :name="reservation.res_room_type"
          />
          <item
            v-if="reservation.res_board"
            icon="plate"
            :label="$t('reservations.board')"
            :name="reservation.res_board"
          />
          <item
            v-if="reservation.check_in"
            icon="checkin"
            :label="$t('reservations.checkin')"
            :name="parseDate(reservation.check_in)"
          />
          <item
            v-if="reservation.check_out"
            icon="checkout"
            :label="$t('reservations.checkout')"
            :name="parseDate(reservation.check_out)"
          />
        </div>
      </transition>
      <transition
        name="fadeRight"
        @leave="flipGuests = false"
        @after-leave="flipReservation = true"
      >
        <div
          style="animation-duration: 0.3s"
          class="guests flex flex-grow h-full flex-col"
          v-show="showGuests && flipGuests"
        >
          <h1 class="uppercase font-black text-sm mb-8 text-center">
            {{ $t("reservations.guestsTitle") }}
          </h1>
          <guest-list :guests="reservation.guests" class="mb-5" />
          <p
            class="text-center detailView text-xs uppercase font-black mb-10 cursor-pointer"
            @click="showGuests = false"
            v-show="showGuests"
          >
            {{ $t("reservations.goToDetail") }}
          </p>
        </div>
      </transition>
      <btn
        :disabled="reservationCompleted"
        @click.native="gotoStatus"
        data-test="continueReservation"
      >
        {{ buttonText }}
      </btn>
    </div>
  </div>
</template>
<script>
import item from "./Reservation.item.component";
import btn from "@/components/shared/button.component";
import guestList from "@/components/reservation/GuestList.component";
import { mapState, mapActions } from "vuex";
import cssVariables from "@/mixins/cssVariables";
import redirect from "@/mixins/redirect";
import moment from "moment";
import { Tracker } from "@/tracker";
require("moment/min/locales.min");

export default {
  name: "reservation",
  data() {
    return {
      showGuests: false,
      flipGuests: false,
      flipReservation: true
    };
  },
  components: { item, btn, guestList },
  mixins: [cssVariables, redirect],
  props: {
    reservation: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapState("reservations", ["data", "reservationSelected"]),
    ...mapState("brand", ["config"]),
    getPassengers() {
      let completed = this.reservation?.guests?.filter(guest => {
        return guest.validated;
      }).length;

      return { completed, total: this.reservation?.guests?.length };
    },
    reservationCompleted() {
      return this.reservation?.guests?.every(guest => guest.validated);
    },
    buttonText() {
      return this.reservationCompleted
        ? this.$t("reservations.buttonCompleted")
        : this.$t("reservations.button");
    }
  },
  methods: {
    ...mapActions("reservations", ["SET_RESERVATION_SELECTED"]),
    ...mapActions("guest", ["SET_GUEST_TO_LIST"]),
    async gotoStatus() {
      await this.$store.dispatch("loading/LOADING", true);
      await this.SET_RESERVATION_SELECTED(this.reservation);
      await this.SET_GUEST_TO_LIST(this.reservationSelected?.guests);
      Tracker.recordCustomEvent("reservation_selected", {
        reservation: {
          res_id: this.reservation?.res_id,
          res_localizer: this.reservation?.res_localizer,
          brand_id: this.reservation?.brand_id,
          check_in: this.reservation?.check_in,
          check_out: this.reservation?.check_out,
        }
      });
      return this.redirect({ name: "Status" });
    },
    parseDate(date) {
      moment.locale(this.$i18n.locale);
      return moment(date).format("LL");
    }
  }
};
</script>
<style lang="scss">
.detailView {
  color: var(--bgColor);
}

.reservation {
  min-height: 400px;
  overflow-x: hidden;
}

.circle {
  z-index: 10;
  width: 30px;
  height: 60px;
  background-color: white;
  position: absolute;
  bottom: 80px;
}

.circle-left {
  left: -2px;
}

.circle-right {
  right: -2px;
}

.line {
  left: 0;
  z-index: 5;
  position: absolute;
  height: 1px;
  bottom: 110px;
}
</style>
