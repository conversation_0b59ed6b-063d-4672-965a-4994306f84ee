<template>
  <ul :class="scroll && 'scroll overflow-scroll overflow-x-hidden max-h-44'">
    <li class="guest-item mb-1" v-for="(guest, index) in guests" :key="index">
      <guestItem :guest="guest" :type="component" />
    </li>
  </ul>
</template>
<script>
import guestItem from "@/components/reservation/Guest.item.component";
export default {
  props: {
    guests: {
      type: Array,
      required: true,
      default: () => []
    },
    component: {
      type: String,
      default: "div"
    },
    scroll: {
      type: Boolean,
      default: false
    }
  },
  components: { guestItem }
};
</script>

<style lang="scss" scoped>
.scroll {
  scroll-behavior: smooth;
}
.guest-item:last-child {
  margin-bottom: 0;
}
</style>
