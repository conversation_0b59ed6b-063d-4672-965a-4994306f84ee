<template>
  <div
    class="guest-item rounded w-full"
    :class="{
      'border-green-400 border': isGuestValidated
    }"
  >
    <div class="flex">
      <div class="icon-wrapper flex m-auto p-2">
        <adult v-if="guest.pax_type === 'AD'" data-test="adult-guest-item" />
        <adult v-if="guest.pax_type === null" data-test="adult-guest-item" />
        <junior v-if="guest.pax_type === 'JR'" data-test="junior-guest-item" />
        <kid v-if="guest.pax_type === 'CH'" data-test="child-guest-item" />
        <baby v-if="guest.pax_type === 'BB'" data-test="baby-guest-item" />
      </div>
      <div
        data-test="guest-name"
        class="uppercase text-xs flex flex-col flex-grow justify-center m-auto text-left"
        :class="{
          'font-black text-black': isGuestValidated,
          'text-gray-400': !isGuestValidated
        }"
      >
        <span>
          {{ getGuestName(guest) || $t("reservations.free") }}
        </span>
      </div>
      <div
        v-if="isGuestValidated"
        class=" bg-green-400 p-2 flex flex-col items-center justify-center"
      >
        <completed />
      </div>
    </div>
  </div>
</template>
<script>
import adult from "@/assets/images/icons/adult.svg";
import junior from "@/assets/images/icons/junior.svg";
import kid from "@/assets/images/icons/kid.svg";
import baby from "@/assets/images/icons/baby.svg";
import completed from "@/assets/images/icons/completed.svg";
import getGuestName from "@/mixins/getGuestName";
import { mapState } from "vuex";
export default {
  name: "guestItem",
  mixins: [getGuestName],
  components: { adult, junior, kid, baby, completed },
  props: {
    guest: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      required: false,
      default: "div"
    }
  },
  computed: {
    ...mapState("brand", ["config"]),
    isGuestValidated() {
      return this.config.partial_checkin
        ? this.guest.validated
        : this.guest.processCompleted;
    },
    checkinText() {
      return (
        ((this.config.partial_checkin && !this.guest.validated) ||
          (!this.config.partial_checkin && !this.guest.processCompleted)) &&
        this.type == "button"
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.main-color-link {
  color: var(--bgColor);
}
svg > .svg-avatar {
  fill: none;
  stroke: #bebebe;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 1.5px;
}
</style>
