<template>
  <div class="reservationItem flex flex-row items-start" :style="cssVariables">
    <div class="icon-wrapper">
      <component :is="icon" />
    </div>
    <div class="info flex items-start pl-4 flex-col">
      <span class="uppercase text-xs font-bold text-gray-400 lineZero">{{
        label
      }}</span>
      <div class="item-text flex flex-row items-center">
        <span class="uppercase font-black text-sm">{{ name }}</span>
        <a
          class="uppercase font-black text-sm"
          v-if="linkName"
          href="#"
          @click.prevent="$emit('openGuests')"
          >{{ linkName }}</a
        >
      </div>
    </div>
  </div>
</template>
<script>
import dni from "@/assets/images/icons/identification-card.svg";
import users from "@/assets/images/icons/users.svg";
import secret from "@/assets/images/icons/secret.svg";
import bed from "@/assets/images/icons/bed.svg";
import plate from "@/assets/images/icons/plate.svg";
import checkin from "@/assets/images/icons/checkin.svg";
import checkout from "@/assets/images/icons/checkout.svg";
import cssVariables from "@/mixins/cssVariables";

export default {
  name: "reservationItem",
  components: { dni, users, secret, bed, plate, checkin, checkout },
  mixins: [cssVariables],
  props: {
    label: {
      type: String,
      required: true
    },
    name: {
      type: [String, Number],
      required: false,
      default: ""
    },
    icon: {
      type: String,
      default: "secret"
    },
    linkName: {
      type: String
    }
  }
};
</script>
<style lang="scss">
.reservationItem {
  padding-bottom: 0.6rem;
  a {
    color: var(--bgColor);
  }
}

.lineZero {
  line-height: 1;
}
.icon-wrapper {
  // overrides default icon-wrapper min-width
  min-width: 24px;
}
</style>
