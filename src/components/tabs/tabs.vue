<template>
  <div class="w-full flex flex-nowrap justify-between py-5 overflow-x-auto">

    <div
      v-for="(name, index) in itemsNames"
      :key="index"
      @click="tabClicked(index)"
      :style="{ borderColor: itemIndex === index ? brandColor: '', color:  itemIndex=== index? brandColor :'' }" 
      data-test="reservation_form"
      :class="{
        'flex p-2 flex-col w-full text-center select-none hover:cursor-none border-solid border-b pointer-events-none text-black':
          itemIndex === index,
        'flex p-2 flex-col w-full text-center select-none hover:border-gray-500 border-b border-gray-300 cursor-pointer':
          itemIndex !== index
      }"
    >
    <p class="text-sm lg:text-base">{{ name }}</p>
    </div>
  </div>
</template>
<script>

export default {
  name: "tabs",
  data: () => {
    return {
      itemIndex: 0,
      brandColor: ""
    };
  },
  props: {
    itemsNames: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  mounted(){
    this.brandColor = this.$store.getters["brand/mainColor"]
  },

  methods: {
    itemNames(items) {
      const activeIndexes = items.filter(item => item.active === "true");
      const toReturn = activeIndexes.find(item => {
        if (!this.savedNames.includes(item.name)) {
          this.savedNames.push(item.name);
          return true;
        }
      });
      return toReturn?.name ?? activeIndexes[0].name;
    },
    tabClicked: function(i) {
      this.itemIndex = i;
      this.$emit("tabToggle", i);
    }
  }
};
</script>