<template>
  <footer
    v-if="isReceptionMode"
    class="sticky bg-color z-50 bottom-0 flex items-center justify-between text-white w-full self-start px-8 py-8 h-16"
  >
    <div
      @click="goBack"
      class="p-2 self-center icon"
      :class="{ 'cursor-pointer': showBackButton }"
      data-test="goBackButton"
    >
      <back v-if="showBackButton" />
    </div>
    <div @click="openModal" class="p-2 self-center cursor-pointer"
    data-test="openModalButton"
    >
      <house />
    </div>
    <div  @click="logout" class="p-2 self-center cursor-pointer"
    data-test="logoutButton"
    >
      <exit v-if="showExitButton"/>
    </div>
  </footer>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex";
import { Auth } from "@aws-amplify/auth";
import clearAllStore from "@/utils/clearAllStore";
import redirect from "@/mixins/redirect";
import back from "@/assets/images/icons/back.svg";
import house from "@/assets/images/icons/house.svg";
import exit from "@/assets/images/icons/exit.svg";

export default {
  mixins: [redirect],
  components: { back, house, exit },
  computed: {
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", ["getSelectedGuest"]),
    ...mapGetters("app", ["isReceptionMode"]),
    ...mapState("brand", ["config"]),
    ...mapState("reservations", ["multipleRooms"])
  },
  data() {
    return {
      showBackButton: false,
      showExitButton: false
    };
  },
  created() {
    this.showBackButton = this.$route.meta.allowBack;
    this.showExitButton = this.$route.name === "Search"
  },
  watch: {
    $route: function(value) {
      this.showBackButton = value.meta.allowBack;
      this.showExitButton = this.$route.name === "Search"
    }
  },
  methods: {
    ...mapActions("modal", ["VISIBLE", "SET_NAME", "SET_TITLE"]),
    ...mapActions("reservations", ["CLEAR_STATE"]),
    ...mapActions("app", ["SET_PREVIOUS_COMPONENT"]),
    ...mapActions("loading", ["LOADING"]),
    async openModal() {
      await this.SET_NAME("exitOptIn");
      await this.SET_TITLE(this.$t("header.modalTitle"));
      await this.VISIBLE(true);
    },

    async logout() {
      await this.LOADING(true);
      if (this.isReceptionMode && (await Auth.currentUserInfo())) {
        try {
          await Auth.signOut();
        } catch (error) {
          await this.LOADING(false);
          console.error("Amplify error signing out", error);
        }
      }
      await clearAllStore(this.$store);

      return this.$router.push({ name: "Login" });
    },

    async goBack() {
      if (this.showBackButton) {
        const currentRoute = this.$router.currentRoute;
        if (
          currentRoute.name === "Status" &&
          this.isReceptionMode &&
          !this.multipleRooms
        ) {
          await this.LOADING(true);
          this.CLEAR_STATE();
          return this.redirect({ name: "Search" });
        }
        if (currentRoute.name === "Status" && this.config.partial_checkin) {
          await this.SET_NAME("goBackToReservationsView");
          await this.SET_TITLE(this.$t("header.modalTitle"));
          await this.VISIBLE(true);
          return;
        }
        if (currentRoute.meta.allowBack) {
          await this.SET_PREVIOUS_COMPONENT(currentRoute.name);
          return this.redirect({ name: currentRoute.meta.backRoute });
        } else {
          window.allowGoBack = true;
          this.$router.go(-1);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.icon {
  width: 35px;
}
</style>
