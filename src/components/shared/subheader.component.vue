<template>
  <div class="subheader pt-2 w-full" :class="{ 'z-40': !isLoading }"
>
    <div v-if="show" class="text-right uppercase text-xs">
      <span
        v-if="$route.name !== 'Confirmation'"
        @click="showReservationInfoModal"
        class="subheader-text break-all cursor-pointer text-xs"
        :style="{ color: this.mainColor }"
      >
        {{ $t("subheader.reservationInformation") }}</span
      >
    </div>

    <modal name="reservationInfo">
      <guestList :guests="guests" :scroll="hasScroll" />
    </modal>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapState } from "vuex";
import modal from "@/components/shared/modal.component";
import guestList from "@/components/reservation/GuestList.component";
export default {
  name: "subheader",
  components: { modal, guestList },
  data() {
    return {
      show: false
    };
  },
  computed: {
    ...mapState("reservations", ["reservationSelected", "data"]),
    ...mapState("guest", { guestList: "list" }),
    ...mapGetters("brand", ["mainColor"]),
    guests() {
      return this.guestList || [];
    },
    hasScroll() {
      return this.guests.length > 3;
    },
    isLoading() {
      return this.$store.state.loading.loading;
    },
    shouldShow() {
      return this.$route.matched[0].name === "ReceptionMode" ?
      this.$route.meta?.step?.() > 2 && this.reservationSelected :
      this.$route.meta?.step?.() > 3 && this.reservationSelected;
    }
  },
  created() {
    this.show = this.shouldShow
  },
  watch: {
  $route: function() {
    this.show = this.shouldShow
  }
},
  methods: {
    ...mapActions("modal", {
      VISIBLE: "VISIBLE",
      SET_TITLE: "SET_TITLE",
      SET_TYPE: "SET_TYPE",
      SET_BUTTON_MESSAGE: "SET_BUTTON_MESSAGE",
      SET_NAME: "SET_NAME",
      CLEAR_MODAL_STATE: "CLEAR_STATE"
    }),
    async showReservationInfoModal() {
      await this.SET_TYPE("info");
      await this.SET_NAME("reservationInfo");
      await this.SET_TITLE(this.$t("subheader.reservationInformation"));
      await this.SET_BUTTON_MESSAGE(
        this.$t("searchFormComponent.reservation_code.modal.button")
      );
      await this.VISIBLE(true);
    }
  }
};
</script>
<style lang="scss" scoped>
.subheader {
  min-height: 2.5rem;
}
.subheader-text {
  @media (max-width: 500px) {
    word-spacing: 100vw;
  }
}
</style>
