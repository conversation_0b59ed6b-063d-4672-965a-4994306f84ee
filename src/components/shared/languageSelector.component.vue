<template>
  <div class="h-8">
    <div
      class="cursor-pointer w-full"
      @click="toggleShowLangOptions"
      v-click-outside="hideLangOptions"
    >
      <span class="fi">
        <component :is="currentFlagComponent"/>
      </span>
      <span class="align-text-bottom ml-1">⌄</span>
    </div>
    <transition name="bounce">
      <div
        v-if="showLangOptions"
        class="fixed shadow bg-white text-gray-400 flex flex-col px-4 mt-2 py-2 rounded cursor-pointer"
      >
        <div
          v-for="lang in langs"
          :key="`Lang-${lang.iso2}`"
          class="flex flex-row mt-1"
          @click="handleSelectLanguage(lang)"
        >
          <span class="fi">
            <component :is="getFlagComponent(lang.flag)"/>
          </span>
          <span class="ml-2">{{ $t(`languages.${lang.iso2}`) }}</span>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { mapActions, mapState, mapGetters } from "vuex";
import VueClickaway from "vue-clickaway";
import { flagMap } from "@/assets/flags/flagsList.js"

export default {
  name: "languageSelector",
  async created() {
    if(this.queryParam.lang){
    const langQueryParam = this.queryParam.lang;
    const acceptedLanguages = Object.keys(this.$i18n.messages);
    const isValidLanguage = acceptedLanguages.includes(langQueryParam);

    const language = isValidLanguage ? langQueryParam : 'en';
    this.$root.$i18n.locale = language;
    await this.SET_APP_LANGUAGE(this.$i18n.locale);
    }
  },
  data() {
    return {
      showLangOptions: false,
    };
  },
  directives: {
    'click-outside': VueClickaway.directive
  },
  computed:{
    ...mapState("queryParams", { queryParam: "data" }),
    ...mapGetters("brand", [ "langs" ]),

    flagSelected(){
      return this.langs.find(lang => lang.iso2 == this.$i18n.locale)?.flag ?? "gb"
    },

    currentFlagComponent()  {
      return flagMap[this.flagSelected] || flagMap.en;
    },
  },
 
  methods: {
    ...mapActions("app", ["SET_APP_LANGUAGE"]),
    ...mapActions("loading", ["LOADING"]),
    getFlagComponent(flag){
      return flagMap[flag]
      
    },
    toggleShowLangOptions(){
      this.showLangOptions = !this.showLangOptions
    },
    hideLangOptions() {
      this.showLangOptions = false;
    },
    async handleSelectLanguage(lang) {
      this.$root.$i18n.locale = lang.iso2;
      this.flagSelected = lang.flag;
      this.showLangOptions = false;
      await this.SET_APP_LANGUAGE(this.$i18n.locale);

      // TODO: Remove Validate data reload (Fix select options traslations on language change)
      if (["ValidateData", "Confirmation"].includes(this.$root.$route.name)) {
        await this.LOADING(true);

        return await this.$router.go(this.$router.currentRoute);
      }
    }
  }
};
</script>
