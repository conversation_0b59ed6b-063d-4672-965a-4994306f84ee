<template>
  <footer class="bg-gray-100 w-full self-start px-8 py-2 flex items-center justify-between">
        <div class="flex items-center"> 
          <qrCodeIcon class="h-4 w-4 text-black" v-if="reservationSelected && reservationSelected.res_localizer" />
          <div class="text-xs ml-4" v-if="reservationSelected && reservationSelected.res_localizer">
              <p>{{ $t("footer.reservation") }}</p>
              <p class="uppercase font-bold">{{ reservationSelected.res_localizer }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <div class="text-xs mr-4 text-right">
              <p>{{ $t(`footer.${$route.meta.stepName}Step`) }}</p>
              <p class="text-xs uppercase font-bold">
                {{ $route.meta.step() }} / {{ $route.matched[0].meta.totalSteps() }} 
              </p>
          </div>
          <bookmarkSquareIcon class="h-4 w-4 text-black" />
        </div>
    </footer>
</template>

<script>
import { mapState, mapGetters } from "vuex";
import qrCodeIcon from "@/assets/images/icons/qrcode.svg";
import bookmarkSquareIcon from "@/assets/images/icons/bookmarkSquare.svg";
export default {
  computed: {
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", ["getSelectedGuest"])
  },
  components: {
    bookmarkSquareIcon,
    qrCodeIcon
  },
};
</script>

<style lang="scss" scoped>
footer {
  // this tailwind version does not have this viewport
  @media only screen and (min-width: 1920px) {
    padding-left: 35rem;
    padding-right: 35rem;
  }
}
</style>
