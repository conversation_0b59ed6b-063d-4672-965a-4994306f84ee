<template>
  <div v-show="showDemoHeader" class="flex items-center justify-between bg-orange-300 text-white border-b w-full self-start px-8 py-2" data-test="demoHeader">
    <span class="px-1 py-1" >{{ $t("demoHeader.text") }}</span>
    <button class="border rounded px-4 py-1" @click="toggleDemoMode" data-test="demoToggleButton">{{ $t("demoHeader.button") }}</button>
  </div>
</template>

<script>
import clearStoreAndRedirect from "../../mixins/clearStoreAndRedirect";
import { mapGetters, mapActions, mapState } from "vuex";
export default {
  mixins: [clearStoreAndRedirect],
  methods:{
    ...mapActions("app", ["TOGGLE_DEMO_MODE"]),
    ...mapActions("brand", ["SET_BRAND_ID"]),
    async toggleDemoMode(){
      this.TOGGLE_DEMO_MODE(false)
      await this.clearStoreAndRedirect(this.parentBrand);
      location.reload() // Reloading the app is necessary to toggle the msw (Mock Service Worker) functionality on/off.
    }
  },
  computed: {
    ...mapGetters("app", ["isDemoMode"]),
    ...mapState("trace", ["parentBrand"]),
    ...mapState("brand", ["brandId"]),
    showDemoHeader(){
      return this.isDemoMode
    },
  }
};

</script>