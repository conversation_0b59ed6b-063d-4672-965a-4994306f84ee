<template>
  <div
    data-test="modalError"
    class="modal flex h-screen w-screen fixed items-center justify-center left-0"
    v-show="$store.state.modal.show && $store.state.modal.name === name"
  >
    <transition name="bounce">
      <div
        class="modal-content rounded bg-white w-10/12 px-4 relative pb-4 pt-12"
        v-show="$store.state.modal.show"
      >
        <error v-show="type === 'error'" class="modal-icon" />
        <information v-show="type === 'info'" class="modal-icon" />
        <tick v-show="type === 'success'" class="modal-icon" />
        <h2 class="font-black text-xl my-4">{{ $t(`${title}`) }}</h2>
        <p class="modal-body" data-test="modalBody">
          <slot />
        </p>

        <div v-if="button" class="flex justify-center">
          <button
            :style="cssVariables"
            @click="closeModal"
            data-test="closeModal"
          >
            {{ buttonMessage == "" ? $t("modal.button") : buttonMessage }}
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
import cssVariables from "@/mixins/cssVariables";
import error from "@/assets/images/icons/error.svg";
import information from "@/assets/images/icons/informacion.svg";
import tick from "@/assets/images/icons/tick.svg";
import { mapActions, mapState } from "vuex";

export default {
  name: "modal",
  components: { error, information, tick },
  mixins: [cssVariables],
  props: {
    name: {
      type: String,
      default: "default"
    },
    button: {
      type: Boolean,
      default: true
    }
  },

  methods: {
    ...mapActions("modal", ["CLEAR_STATE"]),

    async closeModal() {
      this.$emit("closeModal", this.name);
      await this.CLEAR_STATE();
    }
  },
  computed: {
    ...mapState("modal", ["title", "type", "buttonMessage"])
  }
};
</script>
<style lang="scss" scoped>
.modal {
  top: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 2002;

  .modal-icon {
    background-color: white;
    position: absolute;
    left: 50%;
    margin-left: -40px;
    top: -40px;
    border-radius: 50%;
    border: 4px solid white;
  }

  .modal-content {
    max-width: 600px;
    max-height: 100vh;
  }

  .modal-body {
    overflow-y: auto;
    max-height: 60vh;
  }

  button {
    @apply font-black py-2 px-4 rounded mt-8 text-white uppercase;
    background-color: var(--bgColor);

    &:hover {
      background-color: var(--darkenBgColor);
    }
  }
}
</style>
