<template>
  <div
    v-if="!isReceptionMode"
    class="go-back-button"
    @click="goBack"
    data-test="go-back"
  >
    {{ text }}
  </div>
</template>
<script>
import { mapState, mapGetters, mapActions } from "vuex";
import redirect from "@/mixins/redirect";

export default {
  name: "goBackLink",
  props: {
    text: {
      type: String,
      default: ""
    },
    goBackModal: {
      type: Boolean,
      default: false
    },
    showLoading: {
      type: Boolean,
      default: false
    }
  },
  mixins: [redirect],
  computed: {
    ...mapState("brand", ["config"]),
    ...mapGetters("app", ["isReceptionMode"])
  },
  methods: {
    ...mapActions("reservations", ["CLEAR_STATE"]),
    ...mapActions("loading", ["LOADING"]),
    async goBack() {
      const currentRoute = this.$router.currentRoute;
      if (this.showLoading) {
        await this.LOADING(true);
      }

      if (this.text === this.$t("shared.goToSearch")) {
        this.CLEAR_STATE();
        return this.redirect({ name: "Search" });
      }
      if (this.goBackModal) {
        this.$emit("openGoBackModal");
        return;
      }
      if (currentRoute.meta.allowBack) {
        return this.redirect({ name: currentRoute.meta.backRoute });
      } else {
        window.allowGoBack = true;
        this.$router.go(-1);
      }
    }
  }
};
</script>
