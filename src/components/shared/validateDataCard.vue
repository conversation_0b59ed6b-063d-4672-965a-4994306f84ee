<template>
    <div class="lg:col-start-3 lg:row-end-1 my-5" v-if="inputs.length > 0">
      <div class="rounded-md bg-gray-50 shadow-sm ring-1 ring-gray-900/5 flex flex-col">
        <div class="flex-auto pl-4 py-3 border-b border-gray-900/5">
          <p id="title" class="text-sm md:text-lg font-bold leading-6 text-gray-900 uppercase">{{ $t('validatedata.card.title') }}</p>
        </div>
        <div v-for="(item, index) in shownInputs" :key="index" class="flex-auto md:py-1">
          <div class="mt-1 flex w-full flex-none gap-x-4 px-4">
            <p class="text-xs md:text-sm font-semibold leading-6 text-gray-900">{{ $t(`validatedata.${item.name}`)}}</p>
          </div>
          <div class="flex w-full flex-none gap-x-4 px-4">
            <p class="text-xs font-medium leading-6 text-gray-900">{{ item.value }}</p>
          </div>
        </div>
        <div class="flex justify-between mt-2 border-t border-gray-900/5 px-4 py-2">
          <p id="data" class="text-xs md:text-sm font-semibold leading-6 text-gray-900 uppercase">
            {{`+ ${inputs.length - shownInputs.length } ${$t('validatedata.card.data')}`}}</p>
            <p id="toggleCard" class="text-xs md:text-sm font-semibold leading-6 text-gray-900 cursor-pointer uppercase" @click="toggleCard"
            data-test="showAllButton"
            >{{ $t('validatedata.card.toggle') }}</p>
          </div>
        </div>
      </div>
    </template>

<script>
export default {
  name: "validateDataCard",
  props: {
    inputs: {
      default: []
    },
  },

  computed:{
    shownInputs(){
      const filteredInputs = this.inputs?.filter(input => input.active === "true" && !input.hasError && input.value).slice(0, 4);
      return filteredInputs?.map(input=>{
        if(input.name == "gender"){
          input.options.forEach(option=>{
            if(option.value == input.value){
              input.value = option.name
            }
          })
        }
        return input;
      });
    },
    unshownInputs(){
      return this.inputs.filter(input => {
        if (input.active === "true") {
          return input.hasError || !input.value;
        }
      });
    },
  },
  methods: {
    toggleCard() {
      this.$emit("cardToggle");
    },
  },
}
</script>
