<template>
  <button
    type="button"
    :style="cssVariables"
    :class="
      `btn text-${textColor} font-bold py-3 px-5 rounded  shadow-lg focus:outline-none uppercase bg-${color}-600 hover:bg-${color}-400 ${small ? 'w-auto' : 'w-full'}`
    "
    :disabled="disabled"
  >
    <slot />
  </button>
</template>
<script>
import cssVariables from "@/mixins/cssVariables";
export default {
  name: "btn",
  mixins: [cssVariables],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: null
    },
    textColor: {
      type: String,
      default: "white"
    },
    small: {
      type: Boolean,
      default: false
    }
  }
};
</script>
<style lang="scss">
.btn {
  background-color: var(--bgColor);
  &:hover {
    background-color: var(--darkenBgColor);
  }
  &:disabled {
    opacity: 0.2;
    cursor: default;
    &:hover {
      background-color: var(--bgColor);
    }
  }
}
</style>
