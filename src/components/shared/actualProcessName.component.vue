<template>
  <div
    class="actual-process-name flex flex-col uppercase text-xs absolute pt-2 text-right"
  >
    <span>{{ $t("actualProcessName.name") }}</span>
    <span data-test="guest-name" class="font-black">
      {{ $store.state.guest.data.name || "N/A" }}
    </span>
  </div>
</template>
<script>
export default {
  name: "actualProcessName"
};
</script>
<style lang="scss" scoped>
.actual-process-name {
  text-align: right;
  right: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 170px;
}
</style>
