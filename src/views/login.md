---
title: Login
layout: layout.html
orderPath: /__index
eleventyNavigation:
  key: Login
  parent: Pages
  order: 1
---

# Login

The login page is accessible only when it is a reception autocheckin. Moreover, it is the only page that is not preceded by the brand_id in the path, since it is not yet known in which hotel you are going to log in.

To show the login form we use the components provided by the [@aws-amplify/ui-components](https://github.com/aws-amplify/amplify-ui/tree/legacy/legacy/amplify-ui-vue) library. We have used all forms of customization to make it as similar as possible to the other forms in the application.


We have a listener that notifies us every time there is a change in the login status. When we detect that you have signed in we save all the brands to which this account has access and redirect you to the first of the options, loading [Index view](/src/views/)

```js
this.unsubscribeAuth = onAuthUIStateChange(async (authState, authData) => {
  this.authState = authState;
  this.user = authData;

  if (this.authState == "signedin") {
    const authorizedBrands = this.user.attributes["custom:brand_id"]?.split(
      ","
    );

    await this.SET_AUTHORIZED_BRANDS(authorizedBrands);

    this.$router.push({
      path: `/${authorizedBrands[0]}`
    });

    return this.unsubscribeAuth();
  }
});
```