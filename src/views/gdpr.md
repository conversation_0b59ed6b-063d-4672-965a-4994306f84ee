---
title: GDPR
layout: layout.html
eleventyNavigation:
  key: GDPR
  parent: Pages
  order: 3
---

# GDPR

## GDPR State Diagram

How the GDPR texts are displayed when user enters in _Autocheckin_ platform.

```mermaid
stateDiagram-v2
state "User arrives to Autocheckin platform" as ACI
state "GDPR View" as GDPR
state "Default GDPR text" as Default
state "EPrivacy GDPR text" as EPrivacy
state "Custom GDPR text" as Custom
state customTexts <<choice>>
state eprivacyTexts <<choice>>
state routeState <<choice>>
ACI --> routeState
    routeState --> GDPR: Autocheckin product is enabled and is working
    GDPR --> customTexts: App has...
    customTexts --> eprivacyTexts: Disabled custom gdpr text
    eprivacyTexts --> Default: Disabled eprivacy texts
    eprivacyTexts --> EPrivacy: Enabled eprivacy texts
    customTexts --> Custom: Enabled custom gdpr text
    Default --> [*]
    EPrivacy --> [*]
    Custom --> [*]
    routeState --> Error: Autocheckin product is disabled, or error
    Error --> [*]
```

The EPrivacy texts are meant to be shown in Captive Portal and Autocheckin by default, if enabled.
Enabling custom gdpr text will override the ePrivacy texts in the Autocheckin platform.
