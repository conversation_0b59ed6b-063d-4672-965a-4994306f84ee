<template>
  <div class="share main-content main-content-white">
    <div class="content h-full flex flex-col">
      <div class="flex-grow">
        <title-component>{{ $t("share.title") }}</title-component>
        <p class="text-gray-800 mb-4">
          {{ $t("share.text") }}
        </p>
        <div class="url-input relative">
          <transition name="fadeUp">
            <span
              v-show="tooltipVisible"
              style="animation-duration: 0.1s"
              class="absolute right-0 bottom-10 bg-black px-4 py-2 text-white rounded uppercase text-xs font-black"
              >{{ $t("share.copied") }}</span
            >
          </transition>
          <label>
            <span class="text-gray-700 font-black uppercase text-sm">{{
              $t("share.click")
            }}</span>
            <span
              class="text-gray-400 font-black uppercase text-xs float-right mt-1.5"
              >{{ $t("share.optional") }}</span
            >
            <input
              type="text"
              class="rounded block w-full p-4 border-2 border-gray-200 focus:outline-none"
              :value="this.shortUrl || this.url"
              @click="copyToClipboard"
            />
          </label>
        </div>
        <ul class="mt-6 text-sm">
          <li>
            <info /> <span>{{ $t("share.validLinktext") }}</span>
          </li>
          <li>
            <info /> <span>{{ $t("share.shareAdviceText") }}</span>
          </li>
        </ul>
        <go-back-link 
        :text="$t('shared.previousStep')"
        data-test="backToConfirmation"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from "vuex";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import redirect from "@/mixins/redirect";
import goBackLink from "@/components/shared/goBackLink.component";
import browserHistoryManagement from "@/mixins/browserHistoryManagement";
import titleComponent from "@/components/shared/title.component";
import repository from "../repository/repositoryFactory";
import info from "@/assets/images/icons/informacion.svg";
const api = repository.get("shortener");

export default {
  name: "share",
  data() {
    return {
      url: window.location.origin,
      shortUrl: null,
      tooltipVisible: false
    };
  },
  components: { titleComponent, info, goBackLink },
  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect],
  computed: {
    ...mapState("reservations", ["dataRequested"]),
    ...mapGetters("reservations", ["getReservationCompleted"]),
    ...mapState("brand", ["brandId", "config"])
  },
  async created() {
      try {
        this.url += this.createShareUrl();
        const response = await api.getShortUrl(this.url);
        this.shortUrl = `https://${response.data.id}`;
      } catch (e) {
        console.error("Error from Shortener API", e);
      } finally {
        this.stopLoading();
    }
  },

  methods: {
    async copyToClipboard() {
      await navigator.clipboard.writeText(this.shortUrl || this.url);
      this.tooltipVisible = true;

      setTimeout(() => {
        this.tooltipVisible = false;
      }, 1500);
    },
    createShareUrl() {
      let searchData = {};
      this.dataRequested.forEach(input => {
        input.forEach(element => {
          if (element.value) {
            searchData[element.name] = element.value;
          }
        })
      });

      const dataRequested = new URLSearchParams(searchData);
      const queryParams = Object.keys(this.$route.query)
        .map(key => {
          if (key !== "token") {
            return `${key}=${this.$route.query[key]}`;
          }
        })
        .join("&");
      if (queryParams) {
        return `/${this.brandId}?${queryParams}&${dataRequested.toString()}`;
      } else {
        return `/${this.brandId}?${dataRequested.toString()}`;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
li {
  @apply mb-4 flex flex-row items-center;
  svg {
    @apply mr-4;
    width: 40px;
    height: 40px;
  }
}
</style>
