<template>
  <div class="search main-content main-content-white">
    <modal name="searchModal"><p v-html="modalMessage"></p></modal>
    <div class="content h-full flex flex-col">
      <title-component>{{ searchTitle }}</title-component>
      <p class="text-gray-800 mb-4 search-text">
        {{ searchText }}
      </p>
      <div class="flex-1">

        <tabs
          v-if="tabNames.length > 1 && !hasMoreReservations"
          :itemsNames="tabNames"
          @tabToggle="onTabClicked"
        />
        <div
          class="flex flex-col"
          v-for="(inputGroup, keyGroup) in inputGroupSelected"
          :key="keyGroup"
        >
          <search-form
            v-if="currentGroup === keyGroup"
            :key="`form-${keyGroup}-${currentGroup}`"
            :ref="`form-${keyGroup}`"
            class="flex-grow"
            :inputs="inputGroup"
            :formGroup="keyGroup"
            @handleFormChanged="handleChangeForm"
            @child-form-submitted="submitAvailable ? submit(inputGroupSelected) : ''"
            :timeLimitCheckin="timeLimitCheckin"
            :closeTimeLimitCheckin="closeTimeLimitCheckin"
            :allowTimeLimit="allowTimeLimit"
          />
        </div>
      </div>
      <btn
        @click.native="submit(inputGroupSelected, {isManual: true})" 
        :disabled="!submitAvailable"
        data-test="searchButton"
      >
        {{ $t("search.button") }}
      </btn>
    </div>
  </div>
</template>
<script>
import tabs from "@/components/tabs/tabs.vue";
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import { mapGetters, mapActions, mapState } from "vuex";
import titleComponent from "@/components/shared/title.component";
import searchForm from "@/components/search/searchForm.component";
import repository from "../repository/repositoryFactory";
import btn from "@/components/shared/button.component";
import modal from "@/components/shared/modal.component";
import { cloneDeep } from "lodash";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import checkinAvailability from "../mixins/checkinAvailability";
import redirect from "@/mixins/redirect";
import { receptionInputs } from "../utils/receptionInputs";
import { nextTick } from "vue";
import * as CryptoJS from 'crypto-js';
import { Tracker } from "@/tracker";
const api = repository.get("integration");

export default {
  name: "Search",
  components: { titleComponent, searchForm, btn, modal, tabs },
  data() {
    return {
      currentGroup: 0,
      form: {},
      enableButton: false,
      inputGroupSelected: [],
      isErrorMessage: false,
      modalMessage: null,
      timezone: "",
      timeLimitCheckin: null,
      closeTimeLimitCheckin: null,
      allowTimeLimit: false,
      submittedInputs: [],
      formSelected: 0,
      savedNames: [],
      tabInputsGroupSelected: [],
      tabNames: [],
      items: [],
      reservationErrorMessage: {
        NOT_FOUND: this.$t('search.reservationNotFound'),
        BAD_INTEGRATION: this.$t('search.integrationError'),
        GROUP_RESERVATION: this.$t('search.groupReservationError'),
      },
    };
  },
  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect, checkinAvailability],

  computed: {
    ...mapState("reservations", {
      hasMoreReservations: "hasMoreReservations",
      reservations: "data",
      attempts: "attempts",
      filterAttempts: "filterAttempts",
      reservationSelected: "reservationSelected"
    }),
    ...mapState("loading", ["loading"]),
    ...mapState("brand", ["brandId", "config"]),
    ...mapState("trace", { trace_id: "id" }),
    ...mapState("queryParams", { queryParam: "data" }),
    ...mapGetters("reservations", [
      "getInputFilterForm",
      "getReservationCompleted"
    ]),
    ...mapGetters("app", ["isReceptionMode"]),

    searchTitle() {
      return this.hasMoreReservations
        ? this.$t("search.foundTitle", { count: this.reservations?.length }) +
            " " +
            this.$tc("search.booking", this.reservations?.length)
        : this.$t("search.title");
    },

    searchText() {
      return this.hasMoreReservations
        ? this.$t("search.foundText")
        : this.$t("search.text");
    },

    submitAvailable() {
      return this.isFormDataCompleted(this.inputGroupSelected[this.formSelected])
    },
  },
  async created() {
    Tracker.recordCustomEvent("reservation_start", {});

    //If in storage have reservation go directly in to Reservation view
    if (this.reservationSelected) {
      await this.LOADING(true);
      return this.redirect({ name: "Reservations" });
    }

    try {
      this.form = {
        reservation_filters: this.sortItemsByPositionIndex(
          cloneDeep(this.config.identification.reservation_filters)
        ),
        reservation_inputs: cloneDeep(
          this.config.identification.reservation_inputs
        )
      };

      this.timezone = this.$store.state.brand.timeZone;
      this.timeLimitCheckin = this.config.time_limit_checkin;
      this.closeTimeLimitCheckin = this.config.close_time_limit_checkin;
      this.allowTimeLimit = this.config.activate_time_limit
    } catch (error) {
      console.error("Error on search cloning configuration", {
        error: error.message,
        config: this.config
      });

      return this.redirect({ name: "Error" });
    }

    if (this.isReceptionMode) {
      // TODO: Inputs for reception are now hardcoded but this will probably be changed
      this.inputGroupSelected = cloneDeep(receptionInputs);
    } else {
      this.inputGroupSelected = this.form.reservation_inputs;
      this.SET_INPUTS_FORM(this.inputGroupSelected);
    }

    if (this.$route.params.reservation) {
      await this.submit(this.$route.params.reservation, {isQueryParam: true});
    }

    await this.manageQueryParams(this.inputGroupSelected);
    this.tabInputsGroupSelected = cloneDeep(this.inputGroupSelected);
    this.stopLoading();
    this.tabInputsGroupSelected.forEach(input => {
      this.tabNames.push(this.$t(`searchFormComponent.${this.itemNames(input)}.key`) );
    });
  },
  watch: {
		"$i18n.locale": {
			handler(newValue, oldValue) {
				if (newValue !== oldValue) {
          this.savedNames = [];
          this.tabNames = [];
					this.tabInputsGroupSelected.forEach(input => {
            this.tabNames.push(this.$t(`searchFormComponent.${this.itemNames(input)}.key`) );
          });
				}
			},
			immediate: true,
		},
	},

  methods: {
    ...mapActions("reservations", [
      "SET_INPUTS_FORM",
      "ADD_FILTER_ATTEMPT",
      "SET_RESERVATION_SELECTED",
      "ADD_ATTEMPT",
      "SET_RESERVATIONS",
      "SET_HAS_MORE_RESERVATIONS",
      "SET_MULTIPLE_ROOMS",
      "SET_DATA_REQUESTED",
      "SET_RESERVATION_CHECKIN_COMPLETE",
      "SET_CHECK_STATE",
      "SET_ERROR"
    ]),
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("app", ["SET_APP_LANGUAGE"]),
    ...mapActions("guest", ["SET_GUEST_TO_LIST"]),
    ...mapActions("queryParams", { CLEAR_QUERY_PARAMS: "CLEAR_STATE"}),

    isFormDataCompleted(formData) {
      return formData?.filter(input => input.active === "true")?.every(input => input.value && !input.error);
    },

    itemNames(items) {
      const activeIndexes = items.filter(item => item.active === "true");
      const activeIndexName = activeIndexes.find(item => {
        if (!this.savedNames.includes(item?.name)) {
          this.savedNames.push(item?.name);
          return true;
        }
      });
      return activeIndexName?.name ?? activeIndexes[0].name;
    },
    onTabClicked(tab) {
      this.currentGroup = tab;
    },
    handleChangeForm(keyGroup) {
      this.formSelected = keyGroup;
      let auxInputs = cloneDeep(this.inputGroupSelected);
      auxInputs.forEach((inputGroup, key) => {
        if (this.formSelected !== key) {
          inputGroup.forEach(input => {
            input.value = "";
          });
          // If the form reference exists, iterate through all its child components' references
          // and check if they have a `resetInput` method. If they do, call it to reset their values.
          if (this.$refs[`form-${key}`] && this.$refs[`form-${key}`][0]) {
          Object.values(this.$refs[`form-${key}`][0].$refs || {}).forEach(item => {
           if (item && item[0] && typeof item[0].resetInput === "function") {
            item[0].resetInput();
              }
          });
          }
        }
      });
      this.inputGroupSelected = cloneDeep(auxInputs);
    },
    sortItemsByPositionIndex(items) {
      return items.sort((a, b) => a[0].position - b[0].position);
    },

    async setModalMessage(errorMessage) {
      this.isErrorMessage = true;
      this.modalMessage = errorMessage || this.reservationErrorMessage.BAD_INTEGRATION;

      await this.$store.dispatch("modal/SET_NAME", "searchModal");
      await this.$store.dispatch("modal/VISIBLE", true);
      this.ADD_ATTEMPT();
    },
    
    async submit(formData, { isQueryParam = false, isManual = false }) {
    const errorInputs = formData[this.currentGroup].filter(input => {
        return (
          input.active === "true" && !input.value
        );
      });

      errorInputs.forEach(input => {
        const childComponent = this.$refs[`form-${this.currentGroup}`][0].$refs[input.name][0].$refs?.component;
       
        childComponent.inputChanged(input.value || " ");
      })

      if (!this.isFormDataCompleted(formData[this.formSelected])){
        return
      }
      await this.LOADING(true);
      if (!this.hasMoreReservations && !this.reservations) {
        try {
          
          // Filter formData with only selected fields 
          let reservations = await this.sendRequest(formData, isManual); 
          if (reservations === "invalid"){
            await this.LOADING(false);
            return
          }
          if (reservations.length === 0) {
            this.setModalMessage(this.reservationErrorMessage.NOT_FOUND);
          } else { 
              if(isQueryParam ||formData.length === 1){
                // Selected form is always first one on searching by queryParams.
                formData[0].forEach(input =>{
                  this.submittedInputs.push(input)
                })
                
              }else{
                // Get the fields that the guest is submitting, based on the selected form, to avoid prompting the same fields again if filters are needed
                formData[this.formSelected].forEach(input => {
                  if (input.active == "true") {
                    this.submittedInputs.push(input);
                  }
                });
              }
              // Save result
              this.SET_RESERVATIONS(reservations);
              await this.checkResponse(reservations);       
            }
        } catch (error) {
          const errorCode = error?.error?.code;
          const alreadyOnPrecheckErrorCode = "INT_3_2"; // When reservation is already on pre-checkin state
          const groupReservationErrorCode = "INT_3_10"
          if (errorCode === alreadyOnPrecheckErrorCode || error?.message === "alreadyChecked") {
            this.SET_ERROR(error.error)
            return this.redirect({
              name: "Confirmation"
            });
          } else if (errorCode === groupReservationErrorCode){
            this.SET_ERROR(error.error)
            this.setModalMessage(this.reservationErrorMessage.GROUP_RESERVATION);
            return this.stopLoading();
          }
          console.error("Error on submit form in search", error);        
          this.setModalMessage(this.reservationErrorMessage.BAD_INTEGRATION);
        }
      } else {
        if (this.filterAttempts < this.form.reservation_filters.length - 1) {
          this.ADD_FILTER_ATTEMPT();
        }
        let reservationsFiltered
        
        if(isQueryParam){
          reservationsFiltered = this.filterReservationsWithTiebreakerInputValue(this.sendRequest(formData, isManual));
        } else{
          reservationsFiltered = this.filterReservationsWithTiebreakerInputValue(formData[this.formSelected][0])         
        }

        if (reservationsFiltered.length === 0) {
          this.modalMessage = this.$t("search.reservationNotFound");
          await this.$store.dispatch("modal/SET_NAME", "searchModal");
          await this.$store.dispatch("modal/VISIBLE", true);
        } else {
          await this.checkResponse(reservationsFiltered);
        }
      }
      this.stopLoading();
    },

    async sendRequest(inputGroupSelected, isManual) {
       this.SET_ERROR(null)
       //Create request object to send integrations api
      this.SET_DATA_REQUESTED(inputGroupSelected);
      const requestObject = this.createRequestObject(inputGroupSelected, isManual);
      // Send request
      const reservationResults = await api.getReservation(this.brandId, requestObject);
      if(Array.isArray(reservationResults)){
        if (this.isReceptionMode) {
        const [reservation] = reservationResults;
        // Identify the language of the holder
        let holderLang = reservation?.guests[0].lang || "en"
        await this.handleSelectLanguage(holderLang)
      }
        return reservationResults
      } else if (reservationResults.action === "redirect"){
        this.SET_RESERVATION_SELECTED(reservationResults.reservation[0])
        this.SET_GUEST_TO_LIST(reservationResults.reservation[0].guests);
        this.SET_RESERVATION_CHECKIN_COMPLETE(true)
        this.SET_CHECK_STATE(reservationResults.checkState)
        throw new Error('alreadyChecked');
      } else if (reservationResults.action === "show modal"){
        this.modalMessage = this.$t("search.invalidReservation", {
            checkIn: reservationResults.reservation[0].check_in,
            checkOut: reservationResults.reservation[0].check_out
        });
        await this.$store.dispatch("modal/SET_NAME", "searchModal");
        await this.$store.dispatch("modal/VISIBLE", true);
        return "invalid";
      }
    
    },

    async handleSelectLanguage(lang) {
      this.$root.$i18n.locale = lang;
      await this.SET_APP_LANGUAGE(this.$i18n.locale);
    },

    createRequestObject(inputs, isManual) {
      let requestObject = {
        cache: !this.isReceptionMode,
        manual: isManual
      };
      // Loop inputGroup and construct requestOjects
      inputs.forEach(inputGroups => {
        requestObject = {
          ...requestObject,

          ...this.parseForm(inputGroups)
        };
      });
      return requestObject;
    },

    parseForm(params) {
      //Get array of params and return object with key value
      let paramsToReturn = {};
      params.forEach(param => {
        if (param.value) {
          paramsToReturn[param.name] = param.value;
        }
      });
      return paramsToReturn;
    },

    filterReservationsWithTiebreakerInputValue(inputGroupSelected) {
      if (this.reservations){
        switch (inputGroupSelected.name) {
          case "reservation_code":
          // The input name is reservation_code, but reservations have res_localizer
            return this.reservations.filter(reservations => reservations["res_localizer"]?.toLowerCase() === inputGroupSelected.value?.toLowerCase()
            );
          case "first_name":
            return this.reservations.filter(reservation => {
              const filteredGuest = reservation.guests.filter( guest => guest?.name?.toLowerCase() == inputGroupSelected?.value?.toLowerCase());
            // If filteredGuest is empty, it means that the guest is not in the reservation
              return filteredGuest.length > 0;
            });
          case "check_in" :
            return this.reservations.filter(reservation => reservation["check_in"]?.toLowerCase() === inputGroupSelected.value?.toLowerCase()
            );  
          case "check_out": 
            return this.reservations.filter(reservation => reservation["check_out"]?.toLowerCase() === inputGroupSelected.value?.toLowerCase()
            );
          default: 
            return this.reservations.filter(reservations => reservations[inputGroupSelected.name]?.toLowerCase() === inputGroupSelected.value?.toLowerCase())}
      }
    },

    async checkResponse(response) {
      // If response of reques have more than one reservation activate flag
      // And search reservation_filters
      if (response.length > 1) {
        try {
          const isSameReservation = response.every(function(reservation) {
            return (
              reservation.res_localizer?.toLowerCase() ===
                response[0].res_localizer?.toLowerCase() &&
              reservation.check_in === response[0].check_in &&
              reservation.check_out === response[0].check_out
            );
          });
  
          if (isSameReservation) {
            // Set reservation selected
            if (response[0]) {
              this.SET_MULTIPLE_ROOMS(true);
              // Check if checkin is available
              const isCheckinAvailable = this.isCheckinAvailable(
                response[0].check_in,
                this.timeLimitCheckin,
                this.closeTimeLimitCheckin,
                this.timezone
              );

              if (!isCheckinAvailable.available) {
                this.modalMessage = isCheckinAvailable.message;
                return this.showCheckinNotAvailableModal();
              } else {
                // Redirect to Confirmation if reservation is completed is autocheckin normal mode
                if (this.getReservationCompleted() && !this.isReceptionMode) {
                  this.SET_RESERVATION_CHECKIN_COMPLETE(true);
                  return this.redirect({ name: "Confirmation" });
                } else {
                  this.SET_RESERVATIONS(response);
                  return this.redirect({ name: "Reservations" });
                }
              }
            }
          }

          this.inputGroupSelected = [
            this.form.reservation_filters[this.filterAttempts]
          ];
         
          let filterInput = this.inputGroupSelected[0];
          
          this.submittedInputs.forEach(input => {
            if (filterInput[0].name === input.name) {
              this.ADD_FILTER_ATTEMPT();
            }
          });

          if (filterInput[0].name === "reservation_code") {
            console.info(
              "Asking guest to filter by localizer. Returned reservations:",
              this.reservations
            );
          }

          // TODO: This should not be repeated
          this.inputGroupSelected = [
            this.form.reservation_filters[this.filterAttempts]
          ];

          await nextTick();
          this.$refs[`form-0`][0].$refs[
            this.inputGroupSelected[0][0].name
          ][0].resetInput();
          this.SET_INPUTS_FORM(this.inputGroupSelected);
          this.SET_HAS_MORE_RESERVATIONS(true);
          let inputName = this.getInputFilterForm.name;
          inputName = this.$t(`search.${inputName}`);
          this.isErrorMessage = false;
          this.modalMessage = this.$t("search.manyReservationFound", {
            number: response.length,
            input: inputName
          });
          await this.$store.dispatch("modal/SET_NAME", "searchModal");
          await this.$store.dispatch("modal/VISIBLE", true);
        } catch (error) {
          console.error("checkResponse Error", { error, brand: this.brandId });
        }
      } else if (response.length === 1) {
        // Set reservation selected
        if (response[0]) {
          // Check if checkin is available
            const isCheckinAvailable = this.isCheckinAvailable(
            response[0].check_in,
            this.timeLimitCheckin,
            this.closeTimeLimitCheckin,
            this.timezone
          );
          if (!isCheckinAvailable.available) {
            this.modalMessage = isCheckinAvailable.message;
            this.SET_RESERVATIONS(null);
            return this.showCheckinNotAvailableModal();
          } else {
            this.SET_RESERVATION_SELECTED(response[0]);
            this.SET_GUEST_TO_LIST(response[0].guests);
            if (this.isReceptionMode) {
              return this.redirect({ name: "Status" });
            }
            if (response[0].action === "redirect"){
              this.SET_CHECK_STATE(response[0].check_state)
            }
            if (this.getReservationCompleted() || response[0].action === "redirect") {
              this.SET_RESERVATION_CHECKIN_COMPLETE(true);
              return this.redirect({ name: "Confirmation" });
            }

            if (response[0].action === "show modal"){
              this.modalMessage = this.$t("search.invalidReservation", {
                checkIn: response[0].check_in,
                checkOut: response[0].check_out
              });
              await this.$store.dispatch("modal/SET_NAME", "searchModal");
              await this.$store.dispatch("modal/VISIBLE", true);
              return;
            }
            return this.redirect({ name: "Reservations" });
          }
        }
      }
      // If not found result add +1 to attemps
      else {
        this.ADD_ATTEMPT();
      }
    },
   
    async showCheckinNotAvailableModal() {
      await this.$store.dispatch("modal/SET_NAME", "searchModal");
      await this.$store.dispatch("modal/VISIBLE", true);
    },

    async manageQueryParams(inputs) {
      if (!this.reservation) {
        if (this.queryParam.token && this.config.token_key) {
          try {
            const tokenDecrypted = await CryptoJS.AES.decrypt(decodeURIComponent(this.queryParam.token), this.config.token_key);
            const params = JSON.parse(tokenDecrypted.toString(CryptoJS.enc.Utf8));

            await this.submitWithParams(inputs, params);
            
          } catch (e) {
            console.error("Error decrypting token param", e);
            this.CLEAR_QUERY_PARAMS();
            this.modalMessage = this.$t("search.tokenMalformed");
            await this.$store.dispatch("modal/SET_NAME", "searchModal");
            await this.$store.dispatch("modal/VISIBLE", true);
            return;
          }
        } else {
          await this.submitWithParams(inputs, this.queryParam);
        }
      }
    },

    async submitWithParams(inputs, params) {
      let submitParams = [];
      // Filters every input activated by config
      inputs.forEach(inputGroup => {
        const SEARCH_PARAMS = inputGroup.filter(
          input => params[input.name]
        );
        // returns an array with matched query params / config inputs
        SEARCH_PARAMS.forEach(param => {
            submitParams.push({
              name: param.name,
              value: params[param.name]
          })
        });
      });
      if (submitParams.length > 0) {
        this.CLEAR_QUERY_PARAMS();
        await this.submit([submitParams], {isQueryParam:true});
      }
      
    }
  }
};
</script>
<style lang="scss" scoped>
.divider {
  &:before,
  &:after {
    content: "";
    flex: 1;
    height: 2px;
    margin: 0 1em;
    background: rgb(229, 231, 235);
  }
}
</style>