---
title: Search
layout: layout.html
eleventyNavigation:
  key: Search
  parent: Pages
  order: 4
---

# Search

Here there's a diagram explaning user flow when interacting:

```mermaid
stateDiagram-v2
    state "User arrives to Search page" as Search
    state "Reservations page" as Reservation
    state "Error page" as Error
    state "Add attempt" as AddAttempt
    state "User sends reservation data" as SendReservationData
    state "User exceeded reservation attempts?" as ReservationAttempts
    state "Reservation stored" as ReservationStored
    state "User sends Check-in/out data" as CheckDates
    state "Reservation not available yet" as timeLimitCheckin
    state "Check-in/out correct?" as CheckDatesCorrect
    state "Has reservation data on Store?" as ReservationDataOnStore
    state "Is Checkin available?" as CheckinAvailable
    state "Check if reservations are the same" as CheckSameReservations

    state ReservationChoice <<choice>>
    state ReservationExistsChoice <<choice>>
    state AttemptsExceededChoice  <<choice>>
    state MoreReservationsChoice <<choice>>
    state CorrectDatesChoice  <<choice>>
    state ReservationDateOnStoreChoice <<choice>>
    state CheckinAvailableChoice <<choice>>
    state CheckSameReservationsChoice <<choice>>

    Search --> ReservationChoice : Reservation selected in store?
        ReservationChoice --> Reservation : Yes
        ReservationChoice --> SendReservationData: No
        SendReservationData --> ReservationExistsChoice : Reservation exists?
            ReservationExistsChoice --> ReservationAttempts : No
            AddAttempt --> ReservationAttempts
            ReservationAttempts --> AttemptsExceededChoice
                AttemptsExceededChoice --> Error : Yes
                AttemptsExceededChoice --> SendReservationData : No

            ReservationExistsChoice --> ReservationStored : Yes
            ReservationStored --> MoreReservationsChoice: More than one reservation?
                MoreReservationsChoice --> Reservation : No
                MoreReservationsChoice --> CheckSameReservations: Yes
                CheckSameReservations --> CheckSameReservationsChoice : Are the same?
                    CheckSameReservationsChoice --> Reservation: Yes
                    CheckSameReservationsChoice --> CheckDates: No
                CheckDates --> CheckDatesCorrect
                CheckDatesCorrect --> CorrectDatesChoice
                    CorrectDatesChoice --> CheckinAvailable : Yes
                    CheckinAvailable --> CheckinAvailableChoice
                        CheckinAvailableChoice --> timeLimitCheckin: No
                        timeLimitCheckin --> Search
                        CheckinAvailableChoice --> Reservation: Yes
                    CorrectDatesChoice --> AddAttempt : No

                    AttemptsExceededChoice --> ReservationDataOnStore: No
                    ReservationDataOnStore --> ReservationDateOnStoreChoice
                        ReservationDateOnStoreChoice --> CheckDates : Yes
                        ReservationDateOnStoreChoice --> SendReservationData : No
```
