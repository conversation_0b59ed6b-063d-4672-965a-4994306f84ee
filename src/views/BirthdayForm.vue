<template>
  <div class="birthday main-content main-content-white">
    <modal>
      <p v-html="$t('birthdayform.modalMessage')"></p>
    </modal>
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("birthdayform.title") }}</title-component>
      <form-input
        @inputChanged="inputChanged($event)"
        class="mb-6"
        :name="$t('birthdayform.inputTitle')"
        type="date"
        value=""
        placeholder="..."
        :disableFutureDates="true"
        data-test="dob-input"
      />
      <div class="flex-1 flex flex-row items-start">
        <information class="info-icon flex-shrink-0" />
        <p class="ml-2 text-gray-500">
          {{ $t("birthdayform.info") }}
        </p>
      </div>

      <btn
        @click.native="validateDob"
        :disabled="birthday === ''"
        data-test="dob-submit"
      >
        {{ $t("birthdayform.button") }}
      </btn>
      <go-back-link :text="$t('birthdayform.goBack')" />
    </div>
  </div>
</template>
<script>
import formInput from "@/components/search/formInput.component";
import btn from "@/components/shared/button.component";
import titleComponent from "@/components/shared/title.component";
import information from "@/assets/images/icons/informacion.svg";
import goBackLink from "@/components/shared/goBackLink.component";
import modal from "@/components/shared/modal.component";
import moment from "moment";
import { mapActions, mapMutations, mapState } from "vuex";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import stopLoading from "../mixins/stopLoading";
export default {
  name: "birthday",
  data() {
    return {
      birthday: ""
    };
  },
  mixins: [browserHistoryManagement, stopLoading],
  components: {
    formInput,
    btn,
    titleComponent,
    information,
    goBackLink,
    modal
  },
  computed: {
    ...mapState("reservations", ["reservationSelected", "childAgeAttempts"]),
    ...mapState("brand", ["config"])
  },
  mounted() {
    this.stopLoading();
  },
  methods: {
    ...mapMutations("reservations", [
      "addChildAgeAttempt",
      "resetChildAgeAttempts"
    ]),

    ...mapActions("guest", [
      "SET_DATA",
      "ADD_GUEST_TO_LIST",
      "SET_GUEST_CHECKIN_COMPLETE"
    ]),

    ...mapActions("modal", ["VISIBLE", "SET_TITLE"]),
    ...mapActions("reservations", ["ADD_SESSION_GUEST"]),

    inputChanged($event) {
      this.birthday = $event.value;
    },

    async validateDob() {
      const maxBrandchildAge = this.config
        .child_required_identity_documents_age;
      const maxChildAgeAttempts = this.config.max_attempts_child;
      const childDob = moment(this.birthday);
      const checkinDate = moment(this.reservationSelected.check_in);
      const childAgeAtCheckinDate = checkinDate.diff(childDob, "years");
      let childData = {
        pax_type: "CH",
        birthday_date: moment(this.birthday).format("YYYY-MM-DD")
      };

      this.addChildAgeAttempt();

      if (
        this.childAgeAttempts > maxChildAgeAttempts &&
        childAgeAtCheckinDate > maxBrandchildAge
      ) {
        this.resetChildAgeAttempts();

        return this.$router.push({
          name: "Error",
          params: {
            error: "ageError",
            route: "Scan"
          }
        });
      } else if (
        this.childAgeAttempts <= maxChildAgeAttempts &&
        childAgeAtCheckinDate > maxBrandchildAge
      ) {
        await this.VISIBLE(true);
        await this.SET_TITLE(this.$t("birthdayform.modalTitle"));
      } else {
        this.resetChildAgeAttempts();
        this.SET_DATA(childData);
        this.$router.push({ name: "Confirmation" });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.main-color-link {
  color: var(--bgColor);
}
.info-icon {
  width: 30px;
  height: 30px;
}
</style>
