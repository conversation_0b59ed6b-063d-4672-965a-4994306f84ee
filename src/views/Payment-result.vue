<template>
  <div
    class="payments-result main-content main-content-white"
    :style="cssVariables"
  >
    <modal name="paymentInformation" @closeModal="closePaymentInformationModal"
      >{{ modalMessage }}
    </modal>
    <div class="content h-full flex flex-col">
      <div class="flex flex-col items-center flex-grow mb-8">
        <big-check v-if="isPaid" class="mb-2" />
        <error-small v-else class="mb-2 error-icon" />
        <title-component>
          {{
            isPaid ? $t("paymentresult.titleOk") : $t("paymentresult.titleKo")
          }}
        </title-component>
        <div v-if="isPaid">
          <p>
            {{ $t("paymentresult.textOkFirst") }}
            <span class="font-black"
              >{{ platformMethodAndPaymentData.amount }}
              {{ platformMethodAndPaymentData.currency }}</span
            >
          </p>
          <p class="mt-2">{{ $t("paymentresult.textOkSecond") }}</p>
          <div
            class="degrade rounded border border-gray-200 status w-full p-4 mt-10 flex flex-col"
          >
            <p>
              <span class="uppercase mr-2 text-xs"
                >{{ $t("paymentresult.service") }}:</span
              ><span class="font-black uppercase text-xs">{{
                this.order.service
              }}</span>
            </p>
            <p>
              <span class="uppercase mr-2 text-xs"
                >{{ $t("paymentresult.operation") }}:</span
              ><span class="font-black text-xl">{{ this.order.id }}</span>
            </p>
          </div>
        </div>
        <div v-else>
          <p>{{ $t("paymentresult.textKo") }}</p>
        </div>
      </div>
      <btn
        v-if="!isPaid"
        @click.native="$router.push({ name: 'PaymentProform' })"
      >
        {{ $t("paymentresult.tryAgain") }}
      </btn>
      <btn v-if="!isPaid" class="mt-4" @click.native="goNext"
        >{{ $t("paymentresult.continueWithoutPay") }}
      </btn>
      <btn v-if="isPaid" @click.native="$router.push({ name: 'Comments' })"
        >{{ $t("paymentresult.continue") }}
      </btn>
    </div>
  </div>
</template>
<script>
import titleComponent from "@/components/shared/title.component";
import bigCheck from "@/assets/images/icons/big-check.svg";
import errorSmall from "@/assets/images/icons/error_small.svg";
import stopLoading from "@/mixins/stopLoading";

import btn from "../components/shared/button.component";
import modal from "../components/shared/modal.component";
import { mapState } from "vuex";
import startLoading from "../mixins/startLoading";
import cssVariables from "@/mixins/cssVariables";

export default {
  name: "payments-result",
  data() {
    return {
      status: "",
      error: "",
      orderId: "",
      modalMessage: ""
    };
  },
  components: {
    titleComponent,
    bigCheck,
    errorSmall,
    btn,
    modal
  },
  mixins: [stopLoading, startLoading, cssVariables],
  computed: {
    ...mapState("brand", ["brandId", "name"]),
    ...mapState("payment", [
      "integrations",
      "platformMethodAndPaymentData",
      "order"
    ]),
    isPaid() {
      return this.$route.query.result === "ok";
    }
  },
  created() {
    this.stopLoading();
  },
  methods: {
    async goNext() {
      await this.$store.dispatch("modal/SET_NAME", "paymentInformation");
      await this.$store.dispatch(
        "modal/SET_TITLE",
        this.$t("paymentresult.modal_info_title")
      );
      await this.$store.dispatch("modal/SET_TYPE", "info");
      this.modalMessage = this.$t("paymentresult.modal_info_text");
      await this.$store.dispatch("modal/VISIBLE", true);
    },
    async closePaymentInformationModal() {
      await this.$store.dispatch("loading/LOADING", true);
      this.$router.push({ name: "Comments" });
    }
  }
};
</script>
<style lang="scss" scoped>
.receipt-link {
  color: var(--bgColor);

  &:hover {
    color: var(--darkenBgColor);
  }
}
</style>
