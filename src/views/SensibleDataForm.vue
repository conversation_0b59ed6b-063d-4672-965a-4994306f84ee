<template>
  <div class="scan main-content main-content-white">
    <modal @closeModal="closeErrorModal" name="errorModal">
      <p>
        {{ messageError }}
      </p>
    </modal>
    <div class="content h-full flex flex-col justify-between">
      <section class="flex flex-col">
        <title-component>{{ $t("sensibledata.title") }}</title-component>
        <p class="text-gray-800 mb-4">
          {{ $t("sensibledata.text") }}
        </p>
      </section>

      <section class="flex-grow">
        <form-input
          v-if="imageOcr && !imageOcr.document_type"
          @inputChanged="documentTypeChanged($event)"
          class="mb-6"
          :active="true"
          :optional="false"
          :name="$t('validatedata.document_type')"
          :type="'select'"
          :options="getTranslatedDocumentTypeOptions"
          :inputName="'document_type'"
          placeholder="..."
          :check-on-start="false"
          data-test="document-type-sensible-input"
        />
        <form-input
          v-if="imageOcr && !imageOcr.nationality && (imageOcr.document_type === 'identity_card' || documentTypeValue === 'identity_card'|| imageOcr.document_type === 'driving_license'|| documentTypeValue === 'driving_license')"
          @autocompleteSelect="autocompleteSelect($event, 'Nationality')"
          @inputChanged="nationalityChanged($event, 'Nationality')"
          class="mb-6"
          :active="true"
          :optional="false"
          :name="$t('validatedata.nationality')"
          :type="'autocomplete'"
          :options="nationalitySuggestion"
          :inputName="'nationality'"
          :value="nationalityValue"
          placeholder="..."
          :check-on-start="false"
          data-test="nationality-sensible-input"
        />
        <form-input
        v-if="imageOcr && !imageOcr.issuing_country && (imageOcr.document_type === 'residence_permit' || documentTypeValue === 'residence_permit')"
        @autocompleteSelect="autocompleteSelect($event, 'IssuingCountry')"
        @inputChanged="nationalityChanged($event, 'IssuingCountry')"
        class="mb-6"
        :active="true"
        :optional="false"
        :name="$t('validatedata.issuing_country')"
        :type="'autocomplete'"
        :options="nationalitySuggestion"
        :inputName="'issuingCountry'"
        :value="issuingCountryValue"
        placeholder="..."
        :check-on-start="false"
        data-test="issuing-country-sensible-input"
      />
      </section>

      <btn
          :disabled="isContinueButtonDisabled"
          data-test="sensible-modal-button"
          @click.native="completeSensibleData()"
        >
          {{ $t("sensibledata.button") }}
        </btn>
      
    </div>
  </div>
</template>
<script>
import modal from "@/components/shared/modal.component";
import titleComponent from "@/components/shared/title.component";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import redirect from "@/mixins/redirect";
import formInput from "@/components/search/formInput.component";
import validateInputs from "@/mixins/validateInputs";
import { mapGetters, mapActions, mapState } from "vuex";
import btn from "@/components/shared/button.component";
import scan from "@/mixins/scan";
import sensibleData from "../mixins/sensibleData";

export default {
  name: "scan",
  data() {
    return {
      imageOcr: null,
      scanImage: null,
      scanImageType: null,
      hasError: false,
      messageError: null,
      documentSide: "",
      schengenZoneError: false,
    };
  },
  components: {
    titleComponent,
    formInput,
    btn,
    modal
  },
  mixins: [
    startLoading,
    stopLoading,
    validateInputs,
    redirect,
    scan,
    sensibleData
  ],

  async created() {
    this.imageOcr = this.$route.params.scanData;
    this.scanImage = this.$route.params.image;
    this.scanImageType = this.$route.params.imageType;

    if (!this.imageOcr) {
      return this.redirect({ name: "Scan" });
    }

    await this.stopLoading();
  },
  
  computed: {
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", { guestData: "getSelectedGuest" }),
    ...mapGetters("scan", [
      "getIdentityDocumentByName",
    ]),
    ...mapState("loading", ["loading"]),
    ...mapState("brand", { country: "country", config: "config"}),
    },
    ...mapState("scan", {
      scannedWithSignature: "scannedWithSignature"}),

  methods: {
    ...mapActions("modal", {
      VISIBLE: "VISIBLE",
      SET_TITLE: "SET_TITLE",
      SET_TYPE: "SET_TYPE",
      SET_BUTTON_MESSAGE: "SET_BUTTON_MESSAGE",
      SET_NAME: "SET_NAME",
      CLEAR_MODAL_STATE: "CLEAR_STATE"
    }),
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("scan", [
      "SET_SCANNED_WITH_SIGNATURE",
    ]),
    
    async completeSensibleData() {
      const isValidSensibleData = await this.checkSensibleData();
      
      if (isValidSensibleData) {
        await this.validateData(this.imageOcr, {
          base64ImageSource: this.scanImage,
          imageType: this.scanImageType
        });
        if(this.config.identity_document_signature_required){
          await this.handleSignatureRequired();  
        } 
        return this.redirect({ name: "Scan", params: { imageProcessed: true }});
      }
    },

    closeErrorModal() {
      return this.redirect({ name: "Scan" });
    }

  }
};
</script>