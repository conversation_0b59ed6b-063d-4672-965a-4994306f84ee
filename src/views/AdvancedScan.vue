<template>
  <div class="main-content main-content-white">
    <modal data-test="modalError" @closeModal="modalClosed" name="errorModal">
      <p>
        {{ messageError }}
      </p>
    </modal>
    <modal @closeModal="modalClosed" name="signatureRequired">
      <div>
        <p>
          {{
            $t("scan.modalSignatureRequiredText", { hotelName: this.brandName })
          }}
        </p>
      </div>
    </modal>
    <modal name="similarGuest" :button="false">
      <p
        class="text-gray-500 text-justify"
        v-if="similarGuest && similarGuest.processCompleted"
      >
        {{ $t("scan.error.similarGuestError") }}
      </p>
      <div v-if="similarGuest" class="flex flex-col mb-4">
        <span v-if="similarGuest.full_name"
          >{{ $t("validatedata.name") }}: {{ similarGuest.full_name }}</span
        >
        <span v-if="similarGuest.document_number"
          >{{ $t("validatedata.document_number") }}:
          {{ similarGuest.document_number }}
        </span>
        <span v-if="similarGuest.birthday_date"
          >{{ $t("validatedata.birthday_date") }}:
          {{ similarGuest.birthday_date }}</span
        >
      </div>
      <p>{{ $t("scan.error.similarGuestConfirm") }}</p>
      <btn
        class="bg-white hover:bg-gray-50 border my-4"
        textColor="black"
        @click.native="handleSelectedGuest(guestData)"
        data-test="different-guest-btn"
      >
        {{ $t("shared.no") }}
      </btn>
      <btn
        @click.native="handleSimilarGuestInReservation"
        data-test="same-guest-btn"
      >
        {{ $t("shared.yes") }}
      </btn>
    </modal>
    <video
      ref="video"
      class="absolute min-w-full min-h-full w-auto h-auto"
      autoplay
      playsinline
    ></video>
    <div class="content h-full flex flex-col z-10">
      <input
        ref="fileUpload"
        type="file"
        @input="onFileChanged"
        accept="image/png, image/jpeg, image/jpg"
        hidden
      />
      <div
        ref="cutout"
        class="cutout w-full h-full 2xl:h-45vh bg-transparent rounded-lg z-10 order-2"
        :class="{
          'bg-black-transparency border-4 border-red-400': userMediaError,
          'border-bg-color': !userMediaError,
        }"
      >
        <div v-if="userMediaError" class="p-4 h-full flex flex-col">
          <p
            data-test="userMediaError"
            class="flex-grow text-red-400 text-center"
          >
            {{ userMediaErrorMessage }}
          </p>
          <div class="flex justify-center">
            <btn
              data-test="uploadPhotoButton"
              class="mt-4"
              :disabled="photo !== null"
              :small="true"
              @click.native="uploadFile"
            >
              {{ $t("advancedscan.uploadPhoto") }}
            </btn>
          </div>
        </div>

        <img v-if="photo" :src="photo" />
      </div>
      <section class="order-3 z-10">
        <button
          data-test="deletePhotoButton"
          class="border-red-900 font-black focus:outline-none w-full text-center uppercase text-xs text-red-500 hover:text-red-900 cursor-pointer"
          @click="photo = null"
        >
          {{ $t("advancedscan.deletePhoto") }}
        </button>
        <div class="border-red-900 flex z-10 gap-4 text-white">
          <div class="w-10 flex">
            <info />
          </div>
          <span class="my-auto">{{ $t("advancedscan.scanInfo") }}</span>
        </div>
      </section>

      <section class="z-10 order-1">
        <title-component class="text-white">{{
          $t("advancedscan.title")
        }}</title-component>
      </section>

      <section class="flex flex-col z-10 order-5 mt-4">
        <btn
          data-test="makePhotoButton"
          :disabled="photo !== null || userMediaError"
          @click.native="takeSnapshotandProcess()"
        >
          {{ $t("advancedscan.makePhoto") }}
        </btn>
        <p
          v-if="!isMobileDevice() && !userMediaError"
          class="mt-5 text-white text-center"
        >
          {{ $t("advancedscan.snapshotTrouble") }}
          <a href="#" @click.prevent="uploadFile" class="text-blue-500 ml-1">{{
            $t("advancedscan.uploadPhotoOnTrouble")
          }}</a>
        </p>
        <go-back-link :text="$t('shared.previousStep')" />
      </section>
    </div>
  </div>
</template>
<script>
import modal from "@/components/shared/modal.component";
import stopLoading from "@/mixins/stopLoading";
import titleComponent from "@/components/shared/title.component";
import btn from "@/components/shared/button.component";
import goBackLink from "@/components/shared/goBackLink.component";
import info from "@/assets/images/icons/informacion.svg";
import redirect from "@/mixins/redirect";
import scan from "@/mixins/scan";
import { mapGetters, mapActions, mapState } from "vuex";

export default {
  mixins: [stopLoading, redirect, scan],
  components: { btn, titleComponent, info, modal, goBackLink },
  data() {
    return {
      video: null,
      stream: null,
      photo: null,
      base64Image: null,
      imageOcr: null,
      imageType: null,
      messageError: null,
      userMediaError: false,
      userMediaErrorMessage: null,
      cameraPermissionDeined: "Permission denied",
      documentSide: "front",
      isCameraEnabled: false,
      modalShown: false,
    };
  },
  mounted() {
    this.documentSide = this.$route?.query?.side ?? this.documentSide;
    this.stopLoading();
    this.setupVideo();
  },
  computed: {
    ...mapState("reservations", ["reservationSelected"]),
    ...mapState("scan", {
      currentAttemptsDocumentScan: "currentAttemptsDocumentScan",
      identityDocuments: "identityDocuments",
      scannedWithSignature: "scannedWithSignature",
      signatureUploadRequired: "signatureUploadRequired",
    }),
    ...mapState("brand", {
      brandName: "name",
      config: "config",
    }),
    ...mapState("modal", {
      modalName: "name",
    }),
    ...mapGetters("guest", { guestData: "getSelectedGuest" }),
    ...mapGetters("scan", ["getIdentityDocumentByName"]),
    showPaxInfo() {
      let pax_type = this.guestData?.pax_type;

      if (pax_type !== "AD") {
        return [
          this.$t(`statusComponent.${pax_type}`).toLowerCase(),
          this.$t("scan.older"),
        ];
      }

      return [
        this.$t("statusComponent.AD").toLowerCase(),
        this.$t("scan.younger"),
      ];
    },
  },
  methods: {
    ...mapActions("scan", [
      "CLEAR_SCAN_DOCUMENT_ATTEMPT",
      "CLEAR_STATE",
      "CLEAR_DOCUMENT",
      "SET_SCANNED_WITH_SIGNATURE",
      "SET_BACK_PART_SCANNED",
      "SET_SIGNATURE_UPLOAD_REQUIRED",
    ]),
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("modal", {
      VISIBLE: "VISIBLE",
      SET_TITLE: "SET_TITLE",
      SET_TYPE: "SET_TYPE",
      SET_BUTTON_MESSAGE: "SET_BUTTON_MESSAGE",
      SET_NAME: "SET_NAME",
      CLEAR_MODAL_STATE: "CLEAR_STATE",
    }),
    ...mapActions("queryParams", {
      SET_QUERY_PARAM: "SET_DATA",
    }),
    async setupVideo() {
      // get video and canvas elements
      this.video = this.$refs.video;

      // set up the video stream
      try {
        const constraints = {
          audio: false,
          video: {
            facingMode: "environment",
            height: { ideal: this.video.clientHeight },
          },
        };

        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        this.isCameraEnabled = true;
        this.video.srcObject = stream;
        this.stream = stream;
        this.userMediaError = false;
      } catch (error) {
        console.error("Could not get user media:", {
          errorMessage: error?.message,
          name: error?.name,
          stack: error?.stack,
          error: error,
        });
        this.userMediaError = true;
        if (error.message === this.cameraPermissionDeined) {
          this.isCameraEnabled = false;
          this.userMediaErrorMessage = this.$t("advancedscan.cameraDeined");
        } else {
          this.userMediaErrorMessage = this.$t(
            "advancedscan.genericUserMediaError"
          );
        }
      }
    },
    async takeSnapshotandProcess() {
      const canvas = document.createElement("canvas");
      let context = canvas.getContext("2d");

      const {
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        rectangleWidth,
        rectangleHeight,
      } = this.calculateSnapshot();

      canvas.width = rectangleWidth;
      canvas.height = rectangleHeight;

      // draw the image onto the canvas
      context.drawImage(
        this.video,
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        0,
        0,
        rectangleWidth,
        rectangleHeight
      );

      this.photo = canvas.toDataURL("image/jpeg", 1.0);

      await this.startImageProcessing(this.photo);
    },

    stopStream() {
      this.stream?.getTracks().forEach((track) => track.stop());
    },

    uploadFile() {
      this.$refs.fileUpload.click();
    },
    async startImageProcessing(photo) {
      await this.LOADING(true);
      let imagesPayload = [
        this.photo
          ? photo.replace(/data:image\/.*,/, "")
          : await this.toBase64(photo),
      ];

      if (Object.keys(this.identityDocuments).length !== 0) {
        for (const doc in this.identityDocuments) {
          if (
            this.identityDocuments[doc].data.document_type ===
              "identity_card" ||
            this.identityDocuments[doc].data.document_type === "passport" ||
            this.identityDocuments[doc].data.document_type === "driving_license"
          ) {
            imagesPayload.push(this.identityDocuments[doc].content);
          }
        }
      }

      let documentScanned;
      // If camera is enabled, we don't distinguish if the document scanned was a signature
      if (!this.isCameraEnabled) {
        documentScanned = this.documentSide;
      }
      this.base64Image = imagesPayload[0];
      this.imageOcr = await this.sendImage(
        this.isCameraEnabled ? imagesPayload : [this.base64Image],
        documentScanned
      );

      const isRejected = await this.rejectInvalidPassport(this.imageOcr);
      if (isRejected) {
        this.stopLoading();
        return;
      }
      this.imageType = this.photo ? "jpeg" : photo?.type;

      if (this.imageOcr) {
        /*
        We redirect to sensible data page asking for extra information about the scanned document in case the document type could not be retrieved or if we know it is a DNI/driving license without nationality, or a residence permit without issuing country.
        This ensures that the back of the document is scanned if it is a DNI or residence permit and then we can reject the document if a DNI from outside the Schengen zone is entered or if the driving license/residence permit is from other country than the hotel.
        */
        if (
          (!this.imageOcr.document_type && this.documentSide !== "signature") ||
          ((this.imageOcr.document_type === "identity_card" ||
            this.imageOcr.document_type === "driving_license") &&
            !this.imageOcr.nationality &&
            this.documentSide !== "signature") ||
          (this.imageOcr.document_type === "residence_permit" &&
            !this.imageOcr.issuing_country &&
            this.documentSide !== "signature")
        ) {
          console.info("Send user to complete data page", {
            ocrData: this.imageOcr,
          });

          await this.stopStream();
          if (this.imageOcr.side == null) {
            this.imageOcr.side = this.documentSide;
          }
          return this.$router.push({
            name: "SensibleData",
            params: {
              scanData: this.imageOcr,
              image: this.base64Image,
              imageType: this.imageType,
            },
          });
        } else {
          if (this.documentSide === "signature") {
            this.imageOcr.document_type = "signature";
          }
          const validDocument = await this.validateData(this.imageOcr, {
            base64ImageSource: this.base64Image,
            imageType: this.imageType,
          });

          if (this.config.identity_document_signature_required) {
            await this.handleSignatureRequired();
            if (this.modalShown && this.modalName === "signatureRequired")
              return;
          }

          if (validDocument) {
            await this.LOADING(true);
            await this.stopStream();
            return this.redirect({
              name: "Scan",
              params: { imageProcessed: true },
            });
          }
        }
      }
      this.stopLoading();
    },

    calculateSnapshot() {
      const videoRect = this.video.getBoundingClientRect();
      const cutoutRect = this.$refs.cutout.getBoundingClientRect();

      const videoAspectRatio = this.video.videoWidth / this.video.videoHeight;
      const displayAspectRatio = videoRect.width / videoRect.height;

      let scale;
      let offsetX = 0;
      let offsetY = 0;

      if (displayAspectRatio > videoAspectRatio) {
        // Video is cut off horizontally (letterboxing)
        scale = this.video.videoHeight / videoRect.height;
        offsetX = (videoRect.width - this.video.videoWidth / scale) / 2;
      } else {
        // Video is cut off vertically (pillarboxing)
        scale = this.video.videoWidth / videoRect.width;
        offsetY = (videoRect.height - this.video.videoHeight / scale) / 2;
      }

      // Compute the position of the cutout relative to the displayed video
      const relativeX = cutoutRect.x - videoRect.x - offsetX;
      const relativeY = cutoutRect.y - videoRect.y - offsetY;

      // Translate this to video pixels
      const sourceX = relativeX * scale;
      const sourceY = relativeY * scale;
      const sourceWidth = cutoutRect.width * scale;
      const sourceHeight = cutoutRect.height * scale;

      // Output dimensions are the same as the cutout
      const rectangleWidth = cutoutRect.width;
      const rectangleHeight = cutoutRect.height;

      return {
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        rectangleWidth,
        rectangleHeight,
      };
    },
    isMobileDevice() {
      return window.innerWidth <= 768;
    },
  },
};
</script>

<style scoped>
.cutout {
  outline: 3000px solid rgba(0, 0, 0, 0.7);
  min-height: 200px;
}
.border-bg-color {
  border: 3px solid var(--bgColor);
}

.bg-black-transparency {
  background-color: rgba(0, 0, 0, 0.7);
}

video {
  top: 50%;
  left: 50%;
  max-width: unset;
  transform: translate(-50%, -50%);
}
</style>
