<template>
  <div class="main-content main-content-white">
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("login.title") }}</title-component>

      <amplify-authenticator>
        <amplify-sign-in
          header-text=""
          hide-sign-up="true"
          username-alias="email"
          slot="sign-in"
        >
        </amplify-sign-in>
        <amplify-forgot-password
          username-alias="email"
          slot="forgot-password"
        ></amplify-forgot-password>
        <amplify-require-new-password
          slot="require-new-password"
          :user.prop="user"
        ></amplify-require-new-password>
      </amplify-authenticator>
    </div>
  </div>
</template>

<script>
import { I18n } from "@aws-amplify/core";
import { onAuthUIStateChange } from "@aws-amplify/ui-components";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import titleComponent from "@/components/shared/title.component";
import { mapActions } from "vuex";

export default {
  name: "Login",
  mixins: [startLoading, stopLoading],
  components: { titleComponent },
  data() {
    return {
      user: undefined,
      authState: undefined,
      unsubscribeAuth: undefined
    };
  },
  async created() {
    await this.SET_MAIN_COLOR(process.env.VUE_APP_MAIN_COLOR_FALLBACK);
    await this.SET_BRAND_LOGO(
      "https://images.hotelinking.com/default/imalogo.jpg"
    );
    await this.SET_BRAND_NAME("Hotelinking");

    I18n.setLanguage(this.$i18n.locale);
    this.stopLoading();

    this.unsubscribeAuth = onAuthUIStateChange(async (authState, authData) => {
      this.authState = authState;
      this.user = authData;

      if (this.authState == "signedin") {
        const authorizedBrands = this.user.attributes["custom:brand_id"]?.split(
          ","
        );
        this.SET_BRAND_ID(authorizedBrands[0])
        await this.SET_AUTHORIZED_BRANDS(authorizedBrands);

        this.$router.push({
          path: `/${authorizedBrands[0]}`
        });

        return this.unsubscribeAuth();
      }
    });
  },
  methods: {
    ...mapActions("brand", ["SET_BRAND_ID"]),
    ...mapActions("brand", [
      "SET_MAIN_COLOR",
      "SET_BRAND_LOGO",
      "SET_BRAND_NAME"
    ]),
    ...mapActions("app", ["SET_AUTHORIZED_BRANDS"])
  }
};
</script>
<style lang="scss">
:root {
  --amplify-primary-color: #715aff;
  --amplify-primary-shade: #6750fc;
  --amplify-primary-tint: #856eff;
}

amplify-authenticator {
  --box-shadow: 0;
  --padding: 0;
  --container-align: none;
  --width: 100%;
}

amplify-sign-in,
amplify-forgot-password,
amplify-require-new-password {
  width: 100%;
}
</style>
