<template>
  <div class="signature main-content main-content-white ">
    <modal @closeModal="closeModal">{{ modalMessage }}</modal>
    <div class="content h-full flex flex-col">
      <div class="signature-content flex flex-grow flex-col">
        <title-component>{{ $t("signature.title") }}</title-component>
        <p class="text-gray-800 mb-4">
          {{ $t("signature.text") }}
        </p>
        <div class="flex justify-center h-full max-h-64">
          <div class="border-solid border-2 border-gray-200 rounded flex flex-grow mb-4 sm:h-full sm:min-h-128 md:min-h-0 w-full landscape:max-w-xs">
            <VueSignaturePad
            data-test="signature-pad"
            ref="signaturePad"
          />
          </div>
        </div>
        <button
          class="font-black mt-4 focus:outline-none w-full text-center uppercase text-xs text-red-500 hover:text-red-900 cursor-pointer mb-10"
          @click="clearSignature"
          >
          {{ $t("signature.clear") }}
        </button>
      </div>
      <btn data-test="continue-button" @click.native="saveSignature()">{{
        $t("signature.button")
      }}</btn>
      <go-back-link :text="$t('shared.previousStep')" />
    </div>
  </div>
</template>

<script>
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import modal from "@/components/shared/modal.component";
import { mapActions, mapState, mapGetters } from "vuex";
import repository from "../repository/repositoryFactory";
import titleComponent from "@/components/shared/title.component";
import goBackLink from "@/components/shared/goBackLink.component";
import btn from "@/components/shared/button.component";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";
import { getSignatureAsTimeStampString } from "@/services/SignatureService";
import { Tracker } from "@/tracker";
import trimCanvas from 'trim-canvas'

const api = repository.get("checkin");

export default {
  name: "Signature",
  data() {
    return {
      modalMessage: "",
      maximumAttemptsExceeded: false
    };
  },
  components: {
    modal,
    titleComponent,
    btn,
    goBackLink
  },
  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect],

  created() {
    this.showAndCheckMaxAttempExceeded(false);
  },
  mounted() {
    this.stopLoading();
  },

  computed: {
    ...mapState("brand", {
      brandId: "brandId",
      brandName: "name",
      logo: "logo",
      mainColor: "mainColor",
      config: "config"
    }),
    ...mapGetters("app", ["isReceptionMode", "isDemoMode"]),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData" })
  },

  methods: {
    clearSignature() {
      this.$refs.signaturePad.clearSignature();
    },
    ...mapActions("guest", ["UPDATE_GUEST", "ADD_SIGNATURE_ATTEMPT"]),
    async saveSignature() {
      await this.$store.dispatch("loading/LOADING", true);
      const { isEmpty } = this.$refs.signaturePad.saveSignature(
        "image/jpeg"
      );
      if (isEmpty) {
        this.modalMessage = this.$t("signature.noSignatureErrorMessage");
        await this.$store.dispatch(
          "modal/SET_TITLE",
          this.$t("signature.errorTitle")
        );
        await this.$store.dispatch("modal/VISIBLE", true);
      } else {
        // Generate timestamp signature trace
        this.UPDATE_GUEST({
          timestampSignature: getSignatureAsTimeStampString(
            this.$refs.signaturePad.toData()
          )
        });

        //We cut out the signature from the image, erasing the white spaces, using trim-canvas. First, we capture the existing canvas, create a copy of it with same size, remove the blank or transparent area around the visible content, and then converts this cropped version of the canvas to a PNG image URL data format.
        const canvas = document.querySelector('canvas');
        const canvasCopy = document.createElement('canvas');

        canvasCopy.width = canvas.width
        canvasCopy.height = canvas.height
        canvasCopy.getContext('2d').drawImage(canvas, 0, 0)

        const trimmedCanvas = trimCanvas(canvasCopy)
      
        const trimmedData = trimmedCanvas.toDataURL('image/png')
        
        // Save signature image on store
        await this.UPDATE_GUEST({ documentSignature: trimmedData });
        const documentsSaved = await this.saveDocuments(trimmedData);
        if (documentsSaved) {
          await this.UPDATE_GUEST({ documentsSaved: documentsSaved });
          Tracker.recordCustomEvent("guest_documents_signed", {
            guest: this.guestLiteData,
            documents: documentsSaved
          });
          return this.redirect({ name: "SendDocuments" });
        }
      }
      this.stopLoading();
    },

    async showAndCheckMaxAttempExceeded(showNormalError = true) {
      this.isErrorMessage = true;
      if (showNormalError) {
        this.modalMessage = this.$t("signature.signatureNotSavedErrorMessage");
      }

      if (showNormalError) {
        await this.$store.dispatch(
          "modal/SET_TITLE",
          this.$t("signature.errorTitle")
        );
        await this.$store.dispatch("modal/VISIBLE", true);
      }
    },

    async saveDocuments(signature) {
      const requestBody = {
        brand_name: this.isDemoMode ? `${this.brandName}-demo` : this.brandName,
        brand_logo: this.logo,
        background_color: this.mainColor,
        guest_name: this.guestData?.full_name,
        reservation_id: this.reservationSelected?.res_id,
        signature: signature,
        documents: this.guestData?.documents
      };

      console.info("Request body INFO: ", requestBody);
      
      return api
        .createDocuments(this.brandId, requestBody)
        .then(response => response.data)
        .catch(async error => {
          console.error("Error signing documents", { error });
          this.showAndCheckMaxAttempExceeded();
          return null;
        });
    },

    closeModal() {
      this.modalMessage = null;
    }
  }
};
</script>
