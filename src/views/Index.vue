<template>
  <router-view />
</template>
<script>
import redirect from "@/mixins/redirect";
import startLoading from "@/mixins/startLoading";
import repository from "@/repository/repositoryFactory";
const api = repository.get("brand");
import { mapState, mapActions, mapGetters } from "vuex";

export default {
  name: "Index",
  mixins: [redirect, startLoading],
  computed: {
    ...mapState("brand", ["brandId", "config", "children"]),
    ...mapGetters("app", ["isReceptionMode"]),
    ...mapGetters("brand", ["getAutocheckinActive"])
  },
  async created() {
    try {
      const brandInfo = await api.getBrand(this.brandId);

      await this.SET_BRAND(brandInfo.data);

      // If brand is a chain, go to BrandSelector
      if (this.children.length) {
        return this.redirect({ name: "BrandSelector" });
      }

      if (this.getAutocheckinActive.length == 0 && !this.$route.query.preview) {
        console.error("Error: Autocheckin not active");
        throw new Error();
      }
      
      const brandConfig = await api.getConfig(this.brandId);
     
      await this.SET_CONFIG(brandConfig.data);

      if (this.isReceptionMode) {
        if (this.config.reception_signature) {
          return this.redirect({ name: "Search" });
        }
        throw new Error();
      }
      return this.redirect({ name: "Privacy" });
    } catch (e) {
      return this.redirect({ name: "Error" });
    }
  },
  methods: {
    ...mapActions("brand", ["SET_BRAND", "SET_CONFIG"]),
  }
};
</script>
