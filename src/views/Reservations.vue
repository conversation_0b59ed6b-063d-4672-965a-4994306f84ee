<template>
  <div
    class="reservations main-content main-content-white"
    :style="cssVariables"
  >
    <div class="content flex flex-col h-full">
      <title-component>
        {{ title }}
      </title-component>
      <p class="text-gray-800 mb-8">
        {{ text }}
      </p>
      <div v-if="multipleRooms" class="slider flex flex-grow">
        <Splide :options="slideOptions">
          <SplideSlide
            v-for="(reservation, index) in reservations"
            :key="index"
          >
            <reservation :reservation="reservation" />
          </SplideSlide>
        </Splide>
      </div>
      <reservation v-else :reservation="reservations" />
      <go-back-link
        :text="$t('shared.goToSearch')"
        class="mt-4"
        data-test="rebookLink"
      />
    </div>
  </div>
</template>
<script>
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import reservation from "@/components/reservation/Reservation.component";
import titleComponent from "@/components/shared/title.component";
import goBackLink from "@/components/shared/goBackLink.component";
import { mapActions, mapState } from "vuex";
import { Splide, SplideSlide } from "@splidejs/vue-splide";
import cssVariables from "@/mixins/cssVariables";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import store from "@/store/index";

export default {
  name: "Reservations",
  components: {
    goBackLink,
    titleComponent,
    reservation,
    Splide,
    SplideSlide
  },
  mixins: [startLoading, stopLoading, cssVariables, browserHistoryManagement],
  data() {
    return {
      numberOfReservations: null,
      reservations: null,
      guest: null,
      slideOptions: {
        rewind: true,
        width: "100%",
        fixedWidth: "85%",
        focus: "center",
        gap: 10,
        perPage: 1,
        trimSpace: false
      }
    };
  },
  computed: {
    ...mapState("reservations", [
      "reservationSelected",
      "multipleRooms",
      "data"
    ]),
    title() {
      return this.reservations.length > 1
        ? this.$t("reservations.MultipleReservationsTitle", [
            this.reservations.length
          ])
        : this.$t("reservations.title");
    },
    text() {
      return this.reservations.length > 1
        ? this.$t("reservations.MultipleReservationstext")
        : this.$t("reservations.text");
    }
  },

  methods: {
    ...mapActions("queryParams", ["CLEAR_STATE"])
  },

  beforeRouteEnter(to, from, next) {
    !store.state.reservations.data ? next({ name: "Search" }) : next();
    return;
  },

  created() {
    this.reservations = this.multipleRooms
      ? this.data
      : this.reservationSelected;
    this.CLEAR_STATE();

    this.stopLoading();
  }
};
</script>
<style lang="scss">
.splide__arrow--next {
  right: -1rem !important;
}
.splide__arrow--prev {
  left: -1rem !important;
}
.splide__slide,
.splide__track,
.splide__list {
  height: 100%;
}
.splide--slide {
  width: 100%;
}
.splide__arrow {
  background: var(--bgColor) !important;
}
.splide__arrow svg {
  fill: white;
}
.splide__pagination__page.is-active {
  background: var(--darkenBgColor) !important;
}
</style>
