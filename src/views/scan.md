---
title: Scan
layout: layout.html
orderPath: /__index
eleventyNavigation:
  key: Scan
  parent: Pages
  order: 5
---

# Scan

```mermaid
graph TD
  A[Document] --> B{Upload to OCR}
  B --> |Max Attempts reached| C[Redirect to Error Page]
  B --> |"Document not valid <br>(Schengen zone, Driving License, <br>Document Expired or Min Required Values)"| D["Show modal error"]
  B --> |document type not retrieved or <br>if is a DNI without nationality| E{Ask for sensible data}
  B --> |Valid Document|F
  E --> |DNI out Schengen Zone|D
  E --> |Valid Document|F{Validate Document}
  F --> |Pax type AD and age under<br>18 or viceversa|D
  F --> |Scanned data similar to another <br>guest in same reservation|G[Show Similar Guest Modal]
  G --> |User says is same guest| I[Redirect to Status Page]
  G --> |Not same guest| H[Add document to Store]
  F --> |Valid document| H
```

After the OCR has processed a document, we compare the data it has returned with the list of guests we have collected from the PMS:

First of all we compare the scan data with the selected guest and if they are similar we let it continue directly.

In the case that it does not look similar we compare the scan data against the entire guest list (exceptin selected guest) and if we find that it looks like another guest of the same reservation we show a modal. In this modal we ask if it is this other person. If it says yes, the user will be redirected to the status page, while if it says no, the user will remain on the scan page, allowing the flow to continue.