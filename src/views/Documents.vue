<template>
  <div class="documents main-content main-content-white">
    <modal name="documentProcessInformation" data-test="document-infoModal">
      {{ infoModalFilterCount(documentList.length) }}
    </modal>
    <modal name="notFound"
      >{{ $t("documents.documentListNotLoadedErrorMessage") }}
    </modal>
    <div v-if="documentToShow" class="content h-full flex flex-col">
      <div
        style="animation-duration: 0.3s"
        class="document-details h-full flex flex-col"
      >
        <title-component class="text-center">
          {{ documentToShow.title }}
        </title-component>
        <div
          class="document-details-content mb-8 flex-grow"
          ref="document_content"
          @change="changeDocument"
          data-test="document-content"
        >
          <p v-html="documentToShow.content"></p>
          <ul
            v-if="documentToShow.checkbox_configurations.length"
            data-test="document-checkboxes-list"
            style="list-style-type: none;" 
            class="document-chekbox"
          >
            <li
              v-for="configuration in documentToShow.checkbox_configurations"
              :key="configuration.type"
            >
              <label :for="configuration.type">
                <input
                  style="vertical-align: middle;"
                  type="checkbox"
                  :id="configuration.type"
                  class="form-checkbox focus:outline-none"
                  v-model="checkboxStates[configuration.type]"
                />
                <span style="vertical-align: middle;margin-left:0.5em;" class="text-sm ml-2" v-html="configuration.text"></span>
              </label>
            </li>
          </ul>
        </div>
        <!-- Informs the user about the amount of documents and which one it's on currently -->
        <titleComponent class="text-center" data-test="documents-index">
          {{ currentIndex }}/{{ documentList.length }}
        </titleComponent>
        <btn
          data-test="close-document-button"
          @click.native="closeReadedDocument()"
        >
          {{
            isLastDocument
              ? $t("documents.buttonLastDocument")
              : $t("documents.buttonNextDocument")
          }}
        </btn>
      </div>
    </div>
  </div>
</template>
<script>
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import modal from "@/components/shared/modal.component";
import titleComponent from "@/components/shared/title.component";
import { mapActions, mapState, mapGetters } from "vuex";
import repository from "../repository/repositoryFactory";
import cssVariables from "@/mixins/cssVariables";
import btn from "@/components/shared/button.component";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import setDocumentVariablesValues from "@/mixins/setDocumentVariablesValues";
import errorHandler from "@/mixins/errorHandler";
import redirect from "@/mixins/redirect";
import translateDocuments from "@/services/documents/translateDocuments";
import { Tracker } from "@/tracker";
import Handlebars from "handlebars";

const api = repository.get("checkin");
export default {
  name: "documents",
  components: {
    titleComponent,
    modal,
    btn,
  },
  mixins: [
    startLoading,
    stopLoading,
    cssVariables,
    browserHistoryManagement,
    errorHandler,
    redirect,
    setDocumentVariablesValues,
  ],
  data: () => {
    return {
      documentList: [],
      documentToShow: null,
      checkboxStates: {},
    };
  },

  watch: {
    "$i18n.locale": async function () {
      await this.LOADING(true);

      await this.getBrandDocuments();
      await this.updateDocumentLanguage();

      await this.LOADING(false);
    },
  },

  async created() {
    if (
      this.guestData.pax_type === "CH" &&
      !this.config.children_sign_documents &&
      !this.isReceptionMode
    ) {
      return this.$router.push({ name: "PhoneForm" });
    }
    try {
      const success = await this.getBrandDocuments();
      if (!success) return;
      await this.updateDocumentLanguage();
      await this.$store.dispatch("modal/SET_TYPE", "info");
      await this.$store.dispatch(
        "modal/SET_NAME",
        "documentProcessInformation"
      );
      await this.$store.dispatch("modal/VISIBLE", true);
      await this.$store.dispatch("modal/SET_TITLE", this.$t("documents.title"));
    } catch (error) {
      await this.$store.dispatch("modal/SET_TYPE", "error");
      await this.$store.dispatch("modal/SET_NAME", "notFound");
      await this.$store.dispatch(
        "modal/SET_TITLE",
        this.$t("documents.documentListNotLoadedErrorTitle")
      );
    }
    Tracker.recordCustomEvent("guest_documents_start", {
      guest: this.guestLiteData,
    });
  },
  computed: {
    ...mapState("brand", ["brandId", "config", "name"]),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", {
      guestData: "getSelectedGuest",
      guestLiteData: "getLiteSelectedGuestData",
      getChildGuests: "getChildGuests",
    }),
    ...mapGetters("app", ["isReceptionMode"]),
    isLastDocument() {
      return (
        this.documentToShow &&
        this.documentToShow.key === this.documentList.length - 1
      );
    },
    currentIndex() {
      return this.documentToShow ? this.documentToShow.key + 1 : -1;
    },
  },
  methods: {
    ...mapActions("guest", ["UPDATE_GUEST"]),
    ...mapActions("loading", ["LOADING"]),
    async getBrandDocuments() {
      return api
        .getBrandDocumentsForCurrentGuest(this.brandId)
        .then(async (response) => {
          const documentVariablesValues = this.setDocumentVariablesValues(this.guestData);
          let documentList = [];

          // Get documents based on locale
          documentList = await translateDocuments(
            response.data,
            this.$i18n.locale,
            documentVariablesValues
          );

          documentList.forEach((document) => {
            const template = Handlebars.compile(document.content);
            document.read = false;
            document.content = template(documentVariablesValues);
            document.checkbox_configurations?.forEach((config) => {
              if (!(config.type in this.checkboxStates)) {
                this.$set(this.checkboxStates, config.type, false);
              }
            });
          });
          this.documentToShow = null;

          this.documentList = documentList;
          this.UPDATE_GUEST({ documents: this.documentList });
          if (!this.documentList.length) {
            this.redirect({ name: "PhoneForm" });
            return false;
          }
          this.stopLoading();
          return true;
        })
        .catch((error) => {
          this.errorHandler(error, "Documents view");
          return false;
        });
    },
    //* Used for generating the first document
    //* and also used on watch:{} for it to generate the first document on language change
    updateDocumentLanguage() {
      this.documentToShow = this.documentList[0] || null;
      this.documentToShow.key = 0;
    },

    //* If 1 document on brand it will print singular "Translation", if more than 1 plural "Translation"
    infoModalFilterCount(documentLength) {
      if (documentLength === 1) {
        return this.$t("documents.singularText", { count: documentLength });
      } else {
        return this.$t("documents.pluralText", { count: documentLength });
      }
    },

    changeDocument(event) {
      //* This is required because documents are injected via v-html, therefore they are not reactive
      //* If checkboxes are present inside documents, we need to know if there's changes (checked)

      let input = event.target;
      // * For now, only checkbox are covered, add more to inputMap if needed
      const inputMap = {
        checkbox: () => input.toggleAttribute("checked"),
      };
      inputMap[input.type]();
    },
    allDocumentsRead() {
      this.documentList = this.documentList.map((document) => {
        document.content = this.sanitizeDocumentContent(document.content);
        return document;
      });
      this.UPDATE_GUEST({ documents: this.documentList });
      return this.redirect({ name: "Signature" });
    },

    closeReadedDocument() {
      this.showDocumentContent(this.documentToShow);
      // * Get document html
      let newHtml = this.$refs.document_content?.innerHTML;
      // * Update documents
      this.documentList = this.documentList.map((document) => {
        if (document.id == this.documentToShow.id) {
          document.content = newHtml;
        }
        return document;
      });
      const nextKey = this.documentToShow.key + 1;
      if (nextKey < this.documentList.length) {
        // Set the next document as the current document
        this.documentToShow = this.documentList[nextKey];
        this.documentToShow.key = nextKey;
      } else {
        // If there is no next document, set the current document to null
        this.documentToShow = null;
        this.UPDATE_GUEST({ checkboxStates: this.checkboxStates });
        this.$store.dispatch("loading/LOADING", true);
        this.allDocumentsRead();
      }
    },

    showDocumentContent(document) {
      document.read = true;
      this.documentToShow = document;
    },

    sanitizeDocumentContent(content) {
      return content
        .replace(
          '<span style="display:none">{{documentCount}}</span>',
          "{{documentCount}}"
        )
        .replace(
          '<span style="display:none">{{guestSignature}}</span>',
          "{{guestSignature}}"
        );
    },
  },
};
</script>
<style lang="scss">
.document-details-content p {
  margin-bottom: 14px;
}

.document-icon {
  width: 40px;
  min-width: 40px;
  fill: #a8a8a8;
}
</style>
