---
title: User Flow Diagram
layout: layout.html
eleventyNavigation:
  key: User Flow Diagram
  order: 0
---

# User Flow Diagram

From arriving to the welcome page, to completing the full process of check-in, every user has to follow a flow inside the application.

**This diagram is still not 100% finished.*

```mermaid
flowchart TD

  %% Views

  Start[User arrives to Autocheckin platform]
  Error[Error Page]
  Error2[Error Page]
  Error3[Error Page]
  ErrorModal[Error Modal]
  ErrorModal2[Error Modal]
  ErrorModal3[Error Modal]
  GDPR[GDPR View]
  Search[Search View]
  Status[Status View]
  Manual[Manual Checkin]
  PMS[[Request to PMS]]
  PMSSend[[Send to PMS]]
  Draw[Resolve Draw]
  SelectRes[Select reservation]
  SelectManyRes[Select one of \n the reservations]
  Scan[Scan View]
  ChildForm[ChildForm View]
  OCR[[Request to OCR]]
  ValidateData[Validate Data View]
  Confirmation[Confirmation View]
  Confirmation2[Confirmation View]
  Confirmation3[Confirmation View]
  ChildWarnModal[Warning Modal]
  Documents[Documents View]
  Signature[Signature View]
  PMSSend2[[Send to PMS]]
  GetDocsCopy[Get copy of Documents View]
  PhoneForm[Phone Form View]
  Payments[Payments View]
  Comments[Comments View]
  End[End]
  End2[End]

  %% Conditions

  nodeRouteState{ACI enabled}
  acceptGDPR{User \n accepts \n GDPR}
  PMS-R{PMS returns}
  PMS-R2{PMS returns}
  selectPax{User \n selects \n pax}
  OCR-R{OCR returns}
  ChildAge{Insert \n child age}
  ChildAnswer{Child \n answer}
  maxAttemptsScan{Exceeded \n max attempts}
  confirmation-Condition{All guests \n validated}
  formCompleted{User \n completes \n form}

  %% Define classes

  classDef warning fill:#f1c232,stroke:#bf9000,color:#000000;
  classDef error fill:#e25e5e,stroke:#ff00,color:#000000;
  classDef success fill:#6aa84f,stroke:#38761d,color:#000000;
  classDef request fill:#f9cb9c,stroke:#e69138;

  %% Flow Diagram

  Start --> nodeRouteState
  nodeRouteState --> |Yes|GDPR
  nodeRouteState --> |No|Error:::error
  GDPR --> acceptGDPR
  acceptGDPR --> |Yes|Search
  acceptGDPR --> |No|Manual:::error
  Search --> PMS:::request

  PMS --> PMS-R
  PMS-R --> |A case of draw|Draw
  PMS-R --> |1 user, \n 1 reservation|SelectRes
  PMS-R --> |1 user, \n n reservations|SelectManyRes
  PMS-R --> |All reservations \n completed|Confirmation2
  PMS-R --> |No reservations \n or error|ErrorModal:::error

  Draw --> |User introduces check-in date|PMS
  SelectRes --> Status
  SelectManyRes --> |User selects reservation|Status
  Status --> selectPax
  selectPax --> |Child CH \n or Baby BB|ChildForm
  selectPax --> |Junior JR \n or Adult AD|Scan

  ChildForm ---> ChildAge
  ChildAge --> |Inserted age is below the required \n age to scan document|PMSSend:::request
  ChildAge --> |Inserted age is above the required \n age to scan document|ChildWarnModal:::warning
  ChildWarnModal --> ChildAnswer
  ChildAnswer --> |User agrees to modal|Scan
  ChildAnswer --> |User does not agree to modal|ChildForm
  ChildAnswer --> |User does not agree to modal \n and exceeds max tries|Error2:::error

  PMSSend --> Confirmation
  Confirmation --> confirmation-Condition
  confirmation-Condition --> |No|Status
  confirmation-Condition ----> |Yes|End:::success
  
  direction TB
  Scan --> |User scans document|OCR:::request
  OCR --> OCR-R
  OCR-R ----> |OK|ValidateData
  OCR-R --> |Error|maxAttemptsScan
  maxAttemptsScan ---> |No|ErrorModal2
  maxAttemptsScan ---> |Yes|Error3:::error
  ErrorModal2 --> Scan

  ValidateData --> formCompleted
  formCompleted --> |No|ValidateData
  formCompleted --> |Yes|Documents
  Documents --> Signature
  Signature --> |Send documents data|PMSSend2:::request
  PMSSend2 --> PMS-R2
  PMS-R2 --> |OK|GetDocsCopy
  PMS-R2 --> |Error|ErrorModal3:::error
  ErrorModal3 --> Signature
  GetDocsCopy --> PhoneForm
  PhoneForm --> Payments
  Payments --> Comments
  Comments --> Confirmation3
  Confirmation3 --> End2:::success

```