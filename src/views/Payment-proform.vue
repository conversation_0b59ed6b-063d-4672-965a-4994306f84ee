<template>
  <div class="payments-proform main-content main-content-white">
    <div class="content h-full flex flex-col">
      <title-component class="text-left text-lg mb-3">
        {{ $t("paymentproform.title") }}
      </title-component>
      <div class="proform-detail flex flex-grow flex-col">
        <proform
          :reservations="reservations"
          :currency="charges.currency"
          @remainCalculated="storeRemain"
        />
        <div class="flex flex-row mt-6 items-center">
          <invoice class="mr-4 invoice-icon" />
          <span class="text-sm">{{ $t("paymentproform.invoice") }} </span>
        </div>
      </div>
      <payment-button
        data-test="proform-payment-button"
        class="mt-4"
        v-for="(paymentMethod, index) in paymentMethods"
        :key="index"
        :method="paymentMethod"
        @methodSelected="methodSelect"
      />
      <go-back-link :text="`${$t('paymentproform.goBack')}`" />
    </div>
  </div>
</template>
<script>
import titleComponent from "@/components/shared/title.component";
import proform from "@/components/payment/proform.component";
import invoice from "@/assets/images/icons/invoice.svg";
import paymentButton from "@/components/payment/payment.button.component";
import goBackLink from "@/components/shared/goBackLink.component";
import stopLoading from "@/mixins/stopLoading";
import startLoading from "@/mixins/startLoading";
import { mapState } from "vuex";
import redirect from "../mixins/redirect";
export default {
  name: "payment-proform",
  data() {
    return {
      calculatedRemain: 0
    };
  },
  components: {
    titleComponent,
    proform,
    invoice,
    paymentButton,
    goBackLink
  },
  mixins: [stopLoading, startLoading, redirect],
  computed: {
    ...mapState("payment", ["charges", "integrations"]),
    reservations: function() {
      if (this.charges.localizer.selected) {
        return this.charges.reservations;
      } else {
        let array = [];
        this.charges.reservations.forEach(reservation => {
          if (reservation.selected) array.push(reservation);
        });
        return array;
      }
    },
    paymentMethods: function() {
      let paymentMethods = [];
      this.integrations.forEach(integration => {
        paymentMethods.push({
          platform: integration.platform.name,
          method: integration.method.name
        });
      });
      return paymentMethods;
    }
  },
  created() {
    this.stopLoading();
  },
  methods: {
    storeRemain($event) {
      this.calculatedRemain = $event;
    },
    methodSelect: async function($event) {
      await this.$store.dispatch("loading/LOADING", true);
      await this.$store.dispatch(
        "payment/SET_PLATFORM_METHOD_AND_PAYMENT_DATA",
        {
          platform: $event.platform,
          method: $event.method,
          amount: this.calculatedRemain,
          currency: this.charges.currency
        }
      );
      this.redirect({ name: "PaymentForm" });
    }
  }
};
</script>
<style lang="scss" scoped>
.invoice-icon {
  width: 50px;
}
</style>
