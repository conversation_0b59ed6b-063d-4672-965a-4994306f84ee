<template>
  <div class="brandSelector main-content main-content-white">
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("brandselector.title") }}</title-component>
      <div class="flex-grow">
        <p class="text-gray-800 mb-8">
          {{ $t("brandselector.body") }}
        </p>
        <form-input
          @inputChanged="brandChanged($event)"
          class="mb-6"
          :active="true"
          :optional="false"
          :name="$t('brandselector.inputTitle')"
          :type="'select'"
          :value="brandId"
          :inputName="'brand_name'"
          :check-on-start="false"
          :options="brandOptions"
        />
      </div>
      <btn
        data-test="brandSelectorButton"
        @click.native="startCheckin"
        :disabled="!brandId"
      >
        {{ $t("brandselector.button") }}
      </btn>
    </div>
  </div>
</template>
<script>
import stopLoading from "../mixins/stopLoading";
import startLoading from "@/mixins/startLoading";
import btn from "@/components/shared/button.component";
import titleComponent from "@/components/shared/title.component";
import { mapActions, mapGetters, mapState } from "vuex";
import formInput from "@/components/search/formInput.component";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import errorHandler from "@/mixins/errorHandler";
import clearStoreAndRedirect from "../mixins/clearStoreAndRedirect";
import redirect from "@/mixins/redirect";

export default {
  name: "BrandSelector",
  mixins: [
    redirect,
    stopLoading,
    startLoading,
    browserHistoryManagement,
    errorHandler,
    clearStoreAndRedirect
  ],
  components: { btn, titleComponent, formInput },
  data: () => {
    return {
      brandOptions: [],
      brandId: null
    };
  },
 
  computed: {
    ...mapGetters("brand", ["getAutocheckinActive"]),
    ...mapState("queryParams", { queryParam: "data" }),
    ...mapState("brand", { parentBrandId: "brandId" }),
  },
  async mounted() {
    if (this.getAutocheckinActive.length === 1) {
      return this.redirect({ path: `/${this.getAutocheckinActive[0].id}` });
    }

    const autocheckinFromParams = this.getAutocheckinActive.find(
      brand => brand.id == this.queryParam?.brand_id
    );

    if (autocheckinFromParams) {
      this.brandId = autocheckinFromParams.id;
      return await this.startCheckin();
    }

    this.brandOptions = this.getBrandOptions();
    this.stopLoading();
  },
  methods: {
    ...mapActions("brand", ["SET_BRAND_ID"]),
    ...mapActions("trace", ["SET_PARENT_BRAND_ID"]),
    ...mapActions("queryParams", {
      SET_QUERY_PARAM: "SET_DATA"
    }),

    async startCheckin() {
      const parentBrand = this.parentBrandId;
      const params = this.queryParam;
      await this.clearStoreAndRedirect();
      await this.SET_QUERY_PARAM(params);
      this.SET_PARENT_BRAND_ID(parentBrand);
    },
    getBrandOptions() {
      let brandOptions = [];
      this.getAutocheckinActive.forEach(brand => {
        return brandOptions.push({
          value: brand.id.toString(),
          name: brand.name
        });
      });

      brandOptions.sort((a, b) => {
        return a.name.toUpperCase().localeCompare(b.name.toUpperCase());
      });

      return brandOptions;
    },
    brandChanged($event) {
      this.brandId = $event.value;
    }
  }
};
</script>
