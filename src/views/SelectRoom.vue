<template>
  <div class="select-room main-content main-content-white">
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("selectroom.title") }}</title-component>
      <div class="room-container flex-grow">
        <room class="mb-2" roomType="ind" :roomCompleted="true" />
        <room
          roomType="double"
          :roomCompleted="false"
          @click.native="redirect({ name: 'Documents' })"
        />
      </div>
      <btn :disabled="!enableButton">{{ $t("selectroom.buttonContinue") }}</btn>
    </div>
  </div>
</template>

<script>
import room from "@/components/selectRoom/room.component";
import titleComponent from "@/components/shared/title.component";
import btn from "@/components/shared/button.component";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";
// import { mapState } from 'vuex';

export default {
  name: "selectRoom",
  data() {
    return {
      enableButton: false
    };
  },
  components: {
    room,
    titleComponent,
    btn
  },
  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect],
  created() {
    // TODO: wait for multiple room functionality and uncomment de code
    // if (this.multipleRooms) {
    //   return this.redirect({ name: "Documents"} );
    // }
    this.stopLoading();
  }

  // computed: {
  //   ...mapState("reservations", ["reservationSelected", "multipleRooms"])
  // }
};
</script>
