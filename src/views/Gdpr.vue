<template>
  <div class="main-content main-content-white">
    <scrollButton
      :showGDPRText="showGDPRText"
      :GDPRTextLoaded="GDPRTextLoaded"
    />
    <modal name="notFound"
      >{{ $t("documents.documentListNotLoadedErrorMessage") }}
    </modal>
    <div class="content h-full flex flex-col" v-show="showGDPRText === false">
      <title-component>{{ $t("privacy.title") }}</title-component>
      <p
        class="text-gray-800 legal-text mb-8 flex-grow"
        v-html="gdprText"
        data-test="gdprText"
      ></p>
      <btn id="acceptButton" @click.native="acceptPrivacy(true)" data-test="acceptConditionsButton">
        {{ $t("privacy.button") }}
      </btn>
      <go-back-link
        v-show="isReceptionMode"
        :text="$t('shared.previousStep')"
      />
    </div>
    <transition
      enter-active-class="animated fadeInRight"
      leave-active-class="animated fadeOutLeft"
    >
      <div
        style="animation-duration: 0.3s"
        class="content h-full flex flex-col"
        v-show="showGDPRText === true"
      >
        <title-component>{{ $t("privacy.title") }}</title-component>
        <div
          class="text-gray-800 privacy-details mb-8 flex-grow"
          v-html="modalTextGDPR"
        />
        <btn id="hidePrivacyButton" @click.native="hidePrivacy">
          {{ $t("privacy.privacyRead") }}
        </btn>
      </div>
    </transition>
  </div>
</template>
<script>
import stopLoading from "@/mixins/stopLoading";
import startLoading from "@/mixins/startLoading";
import repository from "../repository/repositoryFactory";
import scrollButton from "../components/scrollButton/scrollButton.vue"
import btn from "@/components/shared/button.component";
import titleComponent from "@/components/shared/title.component";
import modal from "@/components/shared/modal.component";
import { mapState, mapActions, mapGetters } from "vuex";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import errorHandler from "@/mixins/errorHandler";
import redirect from "@/mixins/redirect";
import goBackLink from "@/components/shared/goBackLink.component";
import { Tracker } from "@/tracker";

const AutoCheckinApi = repository.get("checkin");

const api = repository.get("brand");
export default {
  name: "Privacy",
  mixins: [
    stopLoading,
    startLoading,
    browserHistoryManagement,
    errorHandler,
    redirect
  ],
  components: { btn, titleComponent, modal, goBackLink, scrollButton },
  data: () => {
    return {
      gdprText: undefined,
      gdprTextType: 3,
      modalTextGDPR: undefined,
      showGDPRText: false,
      GDPRTextLoaded: false
    };
  },

  watch: {
    "$i18n.locale": async function() {
      await this.LOADING(true);

      await this.manageGDPRText();

      await this.LOADING(false);
    }
  },

  computed: {
    ...mapState("brand", ["brandId", "config"]),
    ...mapGetters("app", ["isReceptionMode"]),
    ...mapGetters("guest", {
      guestData: "getSelectedGuest",
      guestsWithSignedDocuments: "getGuestsWithSignedDocuments"
    }),
    ...mapState("gdpr", ["text", "modal", "lang"]),
    ...mapState("trace", { trace_id: "id" })
  },
  async mounted() {
    Tracker.recordCustomEvent("gdpr_start", {});
    if (this.isReceptionMode && this.guestsWithSignedDocuments.length) {
      await this.acceptPrivacy(false);
    }

    if (!Object.keys(this.config).length) {
      console.error("GDPR page: No config data present");
      return this.redirect({ name: "Error" });
    }
    if (!this.text || this.lang != this.$i18n.locale) {
      try {
        await this.manageGDPRText();
        this.stopLoading();
      } catch (e) {
        this.errorHandler(e, "GDPR View, getGdpr error");
      }
    } else {
      const defaultText = await this.text;
      this.modalTextGDPR = await this.modal;

      this.SET_GDPR_LANG(this.$i18n.locale);

      if (this.config?.custom_gdpr_text) {
        await this.getCustomGDPRText();
        if (!this.gdprText) {
          // When checkbox from app is not active
          this.gdprText = defaultText;
        }
      } else {
        this.gdprText = defaultText;
      }
      this.GDPRTextLoaded = false
      await this.SET_GDPR_TEXT(this.gdprText);
      this.GDPRTextLoaded = true

      this.addGDPRLinkEvent();
      this.stopLoading();
    }
  },
  methods: {
    ...mapActions("gdpr", [
      "SET_GDPR_ACCEPTED",
      "SET_GDPR_TEXT",
      "SET_GDPR_MODAL",
      "SET_GDPR_LANG"
    ]),
    ...mapActions("loading", ["LOADING"]),

    acceptPrivacy(manual) {
      this.$store.dispatch("loading/LOADING", true);
      this.SET_GDPR_ACCEPTED(true);
      if (this.isReceptionMode) {
        if (this.guestData.pax_type === "AD" || this.guestData.pax_type === null || this.config.scan_children_like_adults){
          return this.config.scan_on_reception ? this.redirect({ name: "Scan" }) : this.redirect({ name: "ValidateData" })
        } else {
          return this.redirect({ name: "ChildForm" });
        }      
      }   
      
      Tracker.recordCustomEvent("gdpr_completed", {manual});

      return this.redirect({ name: "Search" });
    },
    hidePrivacy() {
      this.showGDPRText = false;
    },
    addGDPRLinkEvent: function() {
      let privacyLinks = document.querySelectorAll(".privacy");
      let links = Array.from(privacyLinks);

      links.forEach(link =>
        link.addEventListener("click", this.onClickGDPR, false)
      );
    },
    onClickGDPR: function() {
      this.showGDPRText = true;
    },

    async getCustomGDPRText() {
      this.gdprText = await AutoCheckinApi.getCustomizedText(
        this.brandId,
        this.gdprTextType,
        this.$i18n.locale
      )
        .then(response => response.data[0].translations[0]?.text || null)
        .catch(async error => {
          console.error("Error getting gdpr legal text", { error });
          return null;
        });
    },

    async manageGDPRText() {
      const getGDPRText = await api.getBrandGDPR(
        this.brandId,
        this.$i18n.locale
      );
      this.SET_GDPR_LANG(this.$i18n.locale);
      if (this.config?.custom_gdpr_text) {
        await this.getCustomGDPRText();
      }

      if (this.config?.custom_gdpr_text) {
        await this.getCustomGDPRText();
        if (!this.gdprText) {
          // When checkbox from app is not active
          this.gdprText = getGDPRText.data.second_eprivacy_page;
        }
      } else {
        this.gdprText = getGDPRText.data.second_eprivacy_page;
      }
      this.GDPRTextLoaded = false
      await this.SET_GDPR_TEXT(this.gdprText);
      this.GDPRTextLoaded = true
      this.modalTextGDPR = getGDPRText.data.legal_text;
      await this.SET_GDPR_MODAL(this.modalTextGDPR);
      this.addGDPRLinkEvent();
    }
  }
};
</script>
<style lang="scss" scoped>
.legal-text ::v-deep a,
.privacy-details ::v-deep a {
  color: #65c3df;
}

.legal-text ::v-deep a:hover,
.privacy-details ::v-deep a:hover {
  color: #2ba8cd;
  cursor: pointer;
}

.privacy-details ::v-deep p {
  margin-bottom: 14px;
}

.privacy-details ::v-deep ul {
  display: block;
  list-style-type: disc;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 40px;
}

.privacy-details ::v-deep li {
  margin-bottom: 1em;
}

.privacy-details ::v-deep h2 {
  font-size: 2em;
}

.privacy-details ::v-deep h4 {
  font-size: 1.25em;
  margin-bottom: 0.5em;
}
</style>
