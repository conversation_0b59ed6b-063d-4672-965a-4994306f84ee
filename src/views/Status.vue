<template>
  <div class="status main-content main-content-white">
    <div class="content h-full flex flex-col">
      <modal
        name="goBackToReservationsView"
        :button="false"
        class="text-black"
        data-test="go-back-to-reservations-modal"
      >
        <p>{{ $t("header.modalContent") }}</p>
        <div class="btn-wrapper flex mt-8 justify-between">
          <btn @click.native="closeModal" data-test="close-modal">
            {{ $t("shared.no") }}
          </btn>
          <btn
            class="bg-white hover:bg-gray-50 border"
            textColor="black"
            @click.native="goBackToReservationsView"
            data-test="go-back-to-reservations"
          >
            {{ $t("shared.yes") }}
          </btn>
        </div>
      </modal>
      <title-component>{{ title }}</title-component>
      <p class="text-gray-800 mb-4">
        {{ text }}
      </p>
      <div class="status-wrapper flex-grow">
        <status-component
          class="mb-1"
          v-for="(pax, index) in orderGuests(this.guestList)"
          :key="index"
          :pax="pax"
          @click.native="goToPage(pax, index)"
          data-test="guestList"
        />
      </div>
      <go-back-link
        @openGoBackModal="openGoBackModal"
        :text="
          isReceptionMode && !multipleRooms
            ? $t('shared.goToSearch')
            : $t('shared.previousStep')
        "
        :showLoading="isReceptionMode && !multipleRooms"
        :goBackModal="!config.partial_checkin"
        class="mt-10"
      />
    </div>
  </div>
</template>
<script>
import { nextTick } from "vue";
import { mapActions, mapGetters, mapState } from "vuex";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import redirect from "@/mixins/redirect";
import browserHistoryManagement from "@/mixins/browserHistoryManagement";
import goBackLink from "@/components/shared/goBackLink.component";
import titleComponent from "@/components/shared/title.component";
import statusComponent from "@/components/status/status.component";
import modal from "@/components/shared/modal.component";
import btn from "@/components/shared/button.component";
import * as guestSort from "@/utils/guestSort";
import { Tracker } from "@/tracker";

export default {
  name: "status",
  components: { titleComponent, statusComponent, goBackLink, modal, btn },
  data() {
    return {
      isErrorMessage: false,
      modalMessage: null
    };
  },

  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect],

  computed: {
    ...mapState("reservations", ["reservationSelected", "multipleRooms"]),
    ...mapState("brand", ["config"]),
    ...mapState("guest", {
      guestList: "list"
    }),
    ...mapState("app", ["appLanguage"]),
    ...mapState("loading", ["loading"]),
    ...mapGetters("app", ["isReceptionMode"]),
    ...mapGetters("reservations", ["getReservationCompleted"]),
    ...mapGetters("guest", ["getGuestsCheckinComplete"]),

    title() {
      return this.isReceptionMode
        ? this.$t("status.receptionTitle")
        : this.$t("status.title");
    },
    text() {
      return this.isReceptionMode
        ? this.$t("status.receptionText")
        : this.$t("status.text");
    }
  },

  methods: {
    ...mapActions("guest", [
      "SELECT_GUEST",
      "CLEAR_SELECTED_GUESTS",
      "SET_GUEST_TO_LIST"
    ]),
    ...mapActions("scan", { CLEAR_SCAN: "CLEAR_STATE" }),
    ...mapActions("reservations", [
      "SET_RESERVATION_CHECKIN_COMPLETE",
      "RESET_SESSION_GUESTS"
    ]),
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("app", ["SET_APP_LANGUAGE"]),
    ...mapActions("modal", ["VISIBLE", "CLEAR_STATE", "SET_NAME", "SET_TITLE", "SET_TYPE"]),

    async goToPage(paxInfo) {
    
      await this.LOADING(true);
      this.SELECT_GUEST(paxInfo.uuid);
      await nextTick();
      Tracker.recordCustomEvent("guest_selected", {
        guest: paxInfo
      });

      if (this.isReceptionMode) {
      //Check that the selected guest has a preferred language that is different from the holder's 
        if(paxInfo.lang && paxInfo.lang !== this.appLanguage){
        this.$root.$i18n.locale = paxInfo.lang;
        await this.SET_APP_LANGUAGE(this.$i18n.locale);
        }

        return this.redirect({ name: "Privacy" });
      }

      if (paxInfo.pax_type === "AD" || paxInfo.pax_type === null) {
        return this.config.disable_scan ? this.redirect({ name: "ValidateData" }) : this.redirect({ name: "Scan" })
      } else {
        if (this.checkAdultsInRoom()) {
          // Check if the brand has activated the option to treat childs like adults, if so, redirect to the scan page
          if(this.config.disable_scan && this.config.scan_children_like_adults){
            return this.redirect({ name: "ValidateData" });
          } else if (this.config.scan_children_like_adults){
            this.redirect({ name: "Scan" })
          } else {
            this.redirect({ name: "ChildForm" })
          }
        } else {
          this.isErrorMessage = true;
          this.modalMessage = this.$t("status.noAdultsAssignedToRoomMessage");
        }
      }
    },
    checkAdultsInRoom() {
      const adultsInRoom = this.reservationSelected.guests.filter(guest => {
        return guest.pax_type === "AD";
      });

      if (adultsInRoom.length === 0) {
        return false;
      }

      return true;
    },
    async openGoBackModal() {
      await this.SET_NAME("goBackToReservationsView");
      await this.SET_TITLE(this.$t("header.modalTitle"));
      await this.VISIBLE(true);
    },
    closeModal() {
      this.CLEAR_STATE();
      this.VISIBLE(false);
    },
    async goBackToReservationsView() {
      this.closeModal();
      this.RESET_SESSION_GUESTS();
      return this.redirect({ name: "Reservations" });
    },
    orderGuests(guests) {
      return [...guests].sort((guest, nextGuest) => {
        return (
          guestSort.sortByHolder(nextGuest, guest) ||
          guestSort.sortByValidated(nextGuest, guest) ||
          guestSort.sortByPaxType(nextGuest, guest) ||
          guestSort.sortByName(nextGuest, guest)
        );
      });
    }
  },
  async created() {
    if (!this.isReceptionMode) {
      // Check if all guest have checkinComplete and Finish
      if (this.getReservationCompleted(true)) {
        return this.redirect({ name: "Confirmation" });
      }
    }
    this.stopLoading();
    Tracker.recordCustomEvent("guest_start", {});
  }
};
</script>

<style lang="scss" scoped>
.info-button {
  height: 25px;
  width: 25px;
  padding: 0 !important;
}
.btn-wrapper > button {
  @apply w-32 md:w-40;
}
</style>
