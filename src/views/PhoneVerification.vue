<template>
  <div
    class="phone-verification main-content main-content-white"
    :style="cssVariables"
  >
    <modal @closeModal="closeModal">{{ modalMessage }}</modal>
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("phoneverification.title") }}</title-component>
      <p class="text-gray-800 mb-4">
        {{ $t("phoneverification.text") }}
      </p>
      <div class="phone-verification-wrapper flex-grow">
        <form-input
          :value="code"
          inputName="verification_code"
          :name="$t('phoneverification.label')"
          @inputChanged="inputChanged($event)"
        />
      </div>
      <p class="text-xs mb-6">
        {{ $t("phoneform.codeNotRecived") }}
        <button
          data-test="resend-code"
          class="main-color-link"
          @click="resendCode"
        >
          {{ $t("phoneform.tryAgain") }}
        </button>
      </p>
      <btn
        :disabled="codeError"
        @click.native="validatePhone"
        data-test="validate-phone"
        >{{ $t("phoneverification.verifyYourPhone") }}</btn
      >
      <go-back-link :text="$t('shared.previousStep')" />
    </div>
  </div>
</template>
<script>
import stopLoading from "../mixins/stopLoading";
import titleComponent from "@/components/shared/title.component";
import formInput from "@/components/search/formInput.component";
import btn from "@/components/shared/button.component";
import modal from "@/components/shared/modal.component";
import { mapState, mapActions, mapGetters } from "vuex";
import repository from "../repository/repositoryFactory";
import cssVariables from "@/mixins/cssVariables";
import checkAttempExceeded from "@/mixins/checkAttempExceeded";
import redirect from "@/mixins/redirect";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import goBackLink from "@/components/shared/goBackLink.component";
import { Tracker } from "@/tracker";
const api = repository.get("checkin");

export default {
  name: "phone-verification",
  data: () => {
    return {
      code: null,
      codeError: true,
      modalMessage: ""
    };
  },
  mixins: [
    stopLoading,
    cssVariables,
    browserHistoryManagement,
    redirect,
    checkAttempExceeded
  ],
  components: {
    titleComponent,
    formInput,
    btn,
    modal,
    goBackLink
  },
  created() {
    this.stopLoading();
    if (
      this.checkAttempExceeded(
        this.config.max_attempts_validate_telephone_code,
        this.attemptCode
      ) ||
      this.checkAttempExceeded(this.config.max_attempts_telephone, this.attempt)
    ) {
      this.SET_TITLE(this.$t("error.header"));
      this.showModalError(
        this.$t("phoneform.phoneNumberMaximumAttemptsExceeded")
      );
    }
  },

  computed: {
    ...mapState("reservations", ["reservationSelected"]),
    ...mapState("phone", ["data", "attemptCode", "attempt"]),
    ...mapState("brand", ["config"]),
    ...mapGetters("guest", ["getSelectedGuest", "getLiteSelectedGuestData"])
  },
  methods: {
    ...mapActions("phone", ["SET_DATA", "ADD_ATTEMPT_CODE", "ADD_ATTEMPT", "ADD_VALIDATED_PHONE"]),
    ...mapActions("guest", ["UPDATE_GUEST"]),

    ...mapActions("modal", [
      "VISIBLE",
      "SET_TYPE",
      "SET_TITLE",
      "SET_BUTTON_MESSAGE"
    ]),

    inputChanged(event) {
      this.code = event.value;
      this.codeError = event.error || event.value == "";
    },
    async validatePhone() {
      await this.$store.dispatch("loading/LOADING", { status: true });
      await api
        .sendPhoneCode(this.data.id, this.code)
        .then(async () => {
          await this.UPDATE_GUEST({
            telephone: {
              ...this.getSelectedGuest.telephone,
              value: this.data.phone
            }
          });
          await this.ADD_VALIDATED_PHONE(this.data.phone)
          this.trackPhoneValidated(true);
          if (this.config.paymentsActive) {
            return await this.finishPhoneProcess("Payment");
          } else {
            return await this.finishPhoneProcess("Comments");
          }
        })
        .catch(error => {
          console.error("Phone API verification error", { error });
          this.ADD_ATTEMPT_CODE();
          let modalErroMessage = this.$t("phoneform.phoneCodeErrorMessage");
          if (
            this.checkAttempExceeded(
              this.config.max_attempts_validate_telephone_code,
              this.attemptCode
            )
          ) {
            this.SET_TITLE(this.$t("error.header"));
            modalErroMessage = this.$t(
              "phoneform.phoneNumberMaximumAttemptsExceeded"
            );
          } else {
            this.SET_TITLE(this.$t("phoneform.phoneCodeErrorTitle"));
            this.SET_BUTTON_MESSAGE(this.$t("phoneform.phoneCodeErrorButton"));
          }
          this.showModalError(modalErroMessage);
          this.stopLoading();
          this.trackPhoneValidated(false);
        });
    },

    async resendCode() {
      await this.$store.dispatch("loading/LOADING", true);
      this.ADD_ATTEMPT();
      if (
        this.checkAttempExceeded(
          this.config.max_attempts_telephone,
          this.attempt
        )
      ) {
        this.showModalError(
          this.$t("phoneform.phoneNumberMaximumAttemptsExceeded")
        );
      } else {
        await api
          .validatePhone(this.data)
          .then(async response => {
            await this.SET_DATA(response.data);
          })
          .catch(error => {
            console.error("Phone API validation error", { error });

            this.showModalError(this.$t("phoneform.phoneNumberErrorMessage"));
          });
      }
      this.stopLoading();
    },

    async showModalError(message) {
      await this.SET_TYPE("error");
      this.SET_TITLE(this.$t("error.header"));
      this.modalMessage = message;
      await this.VISIBLE(true);
    },

    async closeModal() {
      await this.$store.dispatch("loading/LOADING", true);
      if (
        this.checkAttempExceeded(
          this.config.max_attempts_validate_telephone_code,
          this.attemptCode
        ) ||
        this.checkAttempExceeded(
          this.config.max_attempts_telephone,
          this.attempt
        )
      ) {
        if (this.config.paymentsActive) {
          return await this.finishPhoneProcess("Payment");
        } else {
          return await this.finishPhoneProcess("Comments");
        }
      }
      this.stopLoading();
    },

    trackPhoneValidated(valid) {
      Tracker.recordCustomEvent("guest_phone_validated", {
        valid,
        guest: this.getLiteSelectedGuestData,
        phone: this.data.phone,
        code: this.code
      });
    },
    
    finishPhoneProcess(route) {
      Tracker.recordCustomEvent("guest_phone_completed", {
        guest: this.getLiteSelectedGuestData,
        phone: this.data.phone
      });

      return this.redirect({ name: route });
    }    
  }
};
</script>
<style lang="scss" scoped>
.main-color-link {
  color: var(--bgColor);
}
</style>
