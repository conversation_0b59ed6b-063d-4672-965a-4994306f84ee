<template>
  <div class="error main-content main-content-white">
    <div class="content h-full flex flex-col">
      <div class="flex-grow">
        <title-component data-test="errorTitle" class="flex-grow text-center">{{
          error.title
        }}</title-component>
        <p
          class="text-gray-800 mb-10 flex-grow text-center"
          v-html="error.message"
          data-test="errorDescription"
        ></p>
      </div>
      <div v-if="!error.exitOption && this.brandId">
        <btn
          class="mb-4"
          v-for="(button, index) in error.buttons"
          :key="index"
          @click.native="redirectToPage(button.route)"
          data-test="previousStep"
        >
          {{ button.text }}
        </btn>
      </div>
    </div>
  </div>
</template>
<script>
import titleComponent from "@/components/shared/title.component";
import btn from "@/components/shared/button.component";
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import { mapActions, mapState } from "vuex";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";

export default {
  name: "error",
  data() {
    return {
      exitOption: true
    };
  },
  components: { titleComponent, btn },
  mixins: [stopLoading, startLoading, browserHistoryManagement, redirect],
  computed: {
    ...mapState("brand", ["brandId"]),

    error: function() {
      let error = {};

      if (this.$route.params.error) {
        error.title = this.$t(`error.${this.$route.params.error}.title`);
        error.message = this.$t(`error.${this.$route.params.error}.message`);

        error.buttons = [
          {
            text: this.$t(`error.${this.$route.params.error}.buttons.default`),
            route: this.$route.params.route || "Privacy"
          }
        ];
        if (this.$route.params.buttons) {
          error.buttons = this.$route.params.buttons.map(button => {
            button.text = this.$t(
              `error.${this.$route.params.error}.buttons.${button.name}`
            );
            return button;
          });
        }

        error.exitOption =
          this.$route.params.error === "error404"
            ? this.brandId
            : this.$route.params.exit;
        return error;
      }

      return {
        title: this.$t("error.default.title"),
        message: this.$t("error.default.message"),
        exitOption: true
      };
    }
  },
  methods: {
    ...mapActions("brand", ["SET_BRAND_ID"]),

    async redirectToPage(route) {
      await this.$store.dispatch("loading/LOADING", true);
      let brandId = this.brandId;
      if (route == "Privacy") {
        Object.keys(this.$store.state).forEach(stateNames => {
          this.$store.commit(`${stateNames}/clearState`);
        });
      }
      this.SET_BRAND_ID(brandId);
      return this.redirect({
        name: route,
        params: { brandId: brandId }
      });
    }
  },
  async mounted() {
    await this.$store.dispatch("modal/VISIBLE", false);
    this.stopLoading();
  }
};
</script>
