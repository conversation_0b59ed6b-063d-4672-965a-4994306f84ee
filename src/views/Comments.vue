<template>
  <div class="comments main-content main-content-white">
    <div class="content h-full flex flex-col">
      <title-component class="text-left text-lg mb-3">{{
        $t("comments.subtitle")
      }}</title-component>

      <div
        class="comments-wrapper flex flex-col overflow-auto scroll-content flex-1 mb-4"
      >
        <div v-if="this.config.comments" data-test="comment-section">
        <div class="flex justify-between content-center">
          <label
            class="font-bold uppercase mt-2 mb-1"
            v-html="$t('comments.commentLabel')"
          ></label>
          <span
            class="text-gray-400 font-black uppercase text-xs float-right mt-5"
            >{{ $t("inputs.optional") }}</span
          >
        </div>

        <text-area-component
          @inputChanged="inputChanged($event)"
          v-model="comments"
          :text="comments"
          :placeholder="$t('comments.commentsPlaceholder')"
          id="text-area-comments"
          :name="'comments'"
          :type="'comments'"
          :maxChars="400"
        />
        <div v-if="commentsCustomText">
          <p
            data-test="commentsCustomText"
            class="my-4"
            v-html="commentsCustomText">
          </p>
        </div>
      </div>
        <div v-if="this.config.arrival_time" data-test="arrival-time-section" >
        <div class="flex justify-between content-center">
          <label
            class="font-bold uppercase mt-6"
            v-html="$t('comments.timeLabel')"
          ></label>
        </div>

        <div class="time-wrapper flex items-center justify-between ">
          <form-input
            @inputChanged="inputChanged($event)"
            v-model="hours"
            class="w-full mr-2 mb-10 appearance-none rounded text-gray-700 leading-tight text-center"
            :placeholder="$t('comments.hoursPlaceholder')"
            type="time"
            id="hours"
            v-bind:optional="true"
            name="hour"
            maxLength="2"
          />
        </div>
        </div>
      </div>

      <btn
        data-test="continue-button"
        class="mt-4"
        @click.native="handleSubmit"
        :navigationButton="false"
        >{{ $t("comments.continueButton") }}</btn
      >
    </div>
  </div>
</template>

<script>
import btn from "@/components/shared/button.component";
import formInput from "@/components/search/formInput.component";
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import { mapActions, mapState, mapGetters } from "vuex";
import TitleComponent from "../components/shared/title.component.vue";
import TextAreaComponent from "../components/comments/textarea.component.vue";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";
import { Tracker } from "@/tracker";
import repository from "../repository/repositoryFactory";

const AutoCheckinApi = repository.get("checkin");

export default {
  name: "comments",

  data() {
    return {
      comments: "",
      hours: "",
      minutes: "",
      isErrorMessage: false,
      modalMessage: null,
      commentsCustomText: "",
      commentsTextType: 5,
    };
  },

  computed: {
    ...mapState("brand", ["brandId", "config"]),
    ...mapState("reservations", ["reservationSelected"]),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData" })
  },

  components: {
    btn,
    formInput,
    TitleComponent,
    TextAreaComponent
  },

  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect],

  async created() {
    if(!this.guestData.holder && this.config.show_comments_only_on_holder){
      return this.redirect({ name: "Confirmation"});
    }

    if (this.config.custom_comments_text) {
      await this.getCustomCommentsText();
    }
    
    this.stopLoading();

    Tracker.recordCustomEvent("guest_comment_start", {
      guest: this.guestLiteData
    });
  },

  watch: {
  "$i18n.locale": async function() {
    if (this.config.custom_comments_text) {
      await this.LOADING(true);
      await this.getCustomCommentsText()
      await this.LOADING(false);
    } 
    }
  },

  methods: {
    ...mapActions("loading", ["LOADING"]),
    ...mapActions("guest", ["UPDATE_GUEST"]),
    async handleSubmit() {
      if (this.comments || this.hours) {
        try {
          // Separates hours and minutes from time input
          [this.hours, this.minutes] = this.splitTime(this.hours);
          await this.$store.dispatch("loading/LOADING", true);
          await this.UPDATE_GUEST({
            comment: {
              comment: this.comments,
              hours: this.hours,
              minutes: this.minutes
            }
          });

          return await this.finishCommentsProcess("Confirmation");
        } catch (error) {
          console.error("Comments error", error);
          this.isErrorMessage = true;
          this.modalMessage = this.$t("comments.sendCommentsErrorMessage");
          this.stopLoading();
        }
      } else {
        return await this.finishCommentsProcess("Confirmation");
      }
    },

    inputChanged($event) {
      if ($event.type === "comments") {
        //Remove line breaks
        this.comments = $event.value.replace(/[\r\n]/gm, "");
      } else if ($event.type === "time") {
        this.hours = $event.value;
      }
    },
    splitTime(time) {
      return time.split(":");
    },
    finishCommentsProcess(route) {
      Tracker.recordCustomEvent("guest_comments_completed", {
        guest: this.guestLiteData,
        comment: this.comments,
        hours: this.hours,
        minutes: this.minutes
      });

      return this.redirect({ name: route });
    },
    async getCustomCommentsText() {
        this.commentsCustomText = await AutoCheckinApi.getCustomizedText(
          this.brandId,
          this.commentsTextType,
          this.$i18n.locale
        )
          .then(response => response.data[0].translations[0]?.text || null)
          .catch(async error => {
            console.error("Error getting custom comments text", { error });
            return null;
          });
    },    
  }
};
</script>
