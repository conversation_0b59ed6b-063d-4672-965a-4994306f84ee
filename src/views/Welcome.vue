<template>
  <div class="welcome main-content">
    <div class="content self-end w-full">
      <h1 class="text-3xl font-black">{{ $t("welcome.welcome") }}</h1>
      <h2 class="text-2xl mb-4">{{ $store.state.brand.name }}</h2>
      <p class="mb-16" v-html="$t('welcome.text')"></p>
      <btn @click.native="navigate('Privacy')">{{ $t("welcome.button") }}</btn>
    </div>
  </div>
</template>

<script>
import repository from "../repository/repositoryFactory";
import stopLoading from "@/mixins/stopLoading";
import btn from "@/components/shared/button.component";
import navigate from "@/mixins/navigate";
import { mapState } from "vuex";
import store from "@/store";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
export default {
  name: "Welcome",
  components: { btn },
  mixins: [stopLoading, navigate, browserHistoryManagement],
  computed: {
    ...mapState("brand", ["brandId"])
  },
  async created() {
    // Get config
    const api = repository.get("brand");
    const responseBrand = await api.getBrand(this.$route.params.brandId);
    await store.dispatch("brand/SET_BRAND", responseBrand);
    const responseConfig = await api.getConfig(this.brandId);
    await this.$store.dispatch("brand/SET_CONFIG", responseConfig);
    this.stopLoading();
  }
};
</script>
<style lang="scss" scoped>
.btn {
  border: 1px solid var(--darkenBgColor);
}
.content {
  padding-top: 0 !important;
}
</style>
