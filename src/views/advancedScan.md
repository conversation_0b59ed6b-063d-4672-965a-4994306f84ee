---
title: Advanced Scan
layout: layout.html
orderPath: /__index
eleventyNavigation:
  key: Advanced Scan
  parent: Pages
  order: 6
---

# Advanced Scan

## Normal operation
The advanced scan page was created because we are having a lot of problems with the normal scan, since the uploaded images are very heavy (sometimes we even exceed the 10MB limits of Api Gateway) and if we compress them too much, the OCR gives us very bad results.

To reduce the size of the images that we send to the OCR, we turn on the device's camera and paint the result in full screen. We also paint a rectangle in the center of the screen so that the user can fit the document in it. In this way we do not send the whole image, but only the part of the rectangle in JPEG format.

The results with the tests performed to date have been quite good as we have seen that the images we send to the OCR are between 18KB and 30KB, a very significant reduction. This has an impact on the response time of the OCR and of course avoids failures due to too large payload.

![Advanced scan page](/src/assets/images/docs/advancedScan/advancedScanExample.jpg)

When the user clicks on the "Take photo" button what we do is create a canvas and capture the video. To do this we have to calculate in which coordinates of the video the cutout zone is located and export it in JPEG format.

Once the "Confirm and scan" button is clicked, the OCR is called with the image obtained from the previous step and we simply wait for the response as we do with the normal scan.

## No permissions to access the camera

However, it is possible that the customer does not give permission for us to access the camera on their device or that there is a problem while trying to access it. For this we add a fallback by painting an informative message and a button to upload a photo. If they press this button they will be able to attach a file as in the normal scan system.

![Advanced scan page with camera permission deined](/src/assets/images/docs/advancedScan/permissionDeined.png)


