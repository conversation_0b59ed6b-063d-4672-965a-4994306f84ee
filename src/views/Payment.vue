<template>
  <div class="payments main-content main-content-white">
    <modal>{{ modalMessage }}</modal>
    <modal name="paymentInformation" @closeModal="closePaymentInformationModal"
      >{{ modalMessage }}
    </modal>
    <div class="content h-full flex flex-col">
      <title-component class="text-left text-lg mb-3">
        {{ $t("payment.title") }}
      </title-component>
      <p class="text-gray-800 mb-4">
        {{ $t("payment.text") }}
        <b data-test="total-amount" class="font-black"
          >{{ localizerAmount }} {{ charges.currency }}</b
        >.<br />
        {{ $t("payment.select") }}
      </p>
      <p class="text-gray-700 font-black uppercase text-sm mb-6">
        {{
          hasMultipleInvoices
            ? this.$t("payment.pay_invoices")
            : this.$t("payment.pay_invoice")
        }}
      </p>
      <reservation-selector
        :data-test="`reservation-selector-${index}`"
        @selected="selectReservation($event)"
        class="mb-2"
        v-for="(reservation, index) in charges.reservations"
        :reservation="reservation"
        :currency="charges.currency"
        :key="index"
      />
      <div
        class="divider flex my-6 items-center text-gray-400"
        v-show="hasMultipleInvoices"
      >
        {{ $t("payment.another_option") }}
      </div>
      <p
        class="text-gray-700 font-black uppercase text-sm mb-6"
        v-show="hasMultipleInvoices"
      >
        {{ $t("payment.pay_all") }}
      </p>
      <localizer-selector
        data-test="localizer-selector"
        v-if="charges.localizer && hasMultipleInvoices"
        :localizer="charges.localizer"
        :currency="charges.currency"
        @selected="localizerSelected"
      />
      <div class="divider flex my-6 items-center text-gray-400">
        {{ $t("payment.or") }}
      </div>
      <div class="no-payment-option-wrapper mb-6 flex-grow">
        <no-payment-option
          data-test="no-payment"
          @selected="noPayment"
          :selected="noPaymentSelected"
        />
      </div>
      <btn
        @click.native="goToNext"
        :disabled="!buttonActive"
        data-test="payment-button"
      >
        {{ !noPaymentSelected ? $t("payment.detail") : $t("payment.continue") }}
      </btn>
    </div>
  </div>
</template>
<script>
import titleComponent from "@/components/shared/title.component";
import stopLoading from "@/mixins/stopLoading";
import startLoading from "@/mixins/startLoading";
import repository from "../repository/repositoryFactory";
import modal from "@/components/shared/modal.component";
import { mapState, mapActions } from "vuex";
import reservationSelector from "@/components/payment/option.component";
import localizerSelector from "@/components/payment/localizer.option.component";
import noPaymentOption from "@/components/payment/no_payment_option.component";
const paymentRepository = repository.get("payment");
const integrationsRepository = repository.get("integration");
import btn from "@/components/shared/button.component";
import redirect from "@/mixins/redirect";
import calculateCharges from "@/services/payment/calculateCharges";
export default {
  name: "payment",
  data() {
    return {
      modalMessage: "",
      reservations: [],
      invoices: [],
      noPaymentSelected: false
    };
  },
  components: {
    modal,
    titleComponent,
    reservationSelector,
    localizerSelector,
    noPaymentOption,
    btn
  },
  computed: {
    ...mapState("brand", ["brandId"]),
    ...mapState("payment", ["integrations", "charges"]),
    ...mapState("reservations", ["reservationSelected"]),
    localizerAmount() {
      return this.charges?.localizer?.amount
        ? this.charges.localizer.amount
        : 0;
    },
    hasMultipleInvoices() {
      return this.charges?.reservations?.length > 1;
    },
    buttonActive() {
      let selectedReservations = this.charges?.reservations?.some(
        reservation => {
          return reservation.selected;
        }
      );
      return (
        selectedReservations ||
        this.charges?.localizer?.selected ||
        this.noPaymentSelected
      );
    }
  },
  mixins: [stopLoading, startLoading, redirect],
  async created() {
    try {
      const paymentIntegrations = await paymentRepository.getBrandIntegrations(
        this.brandId
      );
      await this.SET_PAYMENT_INTEGRATIONS(paymentIntegrations);

      const localizerCharges = await integrationsRepository.getLocalizerCharges(
        this.brandId,
        this.reservationSelected.res_localizer
      );

      await this.SET_PENDING_CHARGES(
        this.normalizePendingCharges(localizerCharges)
      );

      await this.handlePaymentsResponse(this.charges, this.integrations);
    } catch (error) {
      console.error("Payment page error", { error });
      return this.redirect({ name: "Comments" });
    }
  },
  methods: {
    ...mapActions("modal", ["VISIBLE", "SET_TYPE", "SET_TITLE", "SET_NAME"]),
    ...mapActions("payment", [
      "SET_CHARGES_RESPONSE",
      "SET_PAYMENT_INTEGRATIONS",
      "SET_PENDING_CHARGES"
    ]),

    closePaymentInformationModal: function() {
      return this.redirect({ name: "Comments" });
    },
    selectReservation(reservation) {
      if (this.charges) {
        this.charges.localizer.selected = false;
      }
      this.charges?.reservations?.forEach(charge => {
        if (charge.id === reservation.id) {
          charge.selected = !charge.selected;
        }
      });
      this.noPaymentSelected = false;
    },
    localizerSelected() {
      if (this.charges) {
        this.charges.localizer.selected = true;
      }
      this.charges?.reservations?.forEach(charge => {
        charge.selected = false;
      });
      this.noPaymentSelected = false;
    },
    noPayment() {
      this.noPaymentSelected = true;
      if (this.charges) {
        this.charges.localizer.selected = false;
      }
      this.charges?.reservations?.forEach(charge => {
        charge.selected = false;
      });
    },
    async goToNext() {
      if (this.noPaymentSelected) {
        return this.redirect({ name: "Comments" });
      } else {
        return this.redirect({ name: "PaymentProform" });
      }
    },
    normalizePendingCharges(pendingCharges) {
      let charges = {
        localizer: {
          amount: calculateCharges.getLocalizerAmount(pendingCharges),
          selected: false
        },
        reservations: calculateCharges.getReservationsInvoices(pendingCharges),
        currency: calculateCharges.getInvoicesCurrency(pendingCharges)
      };

      return charges;
    },
    getInvoices(reservations) {
      let invoices = [];
      reservations?.forEach(reservation => {
        reservation?.invoices?.forEach(invoice => {
          invoices.push(invoice);
        });
      });
      return invoices;
    },
    async handlePaymentsResponse(charges, integration) {
      let invoices = this.getInvoices(charges?.reservations);
      if (!charges?.currency) {
        console.error("Payment page, no currency retrieved");
        return this.redirect({ name: "Comments" });
      }
      if (
        charges?.reservations?.length &&
        invoices.length &&
        integration.length
      ) {
        console.debug("has invoices and integration");
        if (charges?.localizer?.amount === 0) {
          console.debug("No pending charges");
          return this.redirect({ name: "Comments" });
        }
        this.stopLoading();
      } else if (
        charges?.reservations?.length &&
        invoices.length == 0 &&
        integration.length
      ) {
        console.debug("has integration but no invoices");
        return this.redirect({ name: "Comments" });
      } else if (
        integration.length == 0 &&
        this.charges?.reservations?.length &&
        invoices.length
      ) {
        console.debug("has invoices but no integration", {
          integration: integration.length,
          charges: charges
        });
        await this.$store.dispatch("modal/SET_NAME", "paymentInformation");
        await this.$store.dispatch(
          "modal/SET_TITLE",
          this.$t("payment.modal_info_title")
        );
        await this.$store.dispatch("modal/SET_TYPE", "info");
        this.modalMessage = this.$t("payment.modal_info_text");
        this.stopLoading();
        await this.$store.dispatch("modal/VISIBLE", true);
      } else {
        console.debug("No invoices or integrations redirect");
        // Redirect to Next step
        return this.redirect({ name: "Comments" });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.divider {
  &:before,
  &:after {
    content: "";
    flex: 1;
    height: 2px;
    margin: 0 1em;
    background: rgb(229, 231, 235);
  }
}
</style>
