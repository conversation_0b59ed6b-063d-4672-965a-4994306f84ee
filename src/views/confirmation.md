---
title: Confirmation
layout: layout.html
eleventyNavigation:
  key: Confirmation
  parent: Pages
  order: 8
---

# Confirmation

## Confirmation State Diagram

Possible states of Confirmation page.

```mermaid
stateDiagram-v2
state routeState <<choice>>
[*] --> routeState: User arrives to Confirmation
state "Confirmation View States" as CView
routeState --> CView: OK
  state CView {
    state "Partial Checkin Modal" as PartModal
    state "Not Partial Checkin Modal" as NotPartModal
    state "Confirmation Page" as CPage
    state completed <<choice>>
    state enabledModal <<choice>>
    state partialCheckin <<choice>>
    [*] --> completed: Are all check-ins completed?
    completed --> enabledModal: No. Is modal enabled?
    completed --> CPage: Yes
    enabledModal --> CPage: No
    enabledModal --> partialCheckin: Yes. Is partial checkin enabled?
    partialCheckin --> PartModal: Yes
    partialCheckin --> NotPartModal: No
    PartModal --> CPage: Click continue \n check-in
    NotPartModal --> CPage: Click continue \n check-in
  }
CView --> Customizables
state Customizables {
  direction LR
  state "Custom confirmation text and QR" as CustomQR
  state "Custom confirmation text" as Custom
  state "Default configuration" as Default
  state config <<choice>>
  [*] --> config: Possible configs
  config --> CustomQR
  config --> Custom
  config --> Default
  config --> Minimal
}
routeState --> Error: No reservation selected, or error
Error --> [*]
```

---

## Summary of possible states

- Not partial checkin modal

![ConfirmationModal](/src/assets/images/docs/confirmation/confirmation-modal.png)

---

- Partial checkin modal

![ConfirmationModalPartial](/src/assets/images/docs/confirmation/confirmation-modal-partial.png)

<br/>

If you need to know more details about how the calculation of check-ins is done, take a look to the [checkinCounter](/src/components/confirmation/checkinCounter/).

---

<br/>

- Custom confirmation text and QR

![Confirmation 1](/src/assets/images/docs/confirmation/confirmation-1.png)

---

- Custom confirmation text

![Confirmation 2](/src/assets/images/docs/confirmation/confirmation-2.png)

---

- Default configuration

![Confirmation 3](/src/assets/images/docs/confirmation/confirmation-3.png)

---

- Minimal

![Confirmation 4](/src/assets/images/docs/confirmation/confirmation-4.png)
