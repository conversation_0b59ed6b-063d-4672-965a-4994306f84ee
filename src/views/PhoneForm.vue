<template>
  <div class="phone-form main-content main-content-white">
    <modal @closeModal="closeModal">
      {{ this.modalMessage }}
    </modal>
    <div class="content h-full flex flex-col">
      <title-component>{{ $t("phoneform.title") }} </title-component>
      <p class="text-gray-800 mb-4">
        {{ $t("phoneform.info") }}
      </p>
      <div class="phone-form-wrapper flex-grow">
        <form-input
          ref="phoneNumber"
          class="mb-6"
          :name="$t('phoneform.phoneNumberLabel')"
          type="phone"
          :optional="true"
          :countryCode="countryCode"
          :value="phoneNumber"
          inputName="phone_number"
          @inputChanged="inputChanged"
          :check-on-start="true"
        />
        <p class="text-sm mb-6" v-html="alertSMS" v-if="config.custom_phone_text"></p>
      </div>
      <div class="mb-2 mt-2" v-if="config.show_send_newsletter_checkbox">
        <label for="commercialSMS">
          <input
            type="checkbox"
            id="commercialSMS"
            class="form-checkbox h-5 w-5 focus:outline-none"
          />
          <span class="text-sm ml-2">{{ $t("phoneform.checkbox") }}</span>
        </label>
      </div>
      <div class="mb-2 mt-2" v-if="config.show_save_phone_in_database_checkbox">
        <label for="keep-phone-number">
          <input
            type="checkbox"
            id="keep-phone-number"
            class="form-checkbox h-5 w-5 focus:outline-none"
            v-model="keep_phone"
          />
          <span class="text-sm ml-2">{{ $t("phoneform.keepPhone") }}</span>
        </label>
      </div>
      <btn
        data-test="continue-button"
        class="mt-6"
        @click.native="validatePhone"
        :disabled="disabledButton"
        >{{ $t("phoneform.continue") }}</btn
      >
    </div>
  </div>
</template>
<script>
import { mapState, mapActions, mapGetters } from "vuex";
import startLoading from "@/mixins/startLoading";
import stopLoading from "@/mixins/stopLoading";
import redirect from "@/mixins/redirect";
import checkAttempExceeded from "@/mixins/checkAttempExceeded";
import browserHistoryManagement from "@/mixins/browserHistoryManagement";
import modal from "@/components/shared/modal.component";
import titleComponent from "@/components/shared/title.component";
import formInput from "@/components/search/formInput.component";
import btn from "@/components/shared/button.component";
import repository from "../repository/repositoryFactory";
import { phoneCountries, countryList } from "@/utils/countries";
import { Tracker } from "@/tracker";
const api = repository.get("checkin");

export default {
  name: "phone-form",

  components: {
    titleComponent,
    formInput,
    btn,
    modal
  },

  mixins: [
    startLoading,
    stopLoading,
    browserHistoryManagement,
    redirect,
    checkAttempExceeded,
  ],

  data() {
    return {
      phoneNumber: "",
      countryCode: "",
      dialCode: "",
      invalidFormButton: true,
      disabledButton: false,
      modalMessage: "",
      keep_phone: false,
      alertSMS: null,
      alertSMSType: 4,
    };
  },

  computed: {
    ...mapState("reservations", ["reservationSelected"]),
    ...mapState("brand", ["mainColor", "brandId", "config"]),
    ...mapState("phone", ["attempt", "validatedPhones"]),
    ...mapState("brand", { placeCountry: "country"}),
    ...mapGetters("guest", { guestData: "getSelectedGuest", guestLiteData: "getLiteSelectedGuestData" }),
    dialSelect: () => phoneCountries,
    countryList: () => countryList
  },

  watch: {
    "$i18n.locale": async function() {

      await this.getCustomPhoneText();

    }
  },

  async created() {
    this.phoneNumber = this.guestData.telephone?.value;
    this.countryCode = this.guestData.telephone?.countryCode || this.placeCountry;
    this.dialCode = this.guestData.telephone?.dialCode;
    await this.getCustomPhoneText()
    this.stopLoading();
    
    if (
      this.checkAttempExceeded(this.config.max_attempts_telephone, this.attempt)
    ) {
      this.showModalError(
        this.$t("phoneform.phoneNumberMaximumAttemptsExceeded")
      );
    }

    Tracker.recordCustomEvent("guest_phone_start", {
      guest: this.guestLiteData
    });
  },

  methods: {
    ...mapActions("phone", ["SET_DATA", "ADD_ATTEMPT"]),
    ...mapActions("modal", ["VISIBLE", "SET_TYPE", "SET_TITLE"]),

    async getCustomPhoneText() {
      if (this.config.custom_phone_text) {
        try {
          const customTextResponse = await api.getCustomizedText(
          this.brandId,
          this.legalTextType,
          this.$i18n.locale
          );
        this.alertSMS = customTextResponse.data[3].translations[0]?.text || null;
        } catch (error) {
        console.error("Error getting custom phone text", { error });
        this.alertSMS = null;
        }
        if(this.alertSMS === null){
          this.alertSMS = this.$t("phoneform.alertSMS", { brandName: this.$store.state.brand.name })
        }
      }  
    }, 

    async validatePhone() {
      const currentPhoneNumber = this.getValidatePhoneBodyRequest().phone;
      if (this.validatedPhones.includes(currentPhoneNumber)) {
        return await this.finishPhoneProcess("Comments");
      }

      await this.$store.dispatch("loading/LOADING", true);

      if (
        this.phoneNumber === 0 ||
        !this.phoneNumber ||
        this.countryCode === "0"
      ) {
        if (this.config.paymentsActive) {
          return await this.finishPhoneProcess("Payment");
        } else {
          return await this.finishPhoneProcess("Comments");
        }
      }

      await api
        .validatePhone(this.getValidatePhoneBodyRequest())
        .then(async response => {
          await this.SET_DATA(response.data);
          this.trackPhoneFilled(true);
          return this.redirect({ name: "PhoneVerification" });
        })
        .catch(error => {
          console.error("Phone API validation error", { error });
          this.ADD_ATTEMPT();
          let modalErroMessage = this.$t("phoneform.phoneNumberErrorMessage");
          if (
            this.checkAttempExceeded(
              this.config.max_attempts_telephone,
              this.attempt
            )
          ) {
            modalErroMessage = this.$t(
              "phoneform.phoneNumberMaximumAttemptsExceeded"
            );
          }
          this.showModalError(modalErroMessage);
          this.trackPhoneFilled(false);
        });

      this.stopLoading();
    },

    getValidatePhoneBodyRequest() {
      return {
        phone: this.dialCode + this.phoneNumber,
        lang: this.$i18n.locale,
        brand_id: this.brandId,
        reservation_id: this.reservationSelected.res_id,
        name: this.guestData.name,
        last_name: this.guestData.surname,
        room: this.reservationSelected.res_room_number,
        check_in: this.reservationSelected.check_in,
        check_out: this.reservationSelected.check_out,
        keep: this.keep_phone
      };
    },

    async showModalError(message) {
      await this.SET_TYPE("error");
      await this.SET_TITLE("error.header");
      this.modalMessage = message;
      await this.VISIBLE(true);
    },

    async closeModal() {
      if (
        this.checkAttempExceeded(
          this.config.max_attempts_telephone,
          this.attempt
        )
      ) {
        if (this.config.paymentsActive) {
          return await this.finishPhoneProcess("Payments");
        } else {
          return await this.finishPhoneProcess("Comments");
        }
      }
    },

    inputChanged(value) {
      this.countryCode = value.country;
      this.phoneNumber = value.value;
      this.dialCode = value.dialCode;
    },

    trackPhoneFilled(valid) {
      Tracker.recordCustomEvent("guest_phone_filled", {
        valid,
        guest: this.guestLiteData,
        phone: this.dialCode + this.phoneNumber,
        save_phone_database: this.keep_phone
      });
    },

    finishPhoneProcess(route) {
      Tracker.recordCustomEvent("guest_phone_completed", {
        guest: this.guestLiteData,
        phone: null
      });

      return this.redirect({ name: route });
    }    
  }
};
</script>
