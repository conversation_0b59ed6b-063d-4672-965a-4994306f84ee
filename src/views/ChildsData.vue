<template>
  <div class="main-content main-content-white">
    <div class="content h-full flex flex-col">
      <modal name="failedChildData">
        {{ $t("childsdata.error", { index: failedIndex }) }}
      </modal>
      <title-component>{{ $t("childsdata.title") }}</title-component>
      <p class="text-gray-800 mb-4 search-text">
        {{ $t("childsdata.text") }}
      </p>
      <div class="flex-1">
        <tabs
          v-if="tabNames.length > 1"
          :itemsNames="tabNames"
          @tabToggle="onTabClicked"
        />
        <div
          v-for="(inputs, keyGroup) in formInputs"
          :key="keyGroup"
        >
          <div
            v-if="currentGroup === keyGroup"
            v-for="(input, index) in inputs"
            :key="index"
            :class="{ 'md:flex flex-wrap': input.name === 'address' }"
          >
            <form-input
              v-if="input.name === 'address'"
              :ref="'street_number'"
              :key="`street_number_${keyGroup}_${index}`"
              class="mb-6 md:w-2/5 md:pr-4"
              :name="$t(`validatedata.street_number`)"
              :optional="true"
              :type="'text'"
              :inputName="'street_number'"
              :minLength="'1'"
              :maxLength="'20'"
              :value="streetNumbers[keyGroup] || ''"
              @inputChanged="setStreetNumber($event, keyGroup)"
              :browserAutocomplete="false"
              :autocompleteData="{ name: 'street_number', autocomplete: 'off' }"
              :disabled="isChildValidated(keyGroup)"
            />

            <form-input
              @inputChanged="handleInputChanged($event, input)"
              @autocompleteSelect="autocompleteSelect($event, input)"
              :ref="input.name"
              :key="input.index"
              :active="input.active == 'true'"
              :optional="input.required === 'false'"
              :name="$t(`childform.${input.name}InputTitle`)"
              :type="input.type"
              :inputName="`child-${input.name}-input`"
              :check-on-start="Boolean(input.value)"
              :countryCode="input.countryCode"
              :value="input.value"
              :options="getInputOptions(input)"
              :minLength="input.minLength"
              :maxLength="input.maxLength"
              :disabled="input.disabled"
              disableFutureDates
              class="mb-6"
              :class="{
                'md:w-3/5': input.name === 'address',
              }"
              placeholder="..."
            />
          </div>
        </div>
      </div>
      <btn
        @click.native="submit()" 
        :disabled="!submitAvailable"
        data-test="submitButton"
      >
        {{ $t("validatedata.button") }}
      </btn>
    </div>
  </div>
</template>
<script>
import modal from "@/components/shared/modal.component";
import tabs from "@/components/tabs/tabs.vue";
import startLoading from "../mixins/startLoading";
import stopLoading from "../mixins/stopLoading";
import { mapActions, mapGetters, mapState } from "vuex";
import titleComponent from "@/components/shared/title.component";
import formInput from "@/components/search/formInput.component";
import btn from "@/components/shared/button.component";
import { cloneDeep, debounce } from "lodash";
import browserHistoryManagement from "../mixins/browserHistoryManagement";
import redirect from "@/mixins/redirect";
import childsManagement from "@/mixins/childsManagement";
import { countries } from "@/utils/countries";
import guestConfig from "@/mixins/guestConfig";
import moment from "moment";
import { Geo } from "@aws-amplify/geo";

export default {
  name: "ChildsData",
  components: { titleComponent, formInput, btn, tabs, modal },
  data() {
    return {
      currentGroup: 0,
      tabNames: [],
      formInputs: [],
      failedIndex: null,
      nationalitySuggestions: countries,
      streetNumbers: {}, // Store street numbers for each child (indexed by keyGroup)
      addressData: {}, // Store full address data for each child (indexed by keyGroup)
      selectedCountries: {}, // Store selected country for each child (indexed by keyGroup)
      nationalityValues: {}, // Store nationality values for each child (indexed by keyGroup)
      badLocationCountries: ["JPN", "CHN"] // Countries with poor location data
    };
  },
  mixins: [startLoading, stopLoading, browserHistoryManagement, redirect, childsManagement, guestConfig],
  computed: {
    ...mapState("brand", ["brand", "config"]),
    ...mapGetters("guest", ["getChildGuests", "getHolderGuest"]),
    ...mapState("reservations", ["reservationSelected"]),
    submitAvailable() {
      return this.isFormDataCompleted(this.formInputs)
    },
    // Helper to check if a child is validated
    isChildValidated() {
      return (childIndex) => {
        return this.getChildGuests[childIndex]?.validated || false;
      };
    },
  },

  beforeCreate() {
    this.debouncedManageAddressInfo = debounce(async (input, searchValue, childIndex) => {
      try {
        if (!this.config?.disable_address_autocomplete &&
            searchValue.length > 5) {

          let suggestionFilters = {};
          const selectedCountry = this.selectedCountries[childIndex];

          // Add country filter if nationality/country is selected
          if (selectedCountry) {
            suggestionFilters = {
              ...suggestionFilters,
              countries: [selectedCountry],
            };
          }

          // Skip search for countries with poor location data
          if (selectedCountry && this.badLocationCountries.includes(selectedCountry)) {
            input.options = [];
            return;
          }

          const suggestions = await Geo.searchForSuggestions(searchValue, suggestionFilters);

          if (input.value === searchValue) {
            const newOptions = suggestions.map((option) => ({
              name: option.text,
              value: option.text
            }));

            input.options = newOptions;

            this.$nextTick(() => {
              const refList = this.$refs[input.name];
              const componentRef = Array.isArray(refList) ? refList[childIndex] : refList;
              if (componentRef?.$refs?.component) {
                componentRef.$refs.component.results = newOptions;
                componentRef.$refs.component.isOpen = true;
              }
            });
          }
        }
      } catch (error) {
        console.error('Error fetching address suggestions:', error);
        input.options = [];
      }
    }, 300);
  },

  async created() {
    this.getChildGuests.forEach((childData, index) => {
      this.tabNames.push(this.$t("childsdata.minor", { index: index + 1 }));

      const childInputs = cloneDeep(this.config.identification.child_form[0]);
      const childInputsFilled = this.fillFormWithChildData(childInputs, childData);

      // Additionally populate address fields for validated guests (mixin doesn't handle these)
      if (childData.validated) {
        this.populateAddressFields(childInputsFilled, childData);
      }

      // Initialize street number for this child
      this.streetNumbers[index] = childData?.address?.street_number || null;

      // Initialize nationality and country data for this child
      this.nationalityValues[index] = childData?.nationality || null;

      // Map nationality to country code if nationality exists
      let selectedCountry = null;
      if (childData?.nationality) {
        const countryMatch = this.nationalitySuggestions.find(country =>
          country.name.toLowerCase() === childData.nationality.toLowerCase()
        );
        selectedCountry = countryMatch?.value || null;
      }
      this.selectedCountries[index] = selectedCountry || childData?.address?.country || null;

      // Initialize address data for this child
      this.addressData[index] = {
        street: childData?.address?.street || "",
        street_number: childData?.address?.street_number || "",
        city: childData?.address?.city || "",
        postal_code: childData?.address?.postal_code || "",
        province: childData?.address?.province || "",
        region: childData?.address?.region || "",
        subregion: childData?.address?.subregion || "",
        country: childData?.address?.country || "",
        CCAA: childData?.address?.CCAA || "",
      };

      // Prefill inputs with holder data if available and input is empty (BEFORE changing to autocomplete)
      // Skip prefill for validated guests since their inputs are disabled
      if (!childData.validated) {
        this.prefillWithHolderData(childInputsFilled, index);
      }

      // Set address inputs as autocomplete type (AFTER prefilling)
      childInputsFilled.forEach(input => {
        if (input.name === 'address') {
          input.type = 'autocomplete';
          input.options = [];
        }
      });

      this.formInputs.push(childInputsFilled);

    });

    if (this.isFormDataCompleted(this.formInputs)) {
      return this.redirect({ name: "Documents" });
    }

    this.stopLoading();
  },

  mounted() {
    // Ensure address fields are properly updated in the UI after component is mounted
    // Skip for validated guests since their inputs are disabled
    this.$nextTick(() => {
      this.formInputs.forEach((formInputs, childIndex) => {
        const childData = this.getChildGuests[childIndex];
        if (!childData?.validated) {
          const addressInput = formInputs.find(input => input.name === "address");
          if (addressInput && addressInput.value && !addressInput.disabled) {
            this.updateInputValue('address', addressInput.value, childIndex);
          }
        }
      });
    });
  },

  methods: {
    ...mapActions("modal", ["VISIBLE", "SET_NAME"]),

    populateAddressFields(inputs, childData) {
      console.log("HOLI!", childData);
      // Populate existing address data for validated guests
      inputs.forEach(input => {
        console.log("Input", input.name);
        switch (input.name) {
          case "address":
            if (childData?.address?.street) {
              input.value = childData.address.street;
            }
            break;
          case "postal_code":
            if (childData?.address?.postal_code) {
              input.value = childData.address.postal_code;
            }
            break;
          case "municipality":
            if (childData?.address?.city) {
              input.value = childData.address.city;
            }
            break;
          case "email":
            if (childData?.email) {
              input.value = childData.email;
            }
            break;
          case "telephone":
            console.log("child data telephone code", childData?.telephone );

            if (childData?.telephone) {

              if (typeof childData.telephone === 'object') {
                input.value = childData.telephone.value;
                input.countryCode = childData.telephone.countryCode;
                console.log("Country code", input.countryCode );
              } else {
                input.value = childData.telephone;
                console.log("Country code", input.value );

              }
            }
            break;
        }
      });
    },

    prefillWithHolderData(inputs, childIndex) {
      const guestHolder = this.getHolderGuest;
      if (!guestHolder) return;

      inputs.forEach(input => {
        // Only prefill if input is empty, has fill_from_holder flag, and is not disabled
        if (!input.value && input.fill_from_holder === 'true' && !input.disabled) {
          switch (input.name) {
            case "postal_code":
              if (guestHolder?.address?.postal_code) {
                input.value = guestHolder.address.postal_code;
              }
              break;
            case "address":
              if (guestHolder?.address?.street) {
                const streetValue = typeof guestHolder.address.street === 'object'
                  ? guestHolder.address.street.street
                  : guestHolder.address.street;
                input.value = streetValue;

                // Also update the UI component after Vue has rendered
                this.$nextTick(() => {
                  this.updateInputValue('address', streetValue, childIndex);
                });
              }
              // Also prefill street number if available and not already set
              if (guestHolder?.address?.street_number && !this.streetNumbers[childIndex]) {
                this.streetNumbers[childIndex] = guestHolder.address.street_number;
              }
              break;
            case "municipality":
              if (guestHolder?.address?.city) {
                input.value = guestHolder.address.city;
              }
              break;
            case "email":
              if (guestHolder?.email) {
                input.value = guestHolder.email;
              }
              break;
            case "telephone":
              if (guestHolder?.telephone) {
                if (typeof guestHolder.telephone === 'object') {
                  input.value = guestHolder.telephone.value;
                  input.countryCode = guestHolder.telephone.countryCode;
                  console.log("Country code", input.countryCode );

                } else {
                  input.value = guestHolder.telephone;
                  console.log("Country code val8eeee", input.value );

                }
              }
              break;
            case "nationality":
              if (guestHolder?.nationality) {
                input.value = guestHolder.nationality;
                // Also update nationality and country tracking for this child
                this.nationalityValues[childIndex] = guestHolder.nationality;
                const countryMatch = this.nationalitySuggestions.find(country =>
                  country.name.toLowerCase() === guestHolder.nationality.toLowerCase()
                );
                if (countryMatch) {
                  this.selectedCountries[childIndex] = countryMatch.value;
                }
              }
              break;
          }
        }
      });
    },

    setStreetNumber(event, childIndex) {
      this.streetNumbers[childIndex] = event.value;
    },

    updateInputValue(inputName, value, childIndex) {
      this.$nextTick(() => {
        const refList = this.$refs[inputName];
        const componentRef = Array.isArray(refList) ? refList[childIndex] : refList;
        if (componentRef?.$refs?.component) {
          componentRef.$refs.component.inputValue = value;
          if (componentRef.$refs.component.setResult) {
            componentRef.$refs.component.setResult({
              name: value,
              value: value
            });
          }
        }
      });
    },

    updateRelatedAddressInputs(location, childIndex) {
      const formInputs = this.formInputs[childIndex];
      if (!formInputs) return;

      formInputs.forEach((input) => {
        switch (input.name) {
          case "postal_code":
            if (location.postalCode) {
              input.value = location.postalCode;
            }
            break;
          case "municipality":
            if (location.municipality) {
              input.value = location.municipality;
            }
            break;
          case "region":
            if (location.region) {
              input.value = location.region;
            }
            break;
          case "subregion":
            if (location.subRegion) {
              input.value = location.subRegion;
            }
            break;
          case "CCAA":
            if (location.country === "ESP" && location.region) {
              input.value = location.region;
            }
            break;
          case "province":
            if (location.country !== "ESP" && location.region) {
              input.value = location.region;
            }
            break;
        }
      });
    },

    getInputOptions(input) {
      if (input.name === "kinship") {
        return input.options?.map(option => {
          return {
            name: this.$t(`childform.kinshipOptions.${option}`),
            value: option
          };
        });
      }
      return input.options;
    },
    async handleInputChanged(event, input) {
      this.$set(input, 'value', event.value);
      this.$set(input, 'error', event.error);

      // Find which child this input belongs to
      const childIndex = this.formInputs.findIndex(formInput =>
        formInput.some(formInputItem => formInputItem === input)
      );

      if (input.name === "nationality") {
        input.options = this.nationalitySuggestions.filter(country =>
          country.name.toLowerCase().includes(event.value.toLowerCase())
        );

        // Update nationality and selected country for this child
        if (childIndex !== -1) {
          this.nationalityValues[childIndex] = event.value;
          // Update selected country when nationality changes
          const countryMatch = this.nationalitySuggestions.find(country =>
            country.name.toLowerCase() === event.value.toLowerCase()
          );
          if (countryMatch) {
            this.selectedCountries[childIndex] = countryMatch.value;

            // Clear address options to force new search with country filter
            const addressInput = this.formInputs[childIndex]?.find(i => i.name === "address");
            if (addressInput) {
              addressInput.options = [];
            }

            // Force Vue reactivity update
            this.$forceUpdate();
          }
        }
      } else if (input.name === "address") {
        // Update address data for this child
        if (childIndex !== -1) {
          this.addressData[childIndex].street = event.value;
        }

        // Handle address autocomplete
        if (typeof event.value === "string" && event.value.length > 5) {
          this.debouncedManageAddressInfo(input, event.value, childIndex);
        } else {
          input.options = [];
          const refList = this.$refs[input.name];
          const componentRef = Array.isArray(refList) ? refList[childIndex] : refList;
          if (componentRef?.$refs?.component) {
            componentRef.$refs.component.results = [];
            componentRef.$refs.component.isOpen = false;
          }
        }
      }
    },
    async autocompleteSelect(event, input) {
      this.$set(input, 'value', event.value);
      this.$set(input, 'error', event.error);

      // Find which child this input belongs to
      const childIndex = this.formInputs.findIndex(formInput =>
        formInput.some(formInputItem => formInputItem === input)
      );

      if (input.name === "nationality" && childIndex !== -1) {
        this.nationalityValues[childIndex] = event.value;
        input.value = event.value;

        // Update selected country when nationality is selected
        const countryMatch = this.nationalitySuggestions.find(country =>
          country.name.toLowerCase() === event.value.toLowerCase()
        );
        if (countryMatch) {
          this.selectedCountries[childIndex] = countryMatch.value;

          // Clear address options to force new search with country filter
          const addressInput = this.formInputs[childIndex]?.find(i => i.name === "address");
          if (addressInput) {
            addressInput.options = [];
          }

          // Force Vue reactivity update
          this.$forceUpdate();
        }
      } else if (input.name === "address" && childIndex !== -1) {
        const isAwsGeoSelection = event.selectedOption?.placeId ||
                                (event.selectedOption && event.value.includes(','));

        if (isAwsGeoSelection) {
          const fullAddress = event.value;
          const displayAddress = fullAddress.split(',')[0].trim();

          // Get full location data from AWS Geo
          const [location] = await Geo.searchByText(fullAddress);
          if (!location) return;

          // Update only the street in the address input
          input.value = location.street || displayAddress;

          // Update street number if available
          this.streetNumbers[childIndex] = location.addressNumber || "";

          // Update address object for this child
          this.addressData[childIndex] = {
            street: location.street || displayAddress,
            street_number: location.addressNumber || "",
            city: location.municipality || "",
            postal_code: location.postalCode || "",
            province: location.country === "ESP" ? location.region : "",
            region: location.country !== "ESP" ? location.region : "",
            subregion: location.country !== "ESP" ? location.subRegion : "",
            country: location.country || "",
            CCAA: location.country === "ESP" ? location.region : "",
          };

          // Update related fields using the same approach as ChildForm.vue
          const formInputs = this.formInputs[childIndex];

          // Update address field using updateInputValue
          this.updateInputValue('address', location.street || displayAddress, childIndex);

          if (location.postalCode) {
            const postalCodeInput = formInputs.find(i => i.name === "postal_code");
            if (postalCodeInput) {
              this.handleInputChanged({ value: location.postalCode, error: false }, postalCodeInput);
              this.updateInputValue('postal_code', location.postalCode, childIndex);
            }
          }

          if (location.municipality) {
            const municipalityInput = formInputs.find(i => i.name === "municipality");
            if (municipalityInput) {
              this.handleInputChanged({ value: location.municipality, error: false }, municipalityInput);
              this.updateInputValue('municipality', location.municipality, childIndex);
            }
          }

          if (location.country === "ESP" && location.region) {
            const ccaaInput = formInputs.find(i => i.name === "CCAA");
            if (ccaaInput) {
              this.handleInputChanged({ value: location.region, error: false }, ccaaInput);
              this.updateInputValue('CCAA', location.region, childIndex);
            }
          }

         
        }
      }
    },

    isFormDataCompleted(formData) {
      return formData.every(group => 
        group.filter(input => (input.active === "true" || input.active === true) && !input.disabled).every(input => input.value && !input.error)
      );
    },

    onTabClicked(tab) {
      this.currentGroup = tab;
    },
    
    async submit() {
      const failedChildIndex = await this.validateChilds();
      
      if (failedChildIndex !== -1) {
        this.failedIndex = failedChildIndex + 1;
        await this.SET_NAME("failedChildData");
        await this.VISIBLE(true);
        return;
      }

      this.getChildGuests.forEach((childData, index) => {
        const formInputs = this.formInputs[index];

        // Basic information
        childData.name = formInputs.find(input => input.name === "name")?.value;
        childData.surname = formInputs.find(input => input.name === "surname")?.value;
        childData.full_name = [childData?.name, childData?.surname, childData?.second_surname].filter((item) => item).join(" ") || null;
        childData.birthday_date = formInputs.find(input => input.name === "birthday")?.value;
        childData.nationality = formInputs.find(input => input.name === "nationality")?.value;
        childData.kinship = formInputs.find(input => input.name === "kinship")?.value;
        childData.mustBeSynchronized = !formInputs.find(input => input.name === "name")?.disabled;

        // Contact information
        const emailInput = formInputs.find(input => input.name === "email");
        if (emailInput?.value) {
          childData.email = emailInput.value;
        }

        const telephoneInput = formInputs.find(input => input.name === "telephone");
        if (telephoneInput?.value) {
          if (typeof telephoneInput.value === 'object') {
            childData.telephone = telephoneInput.value;
          } else {
            childData.telephone = {
              value: telephoneInput.value,
              dialCode: telephoneInput.dialCode || "+1",
              countryCode: telephoneInput.countryCode || ""
            };
          }
        }

        // Address information - use addressData if available, otherwise fall back to form inputs
        const addressInput = formInputs.find(input => input.name === "address");
        const postalCodeInput = formInputs.find(input => input.name === "postal_code");
        const municipalityInput = formInputs.find(input => input.name === "municipality");
        const ccaaInput = formInputs.find(input => input.name === "CCAA");
        const provinceInput = formInputs.find(input => input.name === "province");
        const regionInput = formInputs.find(input => input.name === "region");
        const subregionInput = formInputs.find(input => input.name === "subregion");
        const streetNumber = this.streetNumbers[index];
        const addressData = this.addressData[index];

        if (addressInput?.value || postalCodeInput?.value || municipalityInput?.value || streetNumber || addressData) {
          if (!childData.address) {
            childData.address = {};
          }

          // Use addressData if available (from GEO selection), otherwise use form inputs
          childData.address.street = addressData?.street || addressInput?.value || "";
          childData.address.street_number = addressData?.street_number || streetNumber || "";
          childData.address.postal_code = addressData?.postal_code || postalCodeInput?.value || "";
          childData.address.city = addressData?.city || municipalityInput?.value || "";
          childData.address.CCAA = addressData?.CCAA || ccaaInput?.value || "";
          childData.address.province = addressData?.province || provinceInput?.value || "";
          childData.address.region = addressData?.region || regionInput?.value || "";
          childData.address.subregion = addressData?.subregion || subregionInput?.value || "";
          childData.address.country = addressData?.country || "";
        }
      });

      return this.redirect({ name: "Documents" });
    },

    async validateChilds() {
      const maxAgeToNotRequireID =
				this.config.child_required_identity_documents_age;
			const checkInDate = this.reservationSelected.check_in;
			const ADULT_AGE = this.getAdultAge();
      
      return this.formInputs.findIndex((inputs) => {
        const birthdayInput = inputs.find(input => input.name === "birthday");
        const childRequiresDocument = this.childRequiresDocument(birthdayInput.value, checkInDate, maxAgeToNotRequireID);
        const childAge = moment(checkInDate).diff(moment(birthdayInput.value), "years");


        return (childRequiresDocument || childAge >= ADULT_AGE) && !birthdayInput.disabled;
      });
    }
  }
};
</script>
