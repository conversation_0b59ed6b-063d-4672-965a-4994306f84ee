export function calculateStep(
	stepName,
	mode,
	{
		signedDocuments = null,
		telephone = null,
		paymentsActive = null,
		comments = null,
		scanOnReception = null,
	},
) {
	let step;
	// Determine step based on mode
	if (mode === "autocheckin") {
		step = 6;
	} else if (mode === "reception") {
		// If mode is reception, determine step based on stepName. Order is 1) Brand selector, 2) Reservation, 3) Privacy policy, 4) Scan identity documents (optional), 5) Identity verification, 6) Documents (optional), 7) Confirmation
		if (
			stepName === "totalSteps" ||
			stepName === "confirmation" ||
			stepName === "documents"
		) {
			step = 5;
		} else if (stepName === "identityVerification") {
			step = 4;
		} else {
			console.error(`Invalid stepName '${stepName}' for reception mode`);
			return null;
		}
	} else {
		console.error(`Invalid mode '${mode}'`);
		return null;
	}

	// Increment step based on additional configurations
	if (signedDocuments) step += 1;
	if (telephone) step += 1;
	if (paymentsActive) step += 1;
	if (comments) step += 1;
	if (scanOnReception) step += 1;

	return step;
}
