import Index from "@/views/Index.vue";
import AppLayout from "@/components/layouts/AppLayout";
import ErrorPage from "@/views/Error";
import store from "@/store/index";
import { calculateStep } from "./helpers";

export const autocheckinRoutes = [
	{
		path: "/:brandId(\\d+)",
		component: AppLayout,
		meta: {
			totalSteps: () =>
				calculateStep("totalSteps", "autocheckin", {
					signedDocuments: store.state.brand.config.signed_documents,
					telephone: store.state.brand.config.telephone,
					paymentsActive: store.state.brand.config.paymentsActive,
					comments: store.state.brand.config.comments,
				}),
		},
		children: [
			{
				path: "",
				name: "Index",
				component: Index,
			},
			{
				path: "brand-selector",
				name: "BrandSelector",
				component: () =>
					import(
						/* webpackChunkName: "brandSelector" */ "../../views/BrandSelector.vue"
					),
				meta: {
					step: () => 1,
					stepName: "brandSelector",
				},
			},
			{
				path: "privacy",
				name: "Privacy",
				component: () =>
					import(/* webpackChunkName: "privacy" */ "../../views/Gdpr.vue"),
				meta: {
					step: () => 2,
					stepName: "privacy",
				},
			},
			{
				path: "search",
				name: "Search",
				component: () =>
					import(/* webpackChunkName: "search" */ "../../views/Search.vue"),
				meta: {
					step: () => 3,
					stepName: "reservation",
				},
			},
			{
				path: "reservations",
				name: "Reservations",
				component: () =>
					import(
						/* webpackChunkName: "reservations" */ "../../views/Reservations.vue"
					),
				meta: {
					step: () => 3,
					stepName: "reservation",
					allowBack: true,
					backRoute: "Search",
				},
			},
			{
				path: "status",
				name: "Status",
				component: () =>
					import(/* webpackChunkName: "status" */ "../../views/Status.vue"),
				meta: {
					step: () => 3,
					stepName: "reservation",
					allowBack: true,
					backRoute: "Reservations",
				},
			},
			{
				path: "child-form",
				name: "ChildForm",
				component: () =>
					import(
						/* webpackChunkName: "childform" */ "../../views/ChildForm.vue"
					),
				meta: {
					step: () => 5,
					stepName: "identityVerification",
					allowBack: true,
					backRoute: "Status",
				},
			},
			{
				path: "scan",
				name: "Scan",
				component: () =>
					import(/* webpackChunkName: "scan" */ "../../views/Scan.vue"),
				meta: {
					step: () => 4,
					stepName: "scan",
				},
			},
			{
				path: "advanced-scan",
				name: "AdvancedScan",
				component: () =>
					import(
						/* webpackChunkName: "advancedScan" */ "../../views/AdvancedScan.vue"
					),
				meta: {
					step: () => 4,
					stepName: "scan",
				},
			},
			{
				path: "sensible-data",
				name: "SensibleData",
				component: () =>
					import(
						/* webpackChunkName: "advancedScan" */ "../../views/SensibleDataForm.vue"
					),
				meta: {
					step: () => 4,
					stepName: "scan",
				},
			},
			{
				path: "validate-data",
				name: "ValidateData",
				component: () =>
					import(
						/* webpackChunkName: "validateData" */ "../../views/ValidateData.vue"
					),
				meta: {
					step: () => 5,
					stepName: "identityVerification",
				},
			},
			{
				path: "childs-data",
				name: "ChildsData",
				component: () =>
					import(
						/* webpackChunkName: "childsData" */ "../../views/ChildsData.vue"
					),
				meta: {
					step: () => 5,
					stepName: "identityVerification",
					active: () => {
						return (
							store.state.brand.config.child_data_with_holder &&
							store.getters["guest/getSelectedGuest"]?.holder &&
							store.getters["guest/getChildGuests"]?.length
						);
					},
					redirect: "SelectRoom",
				},
			},
			{
				path: "select-room",
				name: "SelectRoom",
				component: () =>
					import(
						/* webpackChunkName: "salectRoom" */ "../../views/SelectRoom.vue"
					),
				meta: {
					active: () => {
						return store.state.brand.config.room_type_selection;
					},
					redirect: "Documents",
				},
			},
			{
				path: "documents",
				name: "Documents",
				component: () =>
					import(
						/* webpackChunkName: "documents" */ "../../views/Documents.vue"
					),
				meta: {
					step: () => 6,
					stepName: "documents",
					active: () => {
						return store.state.brand.config.signed_documents;
					},
					redirect: "PhoneForm",
				},
			},
			{
				path: "signature",
				name: "Signature",
				component: () =>
					import(
						/* webpackChunkName: "signature" */ "../../views/Signature.vue"
					),
				meta: {
					step: () => 6,
					stepName: "documents",
					allowBack: true,
					backRoute: "Documents",
					active: () => {
						return store.state.brand.config.signed_documents;
					},
					redirect: "PhoneForm",
				},
			},
			{
				path: "send-documents",
				name: "SendDocuments",
				component: () =>
					import(
						/* webpackChunkName: "sendDocuments" */ "../../views/SendDocuments.vue"
					),
				meta: {
					step: () => 6,
					stepName: "documents",
					active: () => {
						return (
							store.state.brand.config.signed_documents &&
							!store.state.brand.config.disable_send_documents_page
						);
					},
					redirect: "PhoneForm",
				},
			},
			{
				path: "phone-form",
				name: "PhoneForm",
				component: () =>
					import(
						/* webpackChunkName: "phoneForm" */ "../../views/PhoneForm.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
						});
					},
					stepName: "phone",
					allowBack: true,
					backRoute: "PhoneForm",
					active: () => {
						return store.state.brand.config.telephone;
					},
					redirect: "Payment",
				},
			},
			{
				path: "phone-verification",
				name: "PhoneVerification",
				component: () =>
					import(
						/* webpackChunkName: "phoneVerification" */ "../../views/PhoneVerification.vue"
					),

				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
						});
					},
					stepName: "phone",
					active: () => {
						return store.state.brand.config.telephone;
					},
					redirect: "Payment",
				},
			},
			{
				path: "payment",
				name: "Payment",
				component: () =>
					import(/* webpackChunkName: "payment" */ "../../views/Payment.vue"),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
						});
					},
					stepName: "payment",
					active: () => {
						// TODO: Delete "false" once the payments view is redeployed and ready
						return store.state.brand.config.paymentsActive && false;
					},
					redirect: "Comments",
				},
			},
			{
				path: "payment-proform",
				name: "PaymentProform",
				component: () =>
					import(
						/* webpackChunkName: "payment" */ "../../views/Payment-proform.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
						});
					},
					stepName: "payment",
					allowBack: true,
					backRoute: "Payment",
					active: () => {
						return store.state.brand.config.paymentsActive;
					},
					redirect: "Comments",
				},
			},
			{
				path: "payment-form",
				name: "PaymentForm",
				component: () =>
					import(
						/* webpackChunkName: "payment" */ "../../views/Payment-form.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
						});
					},
					stepName: "payment",
					active: () => {
						return store.state.brand.config.paymentsActive;
					},
					redirect: "Comments",
				},
			},
			{
				path: "payment-result",
				name: "PaymentResult",
				component: () =>
					import(
						/* webpackChunkName: "payment" */ "../../views/Payment-result.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
						});
					},
					stepName: "payment",
					active: () => {
						return store.state.brand.config.paymentsActive;
					},
					redirect: "Comments",
				},
			},
			{
				path: "comments",
				name: "Comments",
				component: () =>
					import(/* webpackChunkName: "comments" */ "../../views/Comments.vue"),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
							paymentsActive: store.state.brand.config.paymentsActive,
						});
					},
					stepName: "extraInformation",
					active: () => {
						return (
							store.state.brand.config.comments ||
							store.state.brand.config.arrival_time
						);
					},
					redirect: "Confirmation",
				},
			},
			{
				path: "confirmation",
				name: "Confirmation",
				component: () =>
					import(
						/* webpackChunkName: "confirmation" */ "../../views/Confirmation.vue"
					),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
							paymentsActive: store.state.brand.config.paymentsActive,
							comments: store.state.brand.config.comments,
						});
					},
					stepName: "confirmation",
				},
			},
			{
				path: "share",
				name: "Share",
				component: () =>
					import(/* webpackChunkName: "share" */ "../../views/Share.vue"),
				meta: {
					step: function () {
						return calculateStep(this.stepName, "autocheckin", {
							signedDocuments: store.state.brand.config.signed_documents,
							telephone: store.state.brand.config.telephone,
							paymentsActive: store.state.brand.config.paymentsActive,
							comments: store.state.brand.config.comments,
						});
					},
					stepName: "confirmation",
				},
			},
			{
				path: "error",
				name: "Error",
				component: ErrorPage,
			},
		],
	},
	{
		path: "*",
		name: "error404",
		component: ErrorPage,
	},
];
