export default {
  minLengthError: "Der Wert muss mehr als {number} <PERSON>eichen haben.",
  maxLengthError: "Der Wert muss weniger als {number} <PERSON>eichen haben.",
  emptyValueError: "Der Wert kann nicht leer bleiben.",
  dateFormatError: "Der Wert hat nicht das erwartete Format",
  defaultOption: "Wählen Sie eine Option:",
  optional: "Optional",
  invalidDocumentNumber: "Die Nummer des Ausweisdokuments ist ungültig.",
  invalidDocumentSupportNumber: "Muss 3 Buchstaben gefolgt von 6 Ziffern enthalten",
  postalCodeError: "Die Postleitzahl ist ungültig",
  date: {
    greaterThanTodayError:
      "Das {name} darf nicht nach dem heutigen Datum liegen",
    lowerThanCheckInDateError:
      "Das {name} darf nicht kleiner als das Check-in Datum sein",
    incorrectPaxError:
      "Das Geburtsdatum stimmt nicht mit dem eines {paxType} überein",
    tooOld: "Geburtsdatum nicht korrekt"
  },
    documentNumberDuplicated: "Dieses Dokument wurde bereits in einem anderen Gast dieses Checkins verwendet."
};
