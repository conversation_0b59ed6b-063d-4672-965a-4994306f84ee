export default {
  header: "<PERSON>erdaten",
  title: "Geben Sie die Daten des Kindes ein",
  nameInputTitle: "<PERSON><PERSON><PERSON>",
  surnameInputTitle: "<PERSON>rst<PERSON> Nachname",
  birthdayInputTitle: "Geburtsdatum",
  nationalityInputTitle: "Staatsangehörigkeit",
  kinshipInputTitle: "Verwandschaftsgrad in Bezug auf den Inhaber der Buchung",
  addressInputTitle: "Adresse",
  municipalityInputTitle: "Gemeinde",
  emailInputTitle: "E-Mail",
  telephoneInputTitle: "Telefon",
  info:
    "Wir sind daran interessiert, diese Informationen über die Kinder zu kennen, die sich in unseren Einrichtungen befinden, um den besten Service zu bieten und auf alle Eventualitäten vorbereitet zu sein.",
  button: "Fortfahren",
  modalMessage:
    "Jede Person, die älter als {maxAgeToNotRequireID} ist, hat ihr Personaldokument zur Registrierung im {hotelName} vorzuweisen. Wir haben das Alter des Minderjährigen auf {age} Jahre berechnet. Ist das richtig?",
  modalErrorMessage:
    "Das Alter des Gastes ist älter als das erforderliche Alter, um als {paxType} zu gelten. Bitte versuche es erneut.",
  goBack: "Zurück zum vorhergehenden Schritt",
  kinshipOptions: {
    son: "Sohn",
    nephew: "Neffe",
    grandson: "Enkelsohn",
    brother: "Bruder",
    cousin: "Cousin",
    other: "Andere"
  }
};
