export default {
  header: "Ergebnis der Zahlung",
  titleOk: "Die Zahlung wurde ordnungsgemäß vorgenommen.",
  titleKo: "Die Zahlung konnte nicht durchgeführt werden.",
  textOkFirst: "Wir bestätigen den ordnungsgemäßen Zahlungseingang von ",
  textOkSecond:
    "Nachfolgend finden Sie die Kennnummer des Zahlungsvorgangs. Denken Si<PERSON> bitte daran, dass Si<PERSON> an der Rezeption eine Rechnung anfordern können.",
  textKo:
    "Beim Zahlungsvorgang kam es zu einem Problem. Versuchen Sie es bitte noch einmal oder wenden Sie sich nach Abschluss des Check-in-Vorgangs an die Rezeption. Unsere Mitarbeiter sind Ihnen gern bei der Zahlung behilflich.",
  continue: "Weiter",
  continueWithoutPay: "An der Rezeption zahlen",
  tryAgain: "Noch einmal versuchen",
  modal_info_text:
    "Nach Abschluss des Check-in-Vorgangs wenden Sie sich bitte um Hilfe beim Bezahlvorgang an unsere Mitarbeiter. Wir fahren mit dem Vorgang fort.",
  modal_info_title: "Keine Sorge",
  service: "Service",
  operation: "Operation",
  receiptTitle: "Zahlungsbestätigung",
  receiptDate: "Datum:",
  receiptAuthorization: "Autorisierung:",
  receiptOrder: "Order:",
  receiptQuantity: "Anzahl:",
  receiptMethod: "Zahlungsweise:",
  receiptLastDigits: "Letzte Ziffern"
};
