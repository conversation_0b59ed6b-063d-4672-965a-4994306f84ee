export default {
  header: "Buchungen",
  title: "Buchungsdetails",
  MultipleReservationsTitle: "Wir haben {0} Buchungen gefunden.",
  MultipleReservationstext:
    "Wählen Sie die Buchung, für die Sie den Check-in vornehmen wollen, indem Sie diese von links nach rechts ziehen.",
  text:
    "Überprüfen Sie bitte, ob die gefundene Buchung mit Ihrer Buchung übereinstimmt und wählen Sie diese aus.",
  localizer: "Buchungsreferenz",
  show: "Zeigen",
  owner: "Inhaber der Buchung",
  users: "Zur Buchung gehörende Personen",
  room: "<PERSON><PERSON>",
  board: "Verpflegungsart",
  checkin: "Check-in-Datum",
  checkout: "Check-out-Datum",
  button: "Dieses auswählen",
  buttonCompleted: "Buchung vollständig",
  goToDetail: "<PERSON>urück zu Details",
  guestsTitle: "<PERSON><PERSON> dieser Buchung gehörende Personen bestätigt",
  free: "<PERSON><PERSON>",
  guestCompleted: "{completed} von {total} abgeschlossen"
};
