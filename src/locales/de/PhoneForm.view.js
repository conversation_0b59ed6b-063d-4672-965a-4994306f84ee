export default {
	header: "Telefonnummer",
	title: "Bleiben Sie während Ihres Aufenthalts auf dem Laufenden",
	info: "Wir können Ihnen wichtige Informationen mitteilen, z.B. wann Ihr Zimmer bezugsbereit ist oder weitere, für Sie wichtige Informationen während Ihres Aufenthalts.",
	alertSMS:
		"* Bei Einverständnis gibt Ihnen {brandName} per SMS interessante Informationen während Ihres Aufenthalts. Zum Beispiel die Verfügbarkeit des Zimmers, Ausflüge, Aktivitäten oder in dieser Zeit stattfindende Events.",
	checkbox:
		"Sollen wir Ihnen auch nach Ihrem Aufenthalt weiterhin Informationen zu unseren Neuigkeiten schicken?",
	keepPhone:
		"Ich stimme zu, dass Sie meine Telefonnummer in der Datenbank speichern, um das Erlebnis meines Aufenthalts zu verbessern",
	number: "Telefonnummer",
	countrySelectorLabel: "Ortskennzahl",
	phoneNumberLabel: "Telefonnummer*",
	exampleLabel: "Beispiel:",
	phoneNumberFormat: "Ortskennzahl - Telefonnummer",
	phoneNumberErrorMessage:
		"Wir konnten die Telefonnummer nicht verifizieren. Versuchen Sie es bitte noch einmal.",
	phoneCodeErrorTitle: "Der eingegebene Verifizierungscode ist ungültig.",
	phoneCodeErrorMessage:
		"Geben Sie bitte den Verifizierungscode ein, den wir Ihnen per SMS an Ihre Telefonnummer geschickt haben. Geben Sie diesen Code noch einmal ein oder löschen/ändern Sie die Telefonnummer.",
	phoneNumberMaximumAttemptsExceeded:
		"Die maximale Anzahl an Versuchen wurde überschritten. Wir leiten Sie zum nächsten Schritt weiter, um mit dem Vorgang fortzufahren.",
	phoneCodeErrorButton: "Noch einmal versuchen",
	codeTitle: "Bestätigen Sie den Code, den wir Ihnen per SMS geschickt haben.",
	codePhoneLabel: "Geben Sie den Code ein",
	codeNotRecived:
		"Wenn Sie den Code nach einigen Minuten noch nicht erhalten haben,",
	tryAgain:
		"klicken Sie bitte hier, damit wir Ihnen den Code noch einmal schicken.",
	continue: "Fortfahren",
};
