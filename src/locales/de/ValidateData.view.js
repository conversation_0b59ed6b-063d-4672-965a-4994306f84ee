export default {
	header: "Verifizieren Sie Ihre Angaben",
	title:
		"Überprüfen Sie die Richtigkeit Ihrer eingescannten Angaben und bestätigen Si<PERSON> diese.",
	modalTitle: "Ganz wichtig!",
	modalDuplicatesTitle: "Bitte daran denken",
	truthfulData:
		"Wir möchten Si<PERSON> daran <PERSON>, dass die der Einrichtung zur Verfügung gestellten Angaben der Wahrheit entsprechen müssen, da sie von unseren Mitarbeitern überprüft werden.",
	modalDuplicates:
		"Einige Felder wurden geändert. Vergewissern Sie sich der Richtigkeit, bevor Sie fortfahren.",
	name: "<PERSON><PERSON><PERSON>",
	surname: "<PERSON><PERSON><PERSON>",
	second_surname: "<PERSON><PERSON><PERSON>ch<PERSON>",
	birthday_date: "Geburtsdatum",
	nationality: "Staatsangehörigkeit",
	gender: "G<PERSON><PERSON>le<PERSON>",
	address: "Anschrift",
	municipality: "Gemeinde",
	postal_code: "Postleitzahl",
	empty_value: "Füllen Sie Ihr {input} aus",
	document_number: "Nummer des Dokuments",
	document_support_number: "<PERSON>-Num<PERSON>",
	document_type: "Art des Dokuments",
	date_of_issue: "Ausstellungsdatum",
	date_of_expiry: "Ablaufdatum",
	issuing_country: "Ausstellungsland des Dokuments",
	fiscal_code: "Steuergesetzbuch",
	detectedValuesWithMuchDifference:
		"Wir haben Änderungen in den folgenden Feldern festgestellt: ",
	button: "Bestätigen und fortfahren",
	closeModal: "Ok, verstanden",
	inputErrorMessage: "Geben Sie bitte ein gültiges Datum ein.",
	passport: "Pass",
	identity_card: "PA",
	driving_license: "Führerschein",
	residence_permit: "Aufenthaltserlaubnis",
	male: "Mann",
	female: "Frau",
	CCAA: "Region",
	province: "Provinz",
	region: "Region",
	subRegion: "Unterregion",
	residence_country: "Wohnsitzland",
	street_number: "Hausnummer",
	telephone: "Telefonnummer",
	email: "E-Mail",
	placeHolder: "TT-MM-JJJJ",
	placeHolderAmerican: "MM-TT-JJJJ",
	card: {
		title: "Validierte Informationen",
		data: "Daten",
		toggle: "Zeige Alles",
	},
	kinship: "Verwandschaftsgrad in Bezug auf den Inhaber der Buchung",
	son: "Sohn",
  nephew: "Neffe",
  grandson: "Enkelsohn",
  brother: "Bruder",
  cousin: "Cousin",
  other: "Andere"
};
