export default {
  header: "Buchung suchen",
  title: "Wir suchen jetzt Ihre Buchung",
  text: "Füllen Sie alle erforderlichen Felder aus, um Ihre Buchung in unserem System finden zu können. Die nötigen Angaben finden Sie in den Informationen, die Ihnen im Moment der Buchung mitgeteilt wurden. Suche nach:",
  button: "Buchung suchen",
  foundTitle: "Wir haben {count} gefunden",
  booking: "Buchung | Buchungen",
  check_in: "Check-in-Datum",
  check_out: "Check-out-Da<PERSON>",
  first_name: "Vorname",
  reservation_code: "Buchungsreferenz",
  foundText:
    "Geben Sie bitte die folgenden Angaben zum Auffinden Ihrer Buchung ein:",
  reservationNotFound:
    "Wir haben keine Buchung mit den uns mitgeteilten Angaben gefunden. Versuchen Sie es bitte noch einmal!",
  invalidReservation:
  "Ihre Buchung vom {checkIn} bis {checkOut} ist ein Online-Check-in nicht möglich. Wenn Sie glauben, dass es sich um einen Fehler handelt, wenden Sie sich bitte an die Rezeption.",
  integrationError:
    "Leider ist der Online-Check-in-Service momentan nicht verfügbar.<br>Versuchen Sie es bitte später noch einmal oder setzen Sie sich mit unseren Mitarbeitern für weitere Informationen und Hilfe in Verbindung.",
  groupReservationError: "Leider ist der Online-Check-in-Prozess aufgrund Ihrer Gruppenbuchung nicht möglich. Bitte gehen Sie zum Einchecken zur Rezeption.",
  tokenMalformed:
    "Wir konnten den Link der Buchung nicht erkennen. Geben Sie bitte für eine erneute Suche die Angaben noch einmal ein!",
  manyReservationFound:
    "Wir haben <span class='font-bold'>{number}</span> Buchungen mit den uns mitgeteilten Angaben gefunden. Geben Sie zur Anpassung der Suche bitte die folgende Information ein: <span class='font-bold'>{input}</span>.",
  checkinStartingAt:
    "Der Online-Check-in beginnt in {days} Tagen, {hours} Stunden, {mins} Minuten.\nKommen Sie bitte später wieder zurück, um Ihr Check-in-Verfahren abzuschließen.",
  checkinClosedAt:
    "Online checkin closed {days} days and {hours} hours ago. Please go to reception to check-in in person.",
};
