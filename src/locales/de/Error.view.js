export default {
  header: "Ups! <PERSON><PERSON>",
  default: {
    header: "Ups! <PERSON><PERSON>",
    title: "Ups, es kam zu einem internen Problem",
    message:
      "Leider ist der Service Online-Check-in im Moment nicht verfügbar.<br>Versuchen sie es bitte später noch einmal oder für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung."
  },
  maxAttemptsReservationError: {
    title: "Sie haben die maximale Anzahl an Versuchen erreicht",
    message:
      "Die maximale Anzahl an Versuchen wurde überschritten.<br>Für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung.",
    buttons: { default: "Aus dem Vorgang ausloggen" }
  },
  brandError: {
    title: "Ups, es kam zu einem Problem bei der Suche nach der Einrichtung",
    message:
      "Es tut uns leid, aber in dieser Einrichtung ist der Online-Check-in im Moment nicht verfügbar.<br>Versuchen sie es bitte später noch einmal oder für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung.",
    buttons: { default: "Noch einmal versuchen" }
  },
  reservationError: {
    title: "Wir haben keinen ausstehenden CHECK-IN-Vorgang gefunden",
    message:
      "Entweder wurde der CHECK-IN-Vorgang bereits abgeschlossen oder wir finden keine Buchung mit diesen Angaben.<br>Versuchen sie es bitte später noch einmal oder für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung.",
    buttons: { default: "Noch einmal versuchen" }
  },
  ageError: {
    title: "Das eingegebene Alter ist nicht korrekt",
    message:
      "Entsprechend Ihrer Information stimmt das Alter des Minderjährigen nicht mit den Angaben in Ihrer Buchung überein.<br>Versuchen sie es bitte später noch einmal oder für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung.",
    buttons: { default: "Mit einem anderen Gast fortsetzen" }
  },
  scanError: {
    buttons: { default: "Aus dem Vorgang ausloggen" },
    title: "Beim Scannen kam es zu einem Fehler",
    message:
      "Beim Scannen des Dokuments kam es zu einem unerwarteten Fehler.<br>Versuchen sie es bitte später noch einmal oder für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung."
  },
  maxAttemptsScanError: {
    buttons: { default: "Aus dem Vorgang ausloggen" },
    title: "Sie haben die maximale Anzahl an Versuchen erreicht",
    message:
      "Die maximale Anzahl an Versuchen zum Einscannen von Dokumenten wurde durch zu häufiges Einscannen überschritten.<br>Für mehr Information und Hilfe wenden Sie sich bitte an die Mitarbeiter der Einrichtung."
  },
  maxAttemptsScanErrorWithCompleteData: {
    buttons: {
      exitProcess: "Aus dem Vorgang ausloggen",
      completeData: "Angaben von Hand vervollständigen"
    },
    title: "Sie haben die maximale Anzahl an Versuchen erreicht",
    message:
      "Sie haben die Anzahl der möglichen Versuche zum Einscannen der Dokumente überschritten.\nVervollständigen Sie die Angaben zu Ihrem Check-in von Hand oder wenden Sie sich an die Rezeption, um den Check-in dort persönlich vorzunehmen.\nWir möchten Sie daran erinnern, dass die Vorlage eines gültigen Personaldokuments für den Check-in in unserem Haus erforderlich ist.\nSollten Sie das Dokument jetzt nicht hochladen können, vervollständigen Sie bitte die Angaben zu Ihrem Check-in von Hand und bei Ihrer Ankunft bei uns weisen Sie dann Ihre Unterlagen vor. Was möchten Sie tun?"
  },
  error404: {
    title: "Leider konnte die gewünschte Information nicht gefunden werden",
    message: "Sie können die Registerkarte zum Ausloggen schließen. ",
    buttons: { default: "Klicken Sie hier, um zur Startseite zu gelangen" },
    header: "Seite nicht gefunden"
  },
  exitText: "Sie können die Registerkarte zum Ausloggen schließen."
};
