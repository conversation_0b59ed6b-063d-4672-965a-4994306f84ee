import Vue from "vue";
import VueI18n from "vue-i18n";
import es from "./es";
import en from "./en";
import de from "./de";
import pt from "./pt";
import fr from "./fr";
import it from "./it";
import ru from "./ru";

Vue.use(VueI18n);

const messages = {
  es,
  en,
  de,
  pt,
  fr,
  it,
  ru
};

const spainLanguages = ["ca", "eu", "gl"];

// Check user browser language
const userLocale = window.navigator.userLanguage || window.navigator.language;

const getLocale = lang => {
  // If user's device language is catalan, galician or basque, use spanish
  if (spainLanguages.includes(lang)) {
    return "es";
  }
  return Object.keys(messages).includes(lang) ? lang : "en";
};

const locale =
  JSON.parse(localStorage.getItem("hlCheckin"))?.app?.appLanguage ||
  getLocale(userLocale.substring(0, 2));

export const i18n = new VueI18n({
  locale,
  fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE,
  silentTranslationWarn: true,
  messages
});
