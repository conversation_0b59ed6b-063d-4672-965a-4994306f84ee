export default {
  header: "Encontrar a sua reserva",
  title: "Vamos procurar a sua reserva",
  text: "Preencha todos os campos obrigatórios para poder localizar a sua reserva no nosso sistema. Poderá encontrar a informação necessária nos dados que lhe foram fornecidos no momento da reserva. Procurar por:",
  button: "Encontrar a sua reserva",
  foundTitle: "Encontramos {count}",
  booking: "reserva | reservas",
  check_in: "Data de Check-in",
  check_out: "Data de Check-out",
  first_name: "Nome",
  reservation_code: "Referência da reserva",
  foundText:
    "Por favor, insira as seguintes informações para encontrar a sua reserva:",
  reservationNotFound:
    "Não nos foi possível encontrar uma reserva com os detalhes fornecidos. Por favor, tente novamente.",
  invalidReservation:
  "A sua reserva de {checkIn} a {checkOut} não permite o check-in online. Se pensa que se trata de um erro, contacte a receção.",
  integrationError:
    "<PERSON><PERSON>avelm<PERSON>, o serviço de check-in online está atualmente indisponível. <br> Por favor, tente novamente mais tarde ou contacte a nossa equipa para obter mais informações e assistência.",
  groupReservationError: "Infelizmente, o processo de check-in online não está disponível devido à sua reserva de grupo. Por favor, dirija-se à receção para efetuar o check-in.",
  tokenMalformed:
    "Não nos foi possível reconhecer o link de reserva. Por favor, procure-o, inserindo os dados novamente.",
  manyReservationFound:
    "Encontramos <span class='font-bold'>{number}</span> reservas com os detalhes fornecidos. Insira as seguintes informações: <span class='font-bold'>{input}</span> para ajustar a pesquisa.",
  checkinStartingAt:
    "O check-in online começará dentro de {days} dias, {hours} horas, {mins} minutos.\nVolte mais tarde para concluir o processo de check-in.",
  checkinClosedAt:
    "Online checkin closed {days} days and {hours} hours ago. Please go to reception to check-in in person.",
};
