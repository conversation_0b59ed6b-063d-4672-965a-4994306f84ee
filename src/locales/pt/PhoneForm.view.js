export default {
	header: "Número de telefone",
	title: "Mantenha-se informado durante a sua estadia",
	info: "Podemos notificá-lo sobre eventos de interesse, como quando o seu quarto estiver pronto, ou acerca de outros eventos importantes durante sua estadia.",
	alertSMS:
		"* Se aceitar, {brandName} irá informá-lo, via SMS, de qualquer evento que possa ser do seu interesse durante a sua estadia. Por exemplo, disponibilidade de quartos, viagens de um dia, atividades ou eventos que ocorram durante a sua estadia.",
	checkbox:
		"Gostaria que continuássemos a enviar-lhe informações sobre as nossas novidades após a sua estadia?",
	keepPhone:
		"Eu concordo em ter o meu número de telefone guardado no banco de dados para melhorar a experiência da minha estadia.",
	number: "Número de telefone",
	countrySelectorLabel: "Código do país",
	phoneNumberLabel: "Número de telefone *",
	exampleLabel: "Exemplo:",
	phoneNumberFormat: "Código do país – Número de telefone",
	phoneNumberErrorMessage:
		"Não nos foi possível verificar o número de telefone. Por favor, tente novamente.",
	phoneCodeErrorTitle: "O código de verificação que inseriu não está correto",
	phoneCodeErrorMessage:
		"Deve inserir o código de verificação que enviámos para o seu telefone via SMS. Por favor, introduza novamente o código ou elimine/altere o número de telefone.",
	phoneNumberMaximumAttemptsExceeded:
		"Excedeu o número máximo de tentativas. Vamos redirecioná-lo para a página seguinte para continuar com o processo.",
	phoneCodeErrorButton: "Tente novamente",
	codeTitle: "Por favor, confirme o código que lhe enviámos via SMS.",
	codePhoneLabel: "Introduza o código",
	codeNotRecived: "Caso não tenha recebido o código após alguns minutos,",
	tryAgain: "clique aqui para tentar novamente.",
	continue: "Continuar",
};
