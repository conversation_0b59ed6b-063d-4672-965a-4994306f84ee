export default {
  minLengthError: "O valor deve conter no mínimo {number} caracteres",
  maxLengthError: "O valor deve conter no máximo {number} caracteres",
  emptyValueError: "O campo valor não pode estar vazio",
  dateFormatError: "O valor não tem o formato esperado",
  defaultOption: "Selecione uma opção:",
  optional: "Facultativo",
  invalidDocumentNumber: "O número do documento é inválido",
  invalidDocumentSupportNumber: "Deve conter 3 letras seguidas de 6 dígitos",
  postalCodeError: "O código postal é inválido",
  date: {
    greaterThanTodayError: "O {name} não pode ser maior que a data de hoje",
    lowerThanCheckInDateError:
      "O {name} não pode ser anterior à data de check-in",
    incorrectPaxError: "A data de nascimento não corresponde a um(a) {paxType}",
    tooOld: "Data de nascimento incorreta"
  },
  documentNumberDuplicated: "Este documento foi utilizado noutro hóspede deste check-in."
};
