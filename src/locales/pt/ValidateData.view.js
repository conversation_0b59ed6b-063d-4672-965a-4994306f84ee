export default {
	header: "Valide as suas informações",
	title: "Verifique se as informações digitalizadas estão corretas e confirme.",
	modalTitle: "Importante!",
	modalDuplicatesTitle: "Aten<PERSON>",
	truthfulData:
		"Lembramos que os dados fornecidos ao estabelecimento devem ser verdadeiros. Os mesmos serão verificados pela nossa equipa.",
	modalDuplicates:
		"Alguns campos foram alterados. Verifique se estão corretos antes de continuar.",
	name: "<PERSON><PERSON>",
	surname: "<PERSON><PERSON> apel<PERSON>",
	second_surname: "<PERSON><PERSON><PERSON>",
	birthday_date: "Data de nascimento",
	nationality: "Nacionalidade",
	gender: "<PERSON><PERSON>",
	address: "endereço",
	municipality: "Municipalidade",
	postal_code: "Código postal",
	empty_value: "Preencha o campo {input}",
	document_number: "Número do documento",
	document_support_number: "Número de suporte",
	document_type: "Tipo de documento",
	date_of_issue: "Data de emissão",
	date_of_expiry: "Data de validade",
	issuing_country: "País de emissão do documento",
	fiscal_code: "Código fiscal",
	detectedValuesWithMuchDifference:
		"Verificámos que houve alterações nos seguintes campos:",
	button: "Confirmar e continuar",
	closeModal: "OK, entendido.",
	inputErrorMessage: "Por favor, insira uma data válida.",
	passport: "Passaporte",
	identity_card: "Cartão de cidadão",
	driving_license: "Cartão de condução",
	residence_permit: "Autorização de residência",
	male: "Masculino",
	female: "Feminino",
	CCAA: "Região",
	province: "Província",
	region: "estado",
	subregion: "cidade",
	residence_country: "País de residência",
	street_number: "Número da rua",
	telephone: "Número de telefone",
	email: "E-mail",
	placeHolder: "DD-MM-AAAA",
	placeHolderAmerican: "MM-DD-AAAA",
	card: {
		title: "Informações validadas",
		data: "Dados",
		toggle: "Moste tudo",
	},
	kinship: "Relação familiar com o titular da reserva",
	son: "Filho",
	nephew: "Nephew",
	grandson: "Sobrinho",
	brother: "Irmão",
	cousin: "Primo",
	other: "Outro"
};
