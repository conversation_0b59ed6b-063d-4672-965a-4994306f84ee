export default {
  header: "<PERSON><PERSON> da criança",
  title: "Introduza os dados da criança",
  nameInputTitle: "<PERSON><PERSON>",
  surnameInputTitle: "<PERSON>pel<PERSON>",
  birthdayInputTitle: "Data de nascimento",
  nationalityInputTitle: "Nacionalidade",
  kinshipInputTitle: "Relação familiar com o titular da reserva",
  addressInputTitle: "Endereço",
  municipalityInputTitle: "Município",
  emailInputTitle: "Email",
  telephoneInputTitle: "Telefone",
  info:
    "Temos interesse em conhecer esses dados dos nossos hóspedes menores de idade para podermos oferecer os melhores serviços e estarmos preparados para eventuais imprevistos.",
  button: "Continuar",
  modalMessage:
    "Qualquer pessoa com mais de {maxAgeToNotRequireID} deverá apresentar o seu documento de identidade para realizar o check-in no {hotelName}. Calculámos que a criança tem {age}, correto?",
  modalErrorMessage:
    "A idade do hóspede é superior à idade exigida para ser considerado um {paxType}. Tente novamente.",
  goBack: "Ir para a etapa anterior",
  kinshipOptions: {
    son: "Filho",
    nephew: "Nephew",
    grandson: "Sobrinho",
    brother: "Irmão",
    cousin: "Primo",
    other: "Outro"
  },
  postal_codeInputTitle: "Código postal"
};
