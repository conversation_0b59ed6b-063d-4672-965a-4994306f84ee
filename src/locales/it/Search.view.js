export default {
  header: "Trova la tua prenotazione",
  title: "Stiamo per cercare la tua prenotazione",
  text: "Compila tutti i campi richiesti per poter individuare la tua prenotazione nel nostro sistema. Potrai trovare le informazioni necessarie nei dati che ti sono stati forniti al momento della prenotazione. Cerca per:",
  button: "Trova la tua prenotazione",
  foundTitle: "Abbiamo trovato {count}",
  booking: "prenotazione | prenotazioni",
  check_in: "Data di check-in",
  check_out: "Data di check-out",
  first_name: "Nome",
  reservation_code: "Riferimento prenotazione",
  foundText: "Inserisci le seguenti informazioni per trovare la tua prenotazione:",
  reservationNotFound:
    "Non è stato possibile trovare una prenotazione con i dati forniti. Riprova.",
  invalidReservation:
  "La tua prenotazione da {checkIn} a {checkOut} non consente il check-in online. Se ritieni che si tratti di un errore, contatta la reception.",
  integrationError:
    "Purtrop<PERSON>, il servizio di check-in online non è al momento disponibile.<br>Riprova in un secondo momento o rivolgiti al nostro personale per maggiori informazioni e assistenza.",
  groupReservationError: "Purtroppo il processo di check-in online non è disponibile a causa della prenotazione di gruppo. Siete pregati di recarvi alla reception per effettuare il check-in.",
  tokenMalformed:
    "Impossibile riconoscere il link di prenotazione. Cercala inserendo di nuovo i dati.",
  manyReservationFound:
    "Abbiamo trovato <span class='font-bold'>{number}</span> prenotazioni con i dati forniti. Inserisci le informazioni seguenti: <span class='font-bold'>{input}</span> per perfezionare la ricerca.",
  checkinStartingAt:
    "Il check-in online comincerà tra {days} giorni, {hours} ore, {mins} minuti.\nTorna più tardi per completare il processo di check-in.",
  checkinClosedAt:
    "Il check-in online è stato chiuso {days} giorni e {hours} ore fa. Recati alla reception per effettuare il check-in di persona.",
};
