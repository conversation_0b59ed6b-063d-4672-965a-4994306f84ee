export default {
  header: "<PERSON><PERSON> del bambino",
  title: "Inserisci le informazioni del bambino",
  nameInputTitle: "<PERSON><PERSON>",
  surnameInputTitle: "Cognome",
  birthdayInputTitle: "Data di nascita",
  nationalityInputTitle: "Nazionalità",
  kinshipInputTitle: "Rapporto di parentela con il titolare della prenotazione",
  addressInputTitle: "Indirizzo",
  municipalityInputTitle: "<PERSON>mune",
  emailInputTitle: "Email",
  telephoneInputTitle: "Telefono",
  info:
    "Siamo interessati a conoscere questi dati dei nostri ospiti minorenni per poter offrire i migliori servizi ed essere preparati ad ogni possibile evenienza.",
  button: "Continua",
  modalMessage:
    "Ogni persona di età superiore a {maxAgeToNotRequireID} deve presentare il proprio documento d'identità per effettuare il check-in presso {hotelName}. Abbiamo calcolato che il bambino ha {age}, gius<PERSON>?",
  modalErrorMessage:
    "L'età dell'ospite è superiore a quella richiesta per essere considerato un {paxType}. Riprova.",
  goBack: "Vai al passaggio precedente",
  kinshipOptions: {
    son: "Figlio",
    nephew: "Nipote",
    grandson: "Nonno",
    brother: "Fratello",
    cousin: "Cugino",
    other: "Altro"
  },
  postal_codeInputTitle: "Codice postale",
};
