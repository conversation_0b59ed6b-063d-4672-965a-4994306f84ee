export default {
	header: "Numero di telefono",
	title: "Rimani informato durante il soggiorno",
	info: "Possiamo segnalare eventi di interesse, ad esempio quando la tua camera è pronta o altri eventi importanti durante il tuo soggiorno.",
	alertSMS:
		"* <PERSON> acc<PERSON>, {brandName} ti informerà via SMS di qualsiasi evento che possa essere di tuo interesse durante il tuo soggiorno. Ad esempio, la disponibilità delle camere, le gite di un giorno, le attività o gli eventi che si svolgono durante il soggiorno.",
	checkbox:
		"Vuoi che continuiamo a inviarti informazioni sulle nostre novità anche dopo il tuo soggiorno?",
	keepPhone:
		"Acconsento alla memorizzazione del mio numero di telefono nel database per migliorare la qualità dell'esperienza di soggiorno",
	number: "Numero di telefono",
	countrySelectorLabel: "Codice <PERSON>ese",
	phoneNumberLabel: "Numero di telefono *",
	exampleLabel: "Esempio:",
	phoneNumberFormat: "Codice Paese – Numero di telefono",
	phoneNumberErrorMessage:
		"Non è stato possibile verificare il numero di telefono. Riprova.",
	phoneCodeErrorTitle: "Il codice di verifica inserito non è corretto",
	phoneCodeErrorMessage:
		"È necessario inserire il codice di verifica che ti abbiamo inviato sul telefono tramite SMS. Inserisci nuovamente il codice o rimuovi/modifica il numero di telefono.",
	phoneNumberMaximumAttemptsExceeded:
		"Hai raggiunto il massimo numero di tentativi. Sarai reindirizzato alla schermata seguente per continuare il processo.",
	phoneCodeErrorButton: "Riprova",
	codeTitle: "Conferma il codice che ti abbiamo inviato con un SMS.",
	codePhoneLabel: "Inserisci il codice",
	codeNotRecived: "Se non ricevi il codice dopo alcuni minuti,",
	tryAgain: "fai clic qui per riprovare.",
	continue: "Continua",
};
