export default {
  minLengthError: "Il valore deve contenere minimo {number} caratteri",
  maxLengthError: "Il valore deve contenere massimo {number} caratteri",
  emptyValueError: "Il valore non può essere vuoto",
  dateFormatError: "Il valore non ha il formato previsto",
  defaultOption: "Seleziona un’opzione:",
  optional: "Facoltativo",
  invalidDocumentNumber: "Il numero di documento non è valido",
  invalidDocumentSupportNumber: "Deve contenere 3 lettere seguite da 6 cifre",
  postalCodeError: "Il codice postale non è valido",
  date: {
    greaterThanTodayError: "Il {name} non può essere superiore alla data di oggi",
    lowerThanCheckInDateError: "Il {name} non può essere inferiore alla data del check-in",
    incorrectPaxError:
      "La data di nascita non corrisponde a {paxType}",
    tooOld: "Data di nascita errata"
  },
    documentNumberDuplicated: "Questo documento è stato utilizzato in un altro ospite di questo checkin."
};
