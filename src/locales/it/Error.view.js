export default {
  header: "Ops... Errore",
  default: {
    header: "Ops... Errore",
    title: "Ops. Abbiamo riscontrato un problema interno.",
    message:
      "<PERSON><PERSON><PERSON><PERSON>, il servizio di check-in online non è al momento disponibile.<br>Riprova più tardi o rivolgiti al nostro personale per maggiori informazioni e assistenza."
  },
  maxAttemptsReservationError: {
    title: "Hai raggiunto il massimo numero di tentativi",
    message:
      "Hai raggiunto il massimo numero di tentativi per trovare la tua prenotazione.<br>Rivolgiti al nostro personale per maggiori informazioni e assistenza.",
    buttons: { default: "Esci dal processo" }
  },
  brandError: {
    title:
      "Ops. Abbiamo riscontrato un problema durante la ricerca della struttura",
    message:
      "Purtroppo, il servizio di check-in online non è al momento disponibile in questa struttura.<br>R<PERSON>rova più tardi o rivolgiti al nostro personale per maggiori informazioni e assistenza.",
    buttons: { default: "Riprova" }
  },
  reservationError: {
    title: "Non abbiamo trovato nessun processo di check-in in corso",
    message:
      "O il processo di CHECK-IN è già stato completato o non riusciamo a trovare nessuna prenotazione con questi dati.<br>Riprova più tardi o rivolgiti al nostro personale per maggiori informazioni e assistenza.",
    buttons: { default: "Riprova" }
  },
  ageError: {
    title: "L’età inserita non è corretta",
    message:
      "L’età del bambino non corrisponde alle informazioni della prenotazione. Riprova più tardi o rivolgiti al nostro personale per maggiori informazioni e assistenza.",
    buttons: { default: "Effettua il check-in per un altro ospite" }
  },
  scanError: {
    buttons: { default: "Esci dal processo" },
    title: "Si è verificato un errore durante il processo di scansione",
    message:
      "Si è verificato un errore durante il processo di scansione.<br>Riprova più tardi o rivolgiti al nostro personale per maggiori informazioni e assistenza."
  },
  maxAttemptsScanError: {
    buttons: { default: "Esci dal processo" },
    title: "Hai raggiunto il massimo numero di tentativi",
    message:
      "Hai superato il limite di tentativi di scansione dei documenti perché hai provato a scansionare un numero eccessivo di volte.<br>Rivolgiti al nostro personale per maggiori informazioni e assistenza."
  },
  maxAttemptsScanErrorWithCompleteData: {
    buttons: {
      exitProcess: "Esci dal processo",
      completeData: "Compila i dati manualmente"
    },
    title: "Hai raggiunto il massimo numero di tentativi",
    message:
      "Hai superato il limite di tentativi di scansione dei documenti perché hai provato a scansionare un numero eccessivo di volte.<br>Inserisci le informazioni di check-in manualmente o rivolgiti al nostro personale per maggiori informazioni e assistenza.<br>Ti ricordiamo che per effettuare il check-in è obbligatorio fornire un documento d’identità valido.<br>Se non puoi caricare il documento in questo momento, puoi inserire le informazioni di check-in manualmente e in seguito dovrai fornire la documentazione al tuo arrivo in struttura. Cosa vorresti fare?"
  },
  error404: {
    title:
      "Sfortunatamente non siamo stati in grado di trovare le informazioni richieste",
    message: "Per uscire, è possibile chiudere la scheda.",
    buttons: { default: "Premi per tornare all’inizio" },
    header: "Pagina non trovata"
  },
  exitText: "Per uscire, è possibile chiudere la scheda."
};
