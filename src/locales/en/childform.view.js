export default {
  header: "Child data",
  title: "Enter the child's details",
  nameInputTitle: "Name",
  surnameInputTitle: "First surname",
  birthdayInputTitle: "Date of birth",
  nationalityInputTitle: "Nationality",
  kinshipInputTitle: "Family relationship with the reservation holder",
  addressInputTitle: "Address",
  municipalityInputTitle: "Municipality",
  emailInputTitle: "Email",
  telephoneInputTitle: "Telephone",
  info:
    "We are interested in knowing this data of our underage guests in order to offer the best services and be prepared for any possible contingencies.",
  button: "Continue",
  modalMessage:
    "Any person over {maxAgeToNotRequireID} must present their ID in order to check in at {hotelName}. We have calculated that the child is {age}, is this correct?",
  modalErrorMessage:
    "The age of the guest is older than the age required to be considered a {paxType}. Please try again.",
  goBack: "Go to previous step",
  kinshipOptions: {
    son: "Son",
    nephew: "<PERSON><PERSON><PERSON><PERSON>",
    grandson: "<PERSON><PERSON>",
    brother: "Brother",
    cousin: "Cousin",
    other: "Other"
  },
  postal_codeInputTitle: "Postal code",
};
