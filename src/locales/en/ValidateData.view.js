export default {
	header: "Validate your information",
	title: "Check that the scanned information is correct and confirm it.",
	modalTitle: "Important!",
	modalDuplicatesTitle: "Remember",
	truthfulData:
		"We remind you that the details provided to the property must be truthful and they will be checked by our staff.",
	modalDuplicates:
		"Some fields have been modified. Make sure they are correct before continuing.",
	name: "Name",
	surname: "First surname",
	second_surname: "Second surname",
	birthday_date: "Date of birth",
	nationality: "Nationality",
	gender: "Sex",
	address: "Address",
	municipality: "Municipality",
	postal_code: "Postal code",
	empty_value: "Fill in your {input}",
	document_number: "Document number",
	document_support_number: "Support number",
	document_type: "Document type",
	date_of_issue: "Date of issue",
	date_of_expiry: "Date of expiry",
	issuing_country: "Document country of issue",
	fiscal_code: "Tax Code",
	detectedValuesWithMuchDifference:
		"We have noticed that there have been changes in the following fields:",
	button: "Confirm and continue",
	closeModal: "OK, understood.",
	inputErrorMessage: "Please, enter a valid date.",
	passport: "Passport",
	identity_card: "ID card",
	driving_license: "Driver's license",
	residence_permit: "Residency permit",
	male: "Male",
	female: "Female",
	CCAA: "Region",
	province: "Province",
	region: "Region",
	subregion: "Subregion",
	residence_country: "Residence country",
	street_number: "Street number",
	telephone: "Phone number",
	email: "Email",
	placeHolder: "DD-MM-YYYY",
	placeHolderAmerican: "MM-DD-YYYY",
	card: {
		title: "Validated Information",
		data: "Data",
		toggle: "Show all",
	},
	kinship: "Family relationship with the reservation holder",
	son: "Son",
	nephew: "Nephew",
	grandson: "Grandson",
	brother: "Brother",
	cousin: "Cousin",
	other: "Other"
};
