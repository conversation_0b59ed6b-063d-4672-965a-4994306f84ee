export default {
	header: "Telephone number",
	title: "Stay informed during your stay",
	info: "We can notify you of events of interest, such as when your room is ready or other important events during your stay.",
	alertSMS:
		"* If you accept, {brandName} will inform you via SMS of any event that may be of your interest during your stay. For example, room availability, one-day trips, activities or events taking place during your stay.",
	checkbox:
		"Would you like us to continue sending you information about our news after your stay?",
	keep<PERSON>hone:
		"I consent to have my telephone number stored in the database to enhance the quality of my stay experience.",
	number: "Telephone number",
	countrySelectorLabel: "Country code",
	phoneNumberLabel: "Telephone number *",
	exampleLabel: "Example:",
	phoneNumberFormat: "Country code – Telephone number",
	phoneNumberErrorMessage:
		"We have been unable to verify the telephone number. Please, try again.",
	phoneCodeErrorTitle: "The verification code you have entered is not correct",
	phoneCodeErrorMessage:
		"You must enter the verification code that we have sent to your phone via SMS. Please, re-enter the code or remove/change the phone number.",
	phoneNumberMaximumAttemptsExceeded:
		"You have exceeded the maximum number of attempts. We will redirect you to the following screen to continue with the process.",
	phoneCodeErrorButton: "Try again",
	codeTitle: "Please confirm the code that we have sent you in an SMS.",
	codePhoneLabel: "Enter the code",
	codeNotRecived: "If you have not received the code after a few minutes,",
	tryAgain: "click here to try again.",
	continue: "Continue",
};
