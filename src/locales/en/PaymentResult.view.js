export default {
  header: "Payment result",
  titleOk: "The payment was successful!",
  titleKo: "The payment did not go through",
  textOkFirst: "We have correclty received the payment of ",
  textOkSecond:
    "This is the confirmation number of the payment. Remember to ask for a invoice at the reception desk",
  textKo:
    "We have encountered an issue with the payment, please try again or go to the reception desk to proceed with the payment with the help of a member of our staff. ",
  continue: "Continue",
  continueWithoutPay: "Pay at the reception desk",
  tryAgain: "Try again",
  modal_info_text:
    "Once you finish the check-in process, contact our staff to get help with the payment process. Continue with the online check-in.",
  modal_info_title: "Do not worry",
  service: "service",
  operation: "operation",
  receiptTitle: "Payment confirmation",
  receiptDate: "Date:",
  receiptAuthorization: "Authorization:",
  receiptOrder: "Order:",
  receiptQuantity: "Quantity:",
  receiptMethod: "Payment method:",
  receiptLastDigits: "Last digits"
};
