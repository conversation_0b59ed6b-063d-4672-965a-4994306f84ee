export default {
  minLengthError: "Value must contain a minimum of {number} characters",
  maxLengthError: "Value must contain a maximum of {number} characters",
  emptyValueError: "The value cannot be empty",
  dateFormatError: "The value doesn't have the expected format",
  defaultOption: "Select an option:",
  optional: "Optional",
  invalidDocumentNumber: "The document number is invalid",
  invalidDocumentSupportNumber: "Must contain 3 letters followed by 6 digits",
  postalCodeError: "The postal code is invalid",
  date: {
    greaterThanTodayError: "The {name} can't be greater than today's date",
    lowerThanCheckInDateError: "The {name} can't be lower than check-in's date",
    incorrectPaxError:
      "The date of birth does not correspond to a/an {paxType}",
    tooOld: "Incorrect date of birth"
  },
  documentNumberDuplicated: "This document has been used in another guest of this checkin."
};
