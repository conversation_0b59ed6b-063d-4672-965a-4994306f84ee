export default {
  header: "Find your reservation",
  title: "We are going to look for your booking",
  text: "Fill in all the required fields to be able to locate your booking in our system. You will be able to find the necessary information in the data that was provided to you at the time of booking. Search by:",
  button: "Find your reservation",
  foundTitle: "We have found {count}",
  booking: "booking | bookings",
  check_in: "Check-in date",
  check_out: "Check-out date",
  first_name: "Name",
  reservation_code: "Reservation reference",
  foundText: "Please, enter the following information to find your booking:",
  reservationNotFound:
    "We have been unable to find a reservation with the details provided. Please, try again.",
  invalidReservation:
  "Your booking from {checkIn} to {checkOut} does not allow online check-in. If you think it is an error, please contact the front desk.",
  integrationError:
    "Unfortunately, the online check-in service is currently unavailable. <br> Please try again at a later date or contact our staff for more information and assistance.",
  groupReservationError: 
  "Unfortunately the online check-in process is not available due to the fact that your reservation is a group booking. Please go to the front desk to check in.",
  tokenMalformed:
    "Unable to recognise the booking link. Please, search for it by entering the data again.",
  manyReservationFound:
    "We have found <span class='font-bold'>{number}</span> reservations with the details provided. Please enter the following information: <span class='font-bold'>{input}</span> to adjust the search.",
  checkinStartingAt:
    "Online check-in will start in {days} days, {hours} hours, {mins} minutes.\nPlease come back later to complete your check-in process.",
  checkinClosedAt:
    "Online checkin closed {days} days and {hours} hours ago. Please go to reception to check-in in person.",
};
