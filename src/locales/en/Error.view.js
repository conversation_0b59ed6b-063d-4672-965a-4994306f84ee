export default {
  header: "Oops... Error",
  default: {
    header: "Oops... Error",
    title: "Oops. We have encountered an internal problem",
    message:
      "Unfortunately, the online check-in service is currently unavailable.<br>Please try again later or ask our staff for more information and help."
  },
  maxAttemptsReservationError: {
    title: "You have reached the maximum number of attempts",
    message:
      "You have reached the maximum number of attempts to find your reservation.<br>Please ask our staff for more information and help.",
    buttons: { default: "Exit the process" }
  },
  brandError: {
    title:
      "Oops. We have encountered a problem when searching for the property",
    message:
      "We are sorry, but the online check-in is currently unavailable in this property.<br>Please try again later or ask our staff for more information and help.",
    buttons: { default: "Try again" }
  },
  reservationError: {
    title: "We have found no pending check-in process",
    message:
      "Either the CHECK-IN process has already been completed or we can't find any reservation with these details.<br>Please try again later or ask our staff for more information and help.",
    buttons: { default: "Try again" }
  },
  ageError: {
    title: "The age you have entered is not correct",
    message:
      "The age of the child does not match the information in the reservation. Please try again later or ask our staff for more information and help.",
    buttons: { default: "Check in another guest" }
  },
  scanError: {
    buttons: { default: "Exit the process" },
    title: "An error has occurred during the scan process",
    message:
      "An error has occurred during the scan process.<br>Please try again later or ask our staff for more information and help."
  },
  maxAttemptsScanError: {
    buttons: { default: "Exit the process" },
    title: "You have reached the maximum number of attempts",
    message:
      "You have exceeded the limit of attempts to scan documents when trying to scan too many times.<br>Please ask our staff for more information and help."
  },
  maxAttemptsScanErrorWithCompleteData: {
    buttons: {
      exitProcess: "Exit the process",
      completeData: "Complete data by hand"
    },
    title: "You have reached the maximum number of attempts",
    message:
      "You have exceeded the limit of attempts to scan documents when trying to scan too many times.<br>Please complete your check-in details by hand or ask our staff for more information.<br>We remind you providing a valid identity document is mandatory to check in.<br>If you cannot upload the document at this time, you can complete your check-in details by hand and then you must provide the documentation when you arrive to the property. What would you like to do?"
  },
  error404: {
    title:
      "Unfortunately, we have been unable to find the requested information",
    message: "You can close the tab to exit.",
    buttons: { default: "Press to go back to the start" },
    header: "Page not found"
  },
  exitText: "You can close the tab to exit."
};
