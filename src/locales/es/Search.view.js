export default {
  header: "Buscar reserva",
  title: "Vamos a buscar su reserva",
  text: "Rellene todos los campos requeridos para poder localizar su reserva en nuestro sistema. Podrá encontrar la información necesaria en los datos que le fueron suministrados en el momento de hacer la reserva. Buscar por:",
  button: "Buscar reserva",
  foundTitle: "Hemos encontrado {count}",
  booking: "reserva | reservas",
  check_in: "Fecha de check-in",
  check_out: "Fecha de check-out",
  first_name: "Nombre",
  reservation_code: "Código de reserva",
  foundText:
    "Por favor, introduzca los siguientes datos para localizar su reserva:",
  reservationNotFound:
    "No hemos encontrado ninguna reserva con los datos proporcionados. Por favor, inténtelo de nuevo.",
  invalidReservation:
    "Su reserva del {checkIn} al {checkOut} no permite hacer el checkin online. Si usted cree que es un error por favor contacte con recepción.",
  integrationError:
    "Lamentablemente el servicio de check-in online no está disponible en este momento.<br>Por favor, inténtelo de nuevo más adelante o contacte con nuestro personal para obtener más información y ayuda.",
  groupReservationError: "Lamentablemente el proceso de check-in online no está disponible debido a que su reserva es grupal. Por favor, acuda a recepción para realizarlo.",
  tokenMalformed:
    "No hemos reconocido el enlace de la reserva. Por favor, búsquela introduciendo los datos de nuevo.",
  manyReservationFound:
    "Hemos encontrado <span class='font-bold'>{number}</span> reservas con los datos proporcionados. Por favor, introduzca la siguiente informacion: <span class='font-bold'>{input}</span> para ajustar la búsqueda.",
  checkinStartingAt:
    "El checkin online comenzará en {days} días, {hours} horas, {mins} minutos. \nPor favor, vuelva más adelante para completar su proceso de check-in.",
  checkinClosedAt:
    "El checkin online se cerró hace {days} días y {hours} horas. Por favor, acuda a recepción para hacer el check-in de forma presencial",
};
