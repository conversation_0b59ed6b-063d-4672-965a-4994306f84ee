export default {
	header: "Número de teléfono",
	title: "Manténgase informado durante su estancia",
	info: "Podemos avisarle de eventos de interés, como por ejemplo, cuando su habitación esté lista o de otros eventos importantes durante su estancia.",
	alertSMS:
		"* Si acepta, {brandName} le informará mediante SMS de acontecimientos de interés durante su estancia. Por ejemplo, la disponibilidad de la habitación, excursiones, actividades o eventos celebrados durante su alojamiento.",
	checkbox:
		"¿Desea que sigamos mandándole información sobre nuestras novedades después de su estancia?",
	keepPhone:
		"Acepto que guarden mi número de teléfono en la base de datos para mejorar la experiencia de mi estancia.",
	number: "Número de teléfono",
	countrySelectorLabel: "Código de área",
	phoneNumberLabel: "Número de teléfono *",
	exampleLabel: "Ejemplo:",
	phoneNumberFormat: "Código área - Número de teléfono",
	phoneNumberErrorMessage:
		"No hemos podido verificar el número de teléfono. Por favor, inténtelo de nuevo.",
	phoneCodeErrorTitle:
		"El código de verificación que ha introducido no es correcto",
	phoneCodeErrorMessage:
		"Debe introducir el código de verificación que le hemos enviado a su teléfono mediante un SMS. Por favor, vuelva a introducir el código o elimine/cambie el número de teléfono.",
	phoneCodeErrorButton: "Volver a intentarlo",
	phoneNumberMaximumAttemptsExceeded:
		"Ha sobrepasado el número máximo de intentos. Vamos a llevarle al siguiente paso para continuar con el proceso.",
	codeTitle: "Confirme el código que le hemos enviado en un SMS.",
	codePhoneLabel: "Introduzca el código",
	codeNotRecived: "Si no ha recibido el código pasados unos minutos,",
	tryAgain: "pulse aquí para que volvamos a enviárselo.",
	continue: "Continuar",
};
