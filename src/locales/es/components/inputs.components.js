export default {
  minLengthError: "El valor ha de tener más de {number} caracteres",
  maxLengthError: "El valor ha de tener menos de {number} caracteres",
  emptyValueError: "El valor no puede estar vacio",
  dateFormatError: "El valor no tiene el formato esperado",
  defaultOption: "Seleccione una opción:",
  optional: "Opcional",
  invalidDocumentNumber: "El número de documento no es válido",
  invalidDocumentSupportNumber: "Debe contener 3 letras seguidas de 6 dígitos",
  postalCodeError: "El código postal no es válido",
  date: {
    greaterThanTodayError: "La {name} no puede superar la fecha de hoy",
    lowerThanCheckInDateError:
      "La {name} no puede ser inferior a la fecha de check-in",
    incorrectPaxError: "La fecha de nacimiento no corresponde con un {paxType}",
    tooOld: "Fecha de nacimiento incorrecta"
  },
  documentNumberDuplicated: "Este documento se ha utilizado en otro huésped de este checkin"
};
