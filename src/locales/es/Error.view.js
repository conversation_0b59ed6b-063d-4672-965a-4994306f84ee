export default {
  header: "Ups... Error",
  default: {
    header: "Ups... Error",
    title: "Ups. Hemos sufrido un problema interno",
    message:
      "Lamentablemente el servicio de check-in online no está disponible en este momento.<br>Por favor, inténtelo de nuevo más adelante o contacte con nuestro personal para obtener más información y ayuda."
  },
  maxAttemptsReservationError: {
    title: "Ha alcanzado el número máximo de intentos",
    message:
      "Ha sobrepasado el límite de intentos para la búsqueda de reserva.<br>Por favor, contacte con nuestro personal para obtener más información y ayuda.",
    buttons: { default: "Salir del proceso" }
  },
  brandError: {
    title: "Ups. Hemos sufrido un problema al buscar el establecimiento",
    message:
      "Lo sentimos, pero este establecimiento no tiene disponible el check-in online en este momento.<br>Por favor, inténtelo de nuevo más adelante o contacte con nuestro personal para obtener más información y ayuda.",
    buttons: { default: "Volver a intentarlo" }
  },
  reservationError: {
    title: "No hemos encontrado ningún proceso de CHECK-IN pendiente",
    message:
      "O bien, el proceso de CHECK-IN ya ha sido completado o no encontramos ninguna reserva con esos datos.<br>Por favor, contacte con nuestro personal para obtener más información y ayuda.",
    buttons: { default: "Volver a intentarlo" }
  },
  ageError: {
    title: "La edad que ha introducido no es correcta",
    message:
      "La edad del menor no concuerda con la información de la reserva. Por favor, inténtelo de nuevo más adelante o contacte con nuestro personal para obtener más información y ayuda.",
    buttons: { default: "Hacer el check in de otro huésped" }
  },
  scanError: {
    buttons: { default: "Salir del proceso" },
    title: "Ha ocurrido un error en el proceso de escaneo",
    message:
      "Ha ocurrido un error inesperado en el escaneo de documentos.<br>Por favor, inténtelo de nuevo más adelante o contacte con nuestro personal para obtener más información y ayuda."
  },
  maxAttemptsScanError: {
    buttons: { default: "Salir del proceso" },
    title: "Ha alcanzado el número máximo de intentos",
    message:
      "Ha sobrepasado el límite de intentos para el escaneo de documentos al intentar realizar el escaneo demasiadas veces.<br>Por favor, contacte con nuestro personal para obtener más información y ayuda."
  },
  maxAttemptsScanErrorWithCompleteData: {
    buttons: {
      exitProcess: "Salir del proceso",
      completeData: "Completar datos manualmente"
    },
    title: "Ha alcanzado el número máximo de intentos",
    message:
      "Ha sobrepasado el límite de intentos al intentar realizar el escaneo de documentos demasiadas veces.<br>Por favor, complete los datos a mano o contacte con nuestro personal para obtener más información y ayuda.<br>Le recordamos que la presentación de un documento de identidad válido es obligatorio para realizar el check-in en el establecimiento.<br>En caso de no poder subir el documento en estos momentos, completará los datos de su check-in a mano y después deberá presentar la documentación en el momento de su llegada al establecimiento. ¿Qué desea hacer?"
  },
  error404: {
    title:
      "Lamentablemente, no se ha podido encontrar la información solicitada",
    message: "Puede cerrar la pestaña para salir.",
    buttons: "Pulse para volver al inicio",
    header: "Página no encontrada"
  },
  exitText: "Puede cerrar la pestaña para salir"
};
