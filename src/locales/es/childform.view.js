export default {
  header: "Información del menor",
  title: "Introduzca los datos del menor",
  nameInputTitle: "Nombre",
  surnameInputTitle: "Primer <PERSON><PERSON>lido",
  birthdayInputTitle: "<PERSON>cha de nacimiento",
  nationalityInputTitle: "Nacionalidad",
  kinshipInputTitle: "Parentesco con el titular de la reserva",
  addressInputTitle: "Dirección",
  municipalityInputTitle: "Municipio",
  emailInputTitle: "Correo electrónico",
  telephoneInputTitle: "Teléfono",
  info:
    "Nos interesa conocer estos datos de las personas menores de edad que están en nuestros establecimientos para poder ofrecer los mejores servicios y estar preparados ante posibles contingencias.",
  button: "Continuar",
  modalMessage:
    "Toda persona con más de {maxAgeToNotRequireID} años debe presentar su documento de identidad para poder registrarse en {hotelName}. Hemos calculado que el menor tiene {age} años, ¿esto es correcto?",
  modalErrorMessage:
    "La edad del huésped es mayor que la requerida para ser considerado un {paxType}. Por favor, inténtelo de nuevo.",
  goBack: "Ir al paso anterior",
  kinshipOptions: {
    son: "Hijo",
    nephew: "Sobrino",
    grandson: "Nieto",
    brother: "Hermano",
    cousin: "Primo",
    other: "Otro"
  },
  postal_codeInputTitle: "Código postal",
};
