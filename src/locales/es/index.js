import login from "./Login.view";
import welcome from "./Welcome.view";
import privacy from "./Privacy.view";
import brandselector from "./BrandSelector.view";
import search from "./Search.view";
import error from "./Error.view";
import status from "./Status.view";
import shared from "./Shared";
import comments from "./comments.view";
import reservations from "./Reservations.view";
import scan from "./Scan.view";
import advancedscan from "./AdvancedScan.view";
import sensibledata from "./SensibleData.view";
import searchFormComponent from "./components/searchForm.component";
import inputs from "./components/inputs.components";
import modal from "./components/modal.component";
import subheader from "./components/subheader.component";
import statusComponent from "./components/status.component";
import loading from "./components/loading.component";
import validatedata from "./ValidateData.view";
import childsdata from "./ChildsData.view";
import documents from "./Documents.view";
import actualProcessName from "./components/actualProcessName";
import signature from "./Signature.view";
import phoneform from "./PhoneForm.view";
import phoneverification from "./PhoneVerification.view";
import confirmation from "./Confirmation.view";
import selectroom from "./SelectRoom.view";
import room from "./components/room.component";
import share from "./Share.view";
import childform from "./childform.view";
import senddocuments from "./SendDocuments.view";
import header from "./components/header.component";
import countries from "./countries";
import redirectLink from "./components/redirectLink.component";
import documentUploader from "./components/documentUploader.component";
import footer from "./components/footer.component";
import demoHeader from "./demoHeader";

// Payments literals
import payment from "./Payment.view";
import paymentproform from "./PaymentProform.view";
import paymentresult from "./PaymentResult.view";
import proformcomponent from "./components/payment/proformComponent";
import paymentoptioncomponent from "./components/payment/paymentOptionComponent";
import paymentbuttoncomponent from "./components/payment/paymentButtonComponent";
import nopaymentoptioncomponent from "./components/payment/noPaymentOptionComponent";
import paynopaincardmethod from "./components/payment/platforms/paynopain/methods/paynopainCardMethod";
import languages from "./languages";
// Reception literals
import receptionvalidatedata from "./Reception/ValidateData.view";

export default {
	login,
	welcome,
	privacy,
	brandselector,
	search,
	error,
	status,
	scan,
	advancedscan,
	sensibledata,
	reservations,
	searchFormComponent,
	inputs,
	shared,
	comments,
	modal,
	subheader,
	statusComponent,
	validatedata,
	childsdata,
	loading,
	documents,
	actualProcessName,
	signature,
	phoneform,
	phoneverification,
	confirmation,
	selectroom,
	room,
	share,
	childform,
	senddocuments,
	header,
	footer,
	countries,
	redirectLink,
	payment,
	paymentproform,
	paymentresult,
	proformcomponent,
	paymentoptioncomponent,
	paymentbuttoncomponent,
	nopaymentoptioncomponent,
	paynopaincardmethod,
	documentUploader,
	receptionvalidatedata,
	languages,
	demoHeader,
};
