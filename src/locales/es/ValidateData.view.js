export default {
	header: "Valide su información",
	title: "Revise que la información escaneada sea correcta y confírmela.",
	modalTitle: "¡Importante!",
	modalDuplicatesTitle: "Recuerde",
	truthfulData:
		"Le recordamos que los datos que indique al establecimiento han de ser verídicos ya que serán cotejados por nuestro personal.",
	modalDuplicates:
		"Algunos campos han sido modificados. Asegúrese de que son correctos antes de continuar.",
	name: "<PERSON><PERSON>",
	surname: "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>",
	second_surname: "<PERSON><PERSON><PERSON>",
	birthday_date: "<PERSON><PERSON> de nacimiento",
	nationality: "Nacionalidad",
	gender: "<PERSON><PERSON>",
	address: "Dirección",
	municipality: "Municipio",
	postal_code: "Código postal",
	empty_value: "Rellene su {input}",
	document_number: "Número de documento",
	document_support_number: "Número de soporte",
	document_type: "Tipo de documento",
	date_of_issue: "Fecha de expedición",
	date_of_expiry: "Fecha de caducidad",
	issuing_country: "País de expedición del documento",
	fiscal_code: "Código fiscal",
	detectedValuesWithMuchDifference:
		"Hemos detectado que han habido cambios en los siguiente campos:",
	button: "Confirmar y continuar",
	closeModal: "Ok, entendido.",
	inputErrorMessage: "Por favor, introduzca una fecha válida.",
	passport: "Pasaporte",
	identity_card: "DNI",
	driving_license: "Licencia de conducir",
	residence_permit: "Permiso de residencia",
	male: "Hombre",
	female: "Mujer",
	CCAA: "Comunidad autónoma",
	province: "Provincia",
	region: "Región",
	subRegion: "Subregión",
	residence_country: "País de residencia",
	street_number: "Número de calle",
	telephone: "Teléfono",
	email: "Email",
	placeHolder: "DD-MM-AAAA",
	placeHolderAmerican: "MM-DD-AAAA",
	card: {
		title: "Información validada",
		data: "Datos",
		toggle: "Ver todo",
	},
	son: "Hijo",
	kinship: "Parentesco con el titular de la reserva",
	nephew: "Sobrino",
	grandson: "Nieto",
	brother: "Hermano",
	cousin: "Primo",
	other: "Otro"
};
