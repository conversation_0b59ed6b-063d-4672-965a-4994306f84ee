export default {
  header: "Oups... Erreur",
  default: {
    header: "Oups... Erreur",
    title: "Oups. Nous avons rencontré un problème interne",
    message:
      "Malheureusement, le service de check-in en ligne n'est actuellement pas disponible. <br> Veuillez réessayer ultérieurement ou contacter notre personnel pour plus d'informations et d'aide."
  },
  maxAttemptsReservationError: {
    title: "Vous avez dépassé le nombre maximum de tentatives.",
    message:
      "Vous avez atteint le nombre maximum de tentatives pour trouver votre réservation.<br>Veuillez demander à notre personnel plus d'informations et d'aide.",
    buttons: { default: "Quitter le processus" }
  },
  brandError: {
    title:
      "Oups. Nous avons rencontré un problème lors de la recherche de la propriété",
    message:
      "Malheureusement, le service de check-in en ligne n'est actuellement pas disponible. <br> Veuillez réessayer ultérieurement ou contacter notre personnel pour plus d'informations et d'aide.",
    buttons: { default: "Veuillez réessayer" }
  },
  reservationError: {
    title: "Nous n'avons trouvé aucun processus de check-in en attente",
    message:
      "Soit le processus de check-in est déjà terminé, soit nous ne trouvons aucune réservation avec ces détails.<br>Veuillez réessayer plus tard ou demander à notre personnel plus d'informations et d'aide.",
    buttons: { default: "Veuillez réessayer" }
  },
  ageError: {
    title: "Le code de vérification que vous avez saisi n'est pas correct",
    message:
      "L'âge de l'enfant ne correspond pas aux informations contenues dans la réservation. Veuillez réessayer plus tard ou demander à notre personnel plus d'informations et d'aide.",
    buttons: { default: "Effectuer le check-in d'un autre invité" }
  },
  scanError: {
    buttons: { default: "Quitter le processus" },
    title: "Une erreur s'est produite lors du processus de numérisation.",
    message:
      "Une erreur s'est produite lors du processus d'analyse.<br>Veuillez réessayer plus tard ou demander à notre personnel plus d'informations et d'aide."
  },
  maxAttemptsScanError: {
    buttons: { default: "Quitter le processus" },
    title: "Vous avez dépassé le nombre maximum de tentatives.",
    message:
      "Vous avez dépassé la limite de tentatives de numérisation de documents en essayant de numériser trop de fois.<br>Veuillez demander plus d'informations et d'aide à notre personnel."
  },
  maxAttemptsScanErrorWithCompleteData: {
    buttons: {
      exitProcess: "Quitter le processus",
      completeData: "Compléter les données à la main"
    },
    title: "Vous avez dépassé le nombre maximum de tentatives.",
    message:
      "Vous avez dépassé la limite de tentatives de numérisation de documents en essayant de numériser trop de fois.<br>Veuillez compléter manuellement vos informations de check-in ou demander plus d'informations à notre personnel.<br>Nous vous rappelons que fournir une pièce d'identité valide est obligatoire pour le check-in.<br>Si vous ne pouvez pas télécharger le document à ce moment-là, vous pouvez compléter vos informations de check-in à la main et vous devrez ensuite fournir la documentation à votre arrivée à l'établissement. Qu'est-ce que vous aimeriez faire?"
  },
  error404: {
    title:
      "Malheureusement, nous n'avons pas pu trouver les informations demandées",
    message: "Vous pouvez fermer l'onglet pour quitter.",
    buttons: { default: "Appuyez pour revenir au début" },
    header: "Page non trouvée"
  },
  exitText: "Vous pouvez fermer l'onglet pour quitter."
};
