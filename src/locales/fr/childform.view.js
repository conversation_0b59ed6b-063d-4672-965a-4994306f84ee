export default {
  header: "Données des enfants",
  title: "Saisir les coordonnées de l'enfant",
  nameInputTitle: "Nom",
  surnameInputTitle: "Premier nom de famille",
  birthdayInputTitle: "Date de naissance",
  nationalityInputTitle: "Nationalité",
  kinshipInputTitle: "Relation familiale avec le titulaire de la réservation",
  addressInputTitle: "Adresse",
  municipalityInputTitle: "Municipalité",
  emailInputTitle: "Email",
  telephoneInputTitle: "Téléphone",
  info:
    "Nous souhaitons connaître les données de nos clients mineurs afin d'offrir les meilleurs services et de nous préparer à toute éventualité.",
  button: "Continuer",
  modalMessage:
    "Toute personne de plus de {maxAgeToNotRequireID} doit présenter sa pièce d'identité pour pouvoir effectuer le check-in à {hotelName}. Nous avons calculé que l'enfant a {age}. Est-ce exact ?",
  modalErrorMessage:
    "L'âge du voyageur est supérieur à l'âge requis pour être considéré comme un {paxType}. Veuillez réessayer plus tard.",
  goBack: "Aller à l'étape précédente",
  kinshipOptions: {
    son: "Fils",
    nephew: "Neveu",
    grandson: "Petit fils",
    brother: "Frère",
    cousin: "Cousin",
    other: "Autre"
  },
  postal_codeInputTitle: "Code postal"
};
