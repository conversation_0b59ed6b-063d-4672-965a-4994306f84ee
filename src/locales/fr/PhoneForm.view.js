export default {
	header: "Numéro de téléphone",
	title: "Restez informé pendant votre séjour",
	info: "Nous pouvons vous informer d'événements intéressants, comme lorsque votre chambre est prête ou d'autres événements importants pendant votre séjour.",
	alertSMS:
		"* Si vous acceptez, {brandName} vous informera par SMS de tout événement susceptible de vous intéresser lors de votre séjour. Par exemple, la disponibilité des chambres, les excursions d'une journée, les activités ou événements ayant lieu pendant votre séjour.",
	checkbox:
		" Souhaitez-vous que nous continuions à vous informer de notre actualité après votre séjour ? ",
	keepPhone:
		"J'accepte que mon numéro de téléphone soit enregistre dans la base de données pour améliorer la qualité de mon expérience de séjour.",
	number: "Numéro de téléphone",
	countrySelectorLabel: "Indicatif du pays",
	phoneNumberLabel: "Numéro de téléphone",
	exampleLabel: "Exemple:",
	phoneNumberFormat: "Indicatif pays – Numéro de téléphone",
	phoneNumberErrorMessage:
		"Nous n'avons pas pu vérifier le numéro de téléphone. Veuillez réessayer plus tard.",
	phoneCodeErrorTitle: "Le code de vérification que vous avez saisi n'est pas correct",
	phoneCodeErrorMessage:
		"Vous devez saisir le code de vérification que nous avons envoyé sur votre téléphone par SMS. Veuillez saisir à nouveau le code ou supprimer/modifier le numéro de téléphone.",
	phoneNumberMaximumAttemptsExceeded:
		"Vous avez dépassé le nombre maximum de tentatives. Nous vous redirigerons vers l'écran suivant pour poursuivre le processus.",
	phoneCodeErrorButton: "Veuillez réessayer",
	codeTitle: "Veuillez confirmer le code que nous vous avons envoyé par SMS.",
	codePhoneLabel: "Entrer le code",
	codeNotRecived: "Si vous n'avez pas reçu le code au bout de quelques minutes",
	tryAgain: "Cliquez ici pour essayer à nouveau.",
	continue: "Continuer",
};
