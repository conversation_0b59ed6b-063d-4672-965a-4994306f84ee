export default {
	header: "Validez vos informations",
	title:
		"Vérifiez que les informations scannées sont correctes et confirmez-les.",
	modalTitle: "Important !",
	modalDuplicatesTitle: "À ne pas oublier",
	truthfulData:
		"Nous vous rappelons que les informations fournies à l’établissement doivent être véridiques et qu'elles seront vérifiées par notre personnel.",
	modalDuplicates:
		"Certains champs ont été modifiés. Assurez-vous qu'ils sont corrects avant de continuer.",
	name: "Nom",
	surname: "Premier nom de famille",
	second_surname: "Deuxième nom de famille",
	birthday_date: "Date de naissance",
	nationality: "Nationalité",
	gender: "Sex<PERSON>",
	address: "Adresse",
	municipality: "Municipalité",
	postal_code: "Code postal",
	empty_value: "Remplissez votre {input}",
	document_number: "Numéro du document",
	document_support_number: "Numéro d'assistance",
	document_type: "Type de document",
	date_of_issue: "Date d'émission",
	date_of_expiry: "Date d'expiration",
	issuing_country: "Pays de délivrance du document",
	fiscal_code: "Code fiscal",
	detectedValuesWithMuchDifference:
		"Nous avons constaté des changements dans les domaines suivants :",
	button: "Confirmer et continuer",
	closeModal: "OK compris.",
	inputErrorMessage: "Veuillez entrer une date valide.",
	passport: "Passeport",
	identity_card: "Carte d'identité",
	driving_license: "Permis de conduire",
	residence_permit: "Titre de séjour",
	male: "Masculin",
	female: "Féminin",
	CCAA: "Région",
	province: "Province",
	region: "Région",
	subregion: "Sous-région/Département",
	residence_country: "Pays de résidence",
	street_number: "Numéro de rue",
	telephone: "Numéro de téléphone",
	email: "Email",
	placeHolder: "JJ-MM-AAAA",
	placeHolderAmerican: "MM-JJ-AAAA",
	card: {
		title: "Informations validées",
		data: "Données",
		toggle: "Afficher tout",
	},
	kinship: "Relation familiale avec le titulaire de la réservation",
	son: "Fils",
	nephew: "Neveu",
	grandson: "Petit fils",
	brother: "Frère",
	cousin: "Cousin",
	other: "Autre"
};
