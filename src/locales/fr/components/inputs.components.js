export default {
  minLengthError: "La valeur doit contenir au minimum {nombre} caractères",
  maxLengthError: "La valeur doit contenir un maximum de {nombre} caractères",
  emptyValueError: "Le champ de la valeur ne peut pas être vide",
  dateFormatError: "La valeur n'a pas le format attendu",
  defaultOption: "Sélectionner une option :",
  optional: "Facultatif",
  invalidDocumentNumber: "Le numéro de document n'est pas valide",
  invalidDocumentSupportNumber: "Doit contenir 3 lettres suivies de 6 chiffres",
  postalCodeError: "Le code postal n'est pas valide",
  date: {
    greaterThanTodayError: "Le {nom} ne peut pas être supérieur à la date d'aujourd'hui",
    lowerThanCheckInDateError: "Le {nom} ne peut pas être inférieur à la date du chekc-in",
    incorrectPaxError:
      "La date de naissance ne correspond pas à un {paxType}",
    tooOld: "Date de naissance incorrecte"
  },
  documentNumberDuplicated: "Ce document a été utilisé par un autre invité de ce check-in."
};
