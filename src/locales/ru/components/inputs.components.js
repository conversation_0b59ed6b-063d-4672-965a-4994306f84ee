export default {
  minLengthError: "Значение должно содержать минимум {число} символов",
  maxLengthError: "Значение должно содержать максимум {число} символов",
  emptyValueError: "Значение не может быть пустым",
  dateFormatError: "Значение имеет неверный формат",
  defaultOption: "Выберите вариант:",
  optional: "Необязательно",
  invalidDocumentNumber: "Номер документа недействителен",
  invalidDocumentSupportNumber: "Должно содержать 3 буквы, за которыми следуют 6 цифр",
  postalCodeError: "Почтовый индекс недействителен",
  date: {
    greaterThanTodayError: "Значение {name} не может быть позже сегодняшней даты",
    lowerThanCheckInDateError: "Значение {name} не может быть раньше даты заезда",
    incorrectPaxError:
      "Дата рождения не соответствует {paxType}",
    tooOld: "Неверная дата рождения"
  },
  documentNumberDuplicated: "Этот документ был использован другим гостем этой регистрации"
};
