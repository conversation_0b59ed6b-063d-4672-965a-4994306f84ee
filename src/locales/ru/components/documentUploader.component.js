export default {
	scanAgain: "Отсканируйте еще раз",
	delete: "Удалить",
	frontPart:
		"Отсканируйте лицевую сторону удостоверения личности, паспорта или вида на жительство",
	backPart:
		"Отсканируйте обратную сторону удостоверения личности, паспорта или вида на жительство",
	frontPartWithDrivingLicense:
		"Отсканируйте лицевую сторону удостоверения личности, паспорта, водительских прав или вида на жительство",
	backPartWithDrivingLicense:
		"Отсканируйте обратную сторону удостоверения личности, паспорта, водительских прав или вида на жительство",
	signaturePartWithDrivingLicense:
		"Пожалуйста, отсканируйте ту часть удостоверения личности, паспорта, водительского удостоверения или вида на жительство, на которой стоит ваша подпись",
	signaturePart:
		"Пожалуйста, отсканируйте ту часть удостоверения личности, паспорта или вида на жительство, на которой стоит ваша подпись",
	frontPartButton: "Отсканируйте лицевую сторону",
	backPartButton: "Отсканируйте обратную сторону",
	signatureButton: "Отсканируйте подпись",
	front: "Лицевая сторона",
	back: "Обратная сторона",
	signature: "Подпись",
	completed: "Завершить",
	missingBackPart: "Пожалуйста, отсканируйте обратную сторону",
	frontPartScanned: "Вы отсканировали лицевую сторону документа",
	backPartScanned: "Вы отсканировали обратную сторону документа",
	signaturePartScanned: "Вы отсканировали свою подпись",
	allRequiredInformation:
		"У нас есть вся необходимая информация для продолжения работы",
	signatureRequired: "Пожалуйста, отсканируйте подпись",
	identityDocument: "Документ, удостоверяющий личность",
	scannedDocument: "Отсканированный документ",
	passport: "Паспорт",
	drivingLicense: "Водительское удостоверение",
	residencePermit: "Вид на жите",
};
