export default {
  header: "Найдите свое бронирование",
  title: "Мы будем искать ваше бронирование",
  text: "Заполните все обязательные поля, чтобы найти свое бронирование в нашей системе. Необходимую информацию вы сможете найти в данных, которые были вам предоставлены при бронировании. Поиск по:",
  button: "Найдите свое бронирование",
  foundTitle: "Мы нашли {count}",
  booking: "бронирование | бронирований",
  check_in: "Дата регистрации заезда",
  check_out: "Дата регистрации отъезда",
  first_name: "<PERSON><PERSON><PERSON>",
  reservation_code: "Описание бронирования",
  foundText: "Введите следующую информацию, чтобы найти свое бронирование:",
  reservationNotFound:
    "Нам не удалось найти бронирование с указанными деталями. Пожалуйста, попробуйте еще раз.",
  invalidReservation:
  "Ваше бронирование с {checkIn} до {checkOut} не позволяет пройти онлайн-регистрацию. Если вы считаете, что это ошибка, обратитесь на стойку регистрации.",
  integrationError:
    "К сожалению, услуга онлайн-регистрации в настоящее время недоступна. <br> Повторите попытку позже или свяжитесь с нашими сотрудниками для получения дополнительной информации и помощи.",
  groupReservationError: "К сожалению, онлайн-регистрация недоступна из-за группового бронирования. Пожалуйста, пройдите на стойку регистрации для регистрации.",
  tokenMalformed:
    "Не удалось распознать ссылку на бронирование. Пожалуйста, выполните поиск повторно, введя данные еще раз.",
  manyReservationFound:
    "Мы нашли <span class='font-bold'>{number}</span> бронирований с указанными деталями. Введите следующую информацию: <span class='font-bold'>{input</span>, чтобы точнее настроить поиск.",
  checkinStartingAt:
    "Онлайн-регистрация начнется через {days} дней, {hours} часов, {mins} минут.\nПожалуйста, зайдите позже, чтобы завершить процесс регистрации.",
  checkinClosedAt:
    "Онлайн-регистрация была закрыта {days} дней и {hours} часов назад. Пожалуйста, пройдите на стойку регистрации, чтобы зарегистрировать свой заезд лично.",
};
