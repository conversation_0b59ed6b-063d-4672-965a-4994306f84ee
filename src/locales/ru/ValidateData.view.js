export default {
	header: "Проверьте свою информ<PERSON><PERSON><PERSON><PERSON>",
	title: "Проверьте правильность отсканированной информации и подтвердите ее.",
	modalTitle: "Важно!",
	modalDuplicatesTitle: "Помните",
	truthfulData:
		"Напоминаем, что данные, предоставленные отелю, должны быть правдивыми и будут проверены нашими сотрудниками.",
	modalDuplicates:
		"Некоторые поля были изменены. Прежде чем продолжить, убедитесь, что они верны.",
	name: "<PERSON><PERSON><PERSON>",
	surname: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> имя",
	second_surname: "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>",
	birthday_date: "Дата рождения",
	nationality: "Национальность",
	gender: "Пол",
	address: "Адрес",
	municipality: "Город",
	postal_code: "Почтовый индекс",
	empty_value: "Введите свой {input}",
	document_number: "Номер документа",
	document_support_number: "Номер подтверждения",
	document_type: "Тип документа",
	date_of_issue: "Дата выдачи",
	date_of_expiry: "Дата истечения срока",
	issuing_country: "Страна, выдавшая документ",
	fiscal_code: "Налоговый кодекс",
	detectedValuesWithMuchDifference:
		"Мы заметили, что произошли изменения в следующих полях:",
	button: "Подтвердить и продолжить",
	closeModal: "OK, понятно.",
	inputErrorMessage: "Введите действительную дату.",
	passport: "Паспорт",
	identity_card: "Удостоверение личности",
	driving_license: "Водительское удостоверение",
	residence_permit: "Вид на жите",
	male: "Мужской",
	female: "Женский",
	CCAA: "Регион",
	province: "Провинция",
	region: "Регион",
	subregion: "Субрегион",
	residence_country: "Страна проживания",
	street_number: "Номер улицы",
	telephone: "Номер телефона",
	email: "Эл.адрес",
	placeHolder: "ДД-ММ-ГГГГ",
	placeHolderAmerican: "ММ-ДД-ГГГГ",
	card: {
		title: "Проверенная информация",
		data: "Данные",
		toggle: "Показать все",
	},
	kinship: "Степень родства с владельцем бронирования",
	son: "Сын",
	nephew: "Племянник",
	grandson: "Внук",
	brother: "Брат",
	cousin: "Двоюродный брат/сестра",
	other: "Другое"
};
