export default {
  header: "Результат оплаты",
  titleOk: "Оплата прошла успешно!",
  titleKo: "Платеж не прошел",
  textOkFirst: "Мы правильно получили оплату за ",
  textOkSecond:
    "Это номер подтверждения платежа. Не забудьте попросить счет на стойке регистрации.",
  textKo:
    "У нас возникла проблема с приемом платежа. Повторите попытку или подойдите к стойке регистрации, чтобы продолжить оплату с помощью нашего сотрудника.",
  continue: "Продолжить",
  continueWithoutPay: "Оплатить на стойке регистрации",
  tryAgain: "Попробовать снова",
  modal_info_text:
    "После завершения процесса регистрации заезда обратитесь к нашим сотрудникам, чтобы выполнить оплату с их помощью. Продолжайте онлайн-регистрацию.",
  modal_info_title: "Не беспокойтесь",
  service: "услуга",
  operation: "операция",
  receiptTitle: "Подтверждение платежа",
  receiptDate: "Дата:",
  receiptAuthorization: "Авторизация:",
  receiptOrder: "Заказ:",
  receiptQuantity: "Количество:",
  receiptMethod: "Способ платежа:",
  receiptLastDigits: "Последние цифры"
};
