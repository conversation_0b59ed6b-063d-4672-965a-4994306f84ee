export default {
	header: "Номер телефона",
	title: "Получайте информацию во время вашего пребывания",
	info: "Мы можем уведомлять вас о важных событиях, например о готовности вашего номера или о других важных событиях во время вашего пребывания.",
	alertSMS:
		"* Если вы согласитесь, {brandName} будет информировать вас по SMS о любом событии, которое может заинтересовать вас во время вашего пребывания. Например, о наличии номеров, однодневных экскурсиях, мероприятиях или событиях, происходящих во время вашего пребывания.",
	checkbox:
		"Вы хотите, чтобы мы продолжали отправлять вам информацию о наших новостях и после вашего пребывания?",
	keepPhone:
		"Я даю согласие на сохранение моего номера телефона в базе данных для повышения качества моего пребывания.",
	number: "Номер телефона",
	countrySelectorLabel: "Код страны",
	phoneNumberLabel: "Номер телефона",
	exampleLabel: "Пример:",
	phoneNumberFormat: "Код страны – Номер телефона",
	phoneNumberErrorMessage:
		"Нам не удалось проверить номер телефона. Пожалуйста, повторите попытку.",
	phoneCodeErrorTitle: "Введенный вами проверочный код неверен",
	phoneCodeErrorMessage:
		"Введите проверочный код, который мы отправили вам на телефон в SMS-сообщении. Пожалуйста, введите код еще раз или удалите/измените номер телефона.",
	phoneNumberMaximumAttemptsExceeded:
		"Превышено максимальное количество попыток. Мы перенаправим вас на следующий экран, чтобы продолжить процесс.",
	phoneCodeErrorButton: "Попробовать снова",
	codeTitle: "Пожалуйста, подтвердите код, который мы отправили вам в SMS.",
	codePhoneLabel: "Введите код",
	codeNotRecived: "Если вы не получили код через несколько минут,",
	tryAgain: "нажмите здесь, чтобы попробовать еще раз.",
	continue: "Продолжить",
};
