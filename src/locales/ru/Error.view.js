export default {
  header: "Упс... Ошибка",
  default: {
    header: "Упс... Ошибка",
    title: "Упс. Произошла внутренняя ошибка",
    message:
      "К сожалению, услуга онлайн-регистрации в настоящее время недоступна. <br> Повторите попытку позже или свяжитесь с нашими сотрудниками для получения дополнительной информации и помощи."
  },
  maxAttemptsReservationError: {
    title: "Достигнуто максимальное количество попыток.",
    message:
      "Вы достигли максимального количества попыток найти свое бронирование.<br>Пож<PERSON>луйста, обратитесь к нашим сотрудникам за дополнительной информацией и помощью.",
    buttons: { default: "Выйти из процесса" }
  },
  brandError: {
    title:
      "Упс. Произошла ошибка при поиске отеля",
    message:
      "К сожалению, услуга онлайн-регистрации в этом отеле в данный момент недоступна. <br> Повторите попытку позже или свяжитесь с нашими сотрудниками для получения дополнительной информации и помощи.",
    buttons: { default: "Попробовать снова" }
  },
  reservationError: {
    title: "Мы не обнаружили ожидающего процесса регистрации.",
    message:
      "Либо процесс РЕГИСТРАЦИИ уже завершен, либо мы не можем найти ни одного бронирования с этими данными.<br>Пожалуйста, повторите попытку позже или обратитесь к нашим сотрудникам за дополнительной информацией и помощью.",
    buttons: { default: "Попробовать снова" }
  },
  ageError: {
    title: "Введенный вами возраст неверен",
    message:
      "Возраст ребенка не соответствует данным в бронировании. Повторите попытку позже или обратитесь к нашим сотрудникам за дополнительной информацией и помощью",
    buttons: { default: "Зарегистрировать другого гостя" }
  },
  scanError: {
    buttons: { default: "Выйти из процесса" },
    title: "В процессе сканирования произошла ошибка",
    message:
      "Во время процесса сканирования произошла ошибка.<br>Повторите попытку позже или обратитесь к нашим сотрудникам за дополнительной информацией и помощью."
  },
  maxAttemptsScanError: {
    buttons: { default: "Выйти из процесса" },
    title: "Достигнуто максимальное количество попыток.",
    message:
      "Вы превысили лимит попыток сканирования документов, пытаясь сканировать слишком много раз.<br>Пожалуйста, обратитесь к нашим сотрудникам за дополнительной информацией и помощью."
  },
  maxAttemptsScanErrorWithCompleteData: {
    buttons: {
      exitProcess: "Выйти из процесса",
      completeData: "Продолжить ввод данных вручную"
    },
    title: "Достигнуто максимальное количество попыток.",
    message:
      "Вы превысили лимит попыток сканирования документов, пытаясь сканировать слишком много раз.<br>Пожалуйста, заполните данные для регистрации вручную или обратитесь к нашим сотрудникам за дополнительной информацией.<br>Напоминаем, что предоставление действительного документа, удостоверяющего личность, является обязательным для регистрации заезда.<br>Если вы не можете загрузить документ в данный момент, вы можете заполнить данные для регистрации заезда вручную, а затем предоставить документ по прибытии в отель. Что бы вы хотели сделать?"
  },
  error404: {
    title:
      "К сожалению, нам не удалось найти запрошенную информацию",
    message: "Вы можете закрыть вкладку, чтобы выйти.",
    buttons: { default: "Нажмите, чтобы вернуться к началу" },
    header: "Страница не найдена"
  },
  exitText: "Вы можете закрыть вкладку, чтобы выйти."
};
