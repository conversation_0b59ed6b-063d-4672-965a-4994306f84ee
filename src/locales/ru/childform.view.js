export default {
  header: "Данные ребенка",
  title: "Введите сведения о ребенке",
  nameInputTitle: "Имя",
  surnameInputTitle: "Личное имя",
  birthdayInputTitle: "Дата рождения",
  nationalityInputTitle: "Национальность",
  kinshipInputTitle: "Степень родства с владельцем бронирования",
  addressInputTitle: "Адрес",
  municipalityInputTitle: "Муниципалитет",
  emailInputTitle: "Электронная почта",
  telephoneInputTitle: "Телефон",
  info:
    "Мы заинтересованы знать эти данные наших несовершеннолетних гостей, чтобы предложить наилучшие услуги и быть готовыми к любым возможным непредвиденным обстоятельствам.",
  button: "Продолжить",
  modalMessage:
    "Любой человек старше {maxAgeToNotRequireID} должен предоставить свое удостоверение личности, чтобы зарегистрироваться в отеле {hotelName}. Мы подсчитали, что ребенку {возраст}, это правильно?",
  modalErrorMessage:
    "Возраст гостя старше возраста, необходимого для того, чтобы считаться {paxType}. Попробуйте еще раз.",
  goBack: "Возврат на предыдущий шаг",
  kinshipOptions: {
    son: "Сын",
    nephew: "Племянник",
    grandson: "Внук",
    brother: "Брат",
    cousin: "Двоюродный брат/сестра",
    other: "Другое"
  },
  postal_codeInputTitle: "Почтовый индекс",
};
