export default {
	header: "Сканирование документов",
	title: "Отсканируйте свое удостоверение личности",
	titleWithSignature: "Отсканируйте свое удостоверение личности с подписью",
	text: "Запомните эти советы и следуйте инструкциям, чтобы загрузить или отсканировать свое удостоверение личности.",
	textWithSignature: "По юридическим причинам у отеля {hotelName} должен иметься документ с вашей официальной подписью. Пожалуйста, отсканируйте часть документа, которая содержит вашу подпись.",
	modalSignatureRequiredTitle: "Ваша подпись отсутствует",
	modalSignatureRequiredText:"Ваша подпись не обнаружена. По юридическим причинам у отеля {hotelName} должен иметься документ с вашей официальной подписью. Пожалуйста, отсканируйте еще раз ту часть документа, которая содержит вашу подпись.",
	modalTipsTitle: "Полезные советы по сканированию документа.",
	scanInfo:
		"На лицевой стороне документа находится изображение вашего лица.",
	dontShake:
		"Держите руку как можно более устойчиво и наведите фокус камеры на документ.",
	goodFraming:
		"Убедитесь, что документ полностью входит в кадр. Сделайте снимок горизонтально.",
	dontFlash:
		"На документе не должно быть бликов, он не должен быть погнут или находиться в плохом состоянии.",
	scanButtonFront: "Загрузите фото лицевой стороны вашего удостоверения личности.",
	scanButtonBack: "Загрузите фото обратной стороны вашего удостоверения личности.",
	scanButtonFrontChild: "Загрузите фото лицевой стороны удостоверения личности вашего ребенка.",
	scanButtonBackChild: "Загрузите фото обратной стороны удостоверения личности вашего ребенка.",
	optionalScanMessage: "Не получилось загрузить документ? Нажмите здесь",
	modalOptionalTitle: "Важно!",
	modalOptionalMessage:
		"Напоминаем, что для регистрации заезда необходимо предоставить действительный документ, удостоверяющий личность.\nЕсли вы не можете загрузить документ в настоящее время, то вы можете заполнить данные для регистрации заезда вручную, а затем предоставить документ по прибытии в отель. Что бы вы хотели сделать?",
	modalOptionalNotScanButton: "Предоставить информацию вручную",
	modalOptionalScanNowButton: "Сканировать сейчас",
	modalSimilarGuestTitle: "Найдена похожая запись гостя",
	needExtraInfoTitle: "Введите данные из своего документа",
	needExtraInfoBody:
		"Нам требуется дополнительная информация о документе, который вы только что отсканировали",
	error: {
		undersizedImageError: "Изображение слишком маленькое. Попробуйте сделать снимок большего размера.",
		oversizedImageError: "Изображение слишком большое. Попробуйте сделать снимок меньшего размера.",
		passportFromSameCountry: "В соответствии с национальными правилами, для граждан страны паспорт не принимается в качестве действительного документа для регистрации. Пожалуйста, используйте свой национальный удостоверяющий документ.",
		noDataRequiredError:
			"Нам не удалось извлечь минимум необходимой информации из отсканированного документа. Проверьте, что это действительный документ (удостоверение личности или паспорт) и повторите попытку, или попробуйте другой документ.",
		similarGuestError:
			"Этот документ уже соответствует другому гостю при бронировании.\nКаждый гость должен иметь уникальное удостоверение личности.",
		similarGuestConfirm:
			"Даже если в данных есть ошибка, подтверждаете ли вы, что это один и тот же человек?",
		expiredDocument:
			"Срок действия предоставленного документа истек. Отсканируйте другой документ или обратитесь к нашим сотрудникам за дополнительной информацией и помощью.",
		schengenZoneError:
			"Этот документ, удостоверяющий личность, недействителен для идентификации вас в стране, где находится отель. Попробуйте отсканировать свой паспорт или подойдите к стойке регистрации.",
		documentNotDetectedError:
			"Отсканируйте действительный документ или, если вы считаете, что это была ошибка, повторно отсканируйте свое удостоверение личности, или пройдите на стойку регистрации для прохождения регистрации заезда.",
		invalidImageFormat:
			"Файл изображения имеет неверный формат. Изображение должно быть в формате PNG, JPG или JPEG.",
		genericError:
			"В процессе сканирования произошла ошибка. Повторите попытку позже или подойдите к стойке регистрации, чтобы зарегистрировать свой заезд вручную.",
		serviceNotAvailableWithOptionalScan:
			"К сожалению, услуга сканирования документов на данный момент недоступна. Повторите попытку позже Вы также можете заполнить данные о регистрации заезда вручную или подойти к стойке регистрации, чтобы зарегистрироваться лично.\nНапоминаем, что для регистрации заезда необходимо предоставить действительный документ, удостоверяющий личность.\nЕсли вы не можете загрузить документ в настоящее время, то вы можете заполнить данные для регистрации заезда вручную, а затем предоставить документ по прибытии в отель.",
		serviceNotAvailableWithoutOptionalScan:
			"К сожалению, услуга сканирования документов на данный момент недоступна. Повторите попытку позже или подойдите к стойке регистрации, чтобы зарегистрировать свой заезд лично.",
		drivingLicenseError:
			"Водительские права не принимаются для идентификации личности при размещении в данном отеле. Попробуйте отсканировать свой паспорт или иное действительное удостоверение личности, или же подойдите к стойке регистрации.",
		ageGuestError:
			"Отсканированный документ неверен, возраст гостя {comparison}  возраста, необходимого для того, чтобы считаться {paxType}. Пожалуйста, повторите попытку с действительным документом.",
	},
	legalText:
		"* Ваше удостоверение личности необходимо нам для документального учета всех наших гостей в соответствии с действующим законодательством о защите безопасности граждан (Ley Organica 4/2015).",
	older: "старше",
	younger: "моложе",
	addDataManually: "Предоставить информацию вручную",
};
