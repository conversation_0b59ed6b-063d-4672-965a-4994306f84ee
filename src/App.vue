<template>
  <router-view />
</template>
<script>
import { Analytics } from "aws-amplify"
import { mapState, mapActions, mapGetters } from "vuex"
import { TrackerProvider, Tracker } from "./tracker"
import { isEmpty } from "lodash";
import checkinAvailability from "./mixins/checkinAvailability";
import repository from "./repository/repositoryFactory";
import clearStoreAndRedirect from "./mixins/clearStoreAndRedirect";
const api = repository.get("integration");

export default {
  name: "app",
  data: () => {
    return {
     tracker: null,
     timer: null,
     timezone: "",
     allowTimeLimit: false,
    };
  },
  mixins: [checkinAvailability, clearStoreAndRedirect],

  computed:{
    ...mapState("brand", {brand_id: "brandId", config:"config"}),
    ...mapState("trace", ["parentBrand"]),
    ...mapState("reservations", ["reservationSelected", "dataRequested"]),
    ...mapGetters("app", ["isReceptionMode", "isDemoMode"]),
    ...mapGetters("guest", { selectedGuestData: "getSelectedGuest", getGuestIndex: "getGuestIndex" }),
    ...mapGetters("reservations", ["getReservationCompleted"]),
  },
  watch: {
    brand_id(newBrandId, oldBrandId) {
      if(newBrandId !== oldBrandId) {
        if(this.tracker) {
          this.tracker.setBrandId(newBrandId)
        } else if(newBrandId) {
          // Initialize tracker if it wasn't initialized before due to missing brand_id
          console.info("Brand ID became available, initializing tracker");
          this.initializeTracker();
        }
      }
    },
    selectedGuestData(newSelectedGuestData, oldSelectedGuestData) {
      const newGuestId = this.getGuestId(newSelectedGuestData);
      const oldGuestId = this.getGuestId(oldSelectedGuestData);

      if (newGuestId !== oldGuestId && this.tracker) {
        this.tracker.setGuestId(newGuestId)
      }
    },
    reservationSelected(newReservationSelectedData, oldReservationSelectedData) {
      if(newReservationSelectedData?.res_id !== oldReservationSelectedData?.res_id && this.tracker){
        this.tracker.setResId(newReservationSelectedData?.res_id ?? null);
      }

    }
  },
  methods:{
    ...mapActions("brand", ["SET_BRAND_ID"]),
    ...mapActions("app", ["TOGGLE_DEMO_MODE"]),
    getSourceValue(source) {
      return source ? "reception" : "autocheckin"
    },
    getGuestId(guest) {
      if (Object.keys(guest).length !== 0) {
        return guest?.pms_id ?? `HL_${this.reservationSelected?.res_id}|${this.getGuestIndex(guest)}`;
      }

      return null;
    },
    getReferrerHost(referrer) {
      try {
        return new URL(referrer).hostname
      } catch (e) {
        console.info("Error getting referrer host");
        return referrer;
      }
    },
    async initializeTracker() {
      // Prevent multiple initializations
      if (this.tracker || this.isDemoMode || !this.brand_id) {
        return;
      }

      try {
        const source = this.getSourceValue(this.isReceptionMode);
        const trackerParams = {
          brand_id: this.brand_id,
          guest_id: this.getGuestId(this.selectedGuestData),
          res_id: this.reservationSelected?.res_id ?? null,
          streamName: process.env.VUE_APP_ANALYTICS_STREAM_NAME,
          source: source
        };

        console.info("Initializing tracker with brand_id:", this.brand_id);
        this.tracker = new TrackerProvider(trackerParams);
        await this.tracker.init();
        await Analytics.addPluggable(this.tracker);

        // Set up auto tracking
        Analytics.autoTrack("pageView", {
          enable: true,
          eventName: "pageView",
          attributes: () => {
            return {
              name: this.$route?.name?.toLowerCase() ?? 'Unknown'
            }
          },
          type: "SPA",
          provider: "AnalyticsTracker",
          getUrl: () => {
            return window.location.origin + window.location.pathname;
          }
        });

        // Record initial event
        try {
          const searchParams = new URLSearchParams(window.location.search);
          const referrer = document.referrer ? this.getReferrerHost(document.referrer) : null;
          const params = Array.from(searchParams.keys()).reduce(
            (acc, val) => ({ ...acc, [val]: searchParams.get(val) }),
            {}
          );

          Tracker.recordCustomEvent("autocheckin_start", {
            referrer,
            params: !isEmpty(params) ? params : null,
            route: this.$route?.name
          });
        } catch(error) {
          console.error("Can't track autocheckin_start event", { error });
        }
      } catch (error) {
        console.error("Failed to initialize tracker:", error);
      }
    },
    async checkReservationIsStillEligible() {

      let searchData = this.dataRequested?.flatMap(inputs => inputs)
        .reduce((acc, {name, value}) => {
        if (value) acc[name] = value;
          return acc;
      }, {});

      // using this.reservationSelected is unreliable in this case. this.$store.state.reservations.reservationSelected reflects the store more accurately.
      if (this.$store.state.reservations.reservationSelected && !this.$store.getters["reservations/getReservationCompleted"]()) {

        this.timezone = this.$store.state.brand.timeZone;
        const timeLimitCheckin = this.config.time_limit_checkin;
        const closeTimeLimitCheckin = this.config.close_time_limit_checkin;

        let requestData = {
          reservation_code: this.reservationSelected.res_localizer,
          cache: !this.isReceptionMode,
          manual: false,
          ...(searchData?.check_in && {check_in: searchData.check_in})
        };

        try{
          const reservationResults = await api.getReservation(
            this.reservationSelected.brand_id,
            requestData,
            false
          );
          let isCheckinAvailable;

          if(reservationResults.length && reservationResults[0].check_in){
            isCheckinAvailable = this.isCheckinAvailable(
              reservationResults[0].check_in,
              timeLimitCheckin,
              closeTimeLimitCheckin,
              this.timezone
          );
          }

          if(!reservationResults.length || reservationResults.action || !isCheckinAvailable?.available){
            this.clearStoreAndRedirect(this.parentBrand);
          }
        } catch (error){
          this.clearStoreAndRedirect(this.parentBrand);
        }
      }
    }
  },
  created() {
    const brand_id = this.$route.params.brandId || null ;

    if(brand_id){
      this.SET_BRAND_ID(brand_id)
    }
  },
  async mounted() {
    // Try to initialize tracker if brand_id is available
    if(this.brand_id){
      console.info("Init tracker on mounted");
      await this.initializeTracker();
    } else {
      console.warn("No brand id found in the store. Tracker will be initialized when brand_id becomes available");
    }

    this.checkReservationIsStillEligible();

    // Every 20 minutes, check if the reservation is still eligible
    this.timer = setInterval(() => {
      this.checkReservationIsStillEligible();
    }, 20 * 60 * 1000);
  },

};

</script>
<style lang="scss">
html,
body {
  height: 100%;
  width: 100%;
  background: var(--bgColor);
}
.bg-color {
  height: 100%;
  width: 100%;
  background: var(--bgColor);
  position: fixed;
  z-index: -1;
}
.withFixedHeader {
  top: 72px;
}
</style>
