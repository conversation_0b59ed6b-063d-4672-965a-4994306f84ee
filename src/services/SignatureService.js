/**
 * Parse signature data to string with points and timestamp ; separed
 * Traces are joined with : and points with ;
 * Example:
 *  Given 2 traces [{x1,y1,time1}, {x2,y2,time2}] and [{x3,y3,time3}]
 *  It is reduced to x1#y1#time1;x2#y2#time2:x3#y3#time3
 *
 * @param {Array} signatureData
 * @returns string
 */
export function getSignatureAsTimeStampString(signatureData) {
  return signatureData
    .reduce((carry, trace) => {
      const points = trace?.points || [];
      return carry.concat(
        points
          .map(
            point => `${point.x.toFixed(0)}#${point.y.toFixed(0)}#${point.time}`
          )
          .join(";")
      );
    }, [])
    .join(":");
}
