export default {
  getLocalizerAmount: function(pendingCharges) {
    let amount = 0;
    pendingCharges.reservations?.map(reservation => {
      reservation.invoices?.map(invoice => {
        amount += invoice.total;
      });
    });
    return Number(parseFloat(amount).toFixed(2));
  },
  getReservationsInvoices: function(pendingCharges) {
    return pendingCharges.reservations?.map(reservation => {
      return {
        id: reservation.res_id,
        selected: false,
        invoices: reservation.invoices?.map(invoice => {
          return {
            ...invoice,
            amount: Number(parseFloat(invoice.total).toFixed(2))
          };
        })
      };
    });
  },
  getInvoicesCurrency: function(pendingCharges) {
    let result = false;
    //get currency of first invoice as value to check
    const firstInvoice = pendingCharges.reservations?.find(reservation => {
      return reservation.invoices && reservation.invoices?.length > 0;
    });
    if (firstInvoice) {
      const currencyCheck = firstInvoice.invoices[0].currency;
      pendingCharges.reservations?.forEach(reservation => {
        result = reservation.invoices?.every(invoice => {
          return invoice.currency === currencyCheck;
        });
        if (!result) return false;
      });
      if (result) {
        return currencyCheck;
      }
    }

    return false;
  }
};
