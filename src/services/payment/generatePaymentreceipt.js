import { jsPDF } from "jspdf";
import { i18n } from "@/locales";

export default function(brandName, order, amount, currency) {
  const doc = new jsPDF({
    format: "a6",
    unit: "mm",
    orientation: "p"
  });

  doc.text(brandName, 52, 12, { align: "center" });
  doc.setFontSize(10);
  doc.text(i18n.t("paymentresult.receiptTitle"), 52, 17, {
    align: "center"
  });
  doc.text(i18n.t("paymentresult.receiptDate"), 10, 35);
  doc.setFontSize(8);
  doc.text(`${order.transaction.created}`, 94, 35, {
    align: "right"
  });
  doc.setFontSize(10);
  doc.text(i18n.t("paymentresult.receiptAuthorization"), 10, 40);
  doc.setFontSize(8);
  doc.text(`${order.transaction.authorization}`, 94, 40, {
    align: "right"
  });
  doc.setFontSize(10);
  doc.text(i18n.t("paymentresult.receiptOrder"), 10, 45);
  doc.setFontSize(8);
  doc.text(`${order.id}`, 94, 45, {
    align: "right"
  });
  doc.setFontSize(10);
  doc.text(i18n.t("paymentresult.receiptQuantity"), 10, 55);
  doc.setFontSize(8);
  doc.text(`${amount} ${currency}`, 94, 55, {
    align: "right"
  });
  doc.setFontSize(10);
  doc.text(i18n.t("paymentresult.receiptMethod"), 10, 60);
  doc.setFontSize(8);
  doc.text(`${order.transaction.method}`, 94, 60, {
    align: "right"
  });
  doc.setFontSize(10);
  if (order.transaction.method === "CARD") {
    doc.text(i18n.t("paymentresult.receiptLastDigits"), 10, 65);
    doc.setFontSize(8);
    doc.text(`${order.transaction.cardLastNumbers}`, 94, 65, {
      align: "right"
    });
  }
  doc.text(`${order.transaction.bank}`, 52, 140, {
    align: "center"
  });
  doc.save("paymentReceipt.pdf");
}
