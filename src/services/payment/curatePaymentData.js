export default charges => {
  //removes unnecessary info
  const paymentData = {
    localizer: charges.localizer.id,
    reservations: []
  };
  //if localizer is paid get all invoices. Else get paid invoices
  charges.reservations.forEach(reservation => {
    if (charges.localizer.selected || reservation.selected) {
      paymentData.reservations.push(reservation);
    }
  });
  return paymentData;
};
