import { API } from "@aws-amplify/api";

import repository from "../../repository/repositoryFactory";
const api = repository.get("checkin");

export default async function(brandDocuments, lang, documentVariablesValues) {
  const brandDocumentsTranslations = [];
  const languagesIds = await getLanguagesIds(lang);
  const documentDefaultLanguageId = languagesIds.documentDefaultLanguageId;
  const documentLanguageId = languagesIds.documentLanguageId;
  const guestDocumentsPromises = brandDocuments.map(
    async document =>
      await api
        .getGuestDocument(document.id, lang, documentVariablesValues)
        .then(response => response.data)
        .catch(error => console.error("Checkin API error", { error }))
  );

  const guestDocuments = await Promise.all(guestDocumentsPromises);

  guestDocuments.forEach(document => {
    let documentTranslated = document["document_translations"].find(
      translation => translation.language_id === documentLanguageId
    );

    // If translation is not available in locale language search for default language translation
    if (!documentTranslated) {
      documentTranslated = document["document_translations"].find(
        translation => translation.language_id === documentDefaultLanguageId
      );
    }

    const checkboxConfigurations = document.checkbox_configurations?.map(config => {
      let checkboxTranslation = config.translations.find(translation => translation.language_id === documentLanguageId)
      if(!checkboxTranslation) {
      checkboxTranslation = config.translations.find(translation => translation.language_id === documentDefaultLanguageId)
      }
      return {
        type: config.checkbox_type,
        text: checkboxTranslation.text,
      }
    }) || []

    const documentData = {
      id: document.id,
      title: documentTranslated.title,
      content: documentTranslated.content,
      extension: document.extension,
      checkbox_configurations: checkboxConfigurations
    };

    brandDocumentsTranslations.push(documentData);
  });

  return brandDocumentsTranslations;
}

async function getLanguagesIds(lang) {
  const defaultDocumentLanguageName = "en";
  try {
    const languagesData = await API.get("hotelinking", "/languages", {});

    const defaultLanguageData = languagesData.find(
      language => language.name === defaultDocumentLanguageName
    );

    const documentLanguageData = languagesData.find(
      language => language.name === lang
    );

    return {
      documentDefaultLanguageId: defaultLanguageData.id,
      documentLanguageId: documentLanguageData.id
    };
  } catch (error) {
    return error;
  }
}
