import { SpanishDocumentsValidator } from "./validators/SpanishDocumentsValidator";

const classes = {
  ESP: SpanishDocumentsValidator
};

export default function(documentType, country, documentNumber) {
  const valid = Object.keys(classes).includes(country)
    ? new classes[country](documentType, documentNumber).validate()
    : true;

  if (!valid) {
    console.error("Invalid document number detected", {
      Nationality: country,
      "Document type": documentType,
      "Document Number": documentNumber
    });
  }

  return valid;
}
