import { BasicValidator } from "./BasicValidator";

export class SpanishDocumentsValidator extends BasicValidator {
  validateIdentityCard(documentNumber) {
    // Calculation of the check digit of the Spanish NIF/NIE: http://www.interior.gob.es/web/servicios-al-ciudadano/dni/calculo-del-digito-de-control-del-nif-nie
    const spanishDniRegex = /^[XYZ]?\d{5,8}[-]?[A-Z]$/;
    const dni = documentNumber.toUpperCase();

    if (spanishDniRegex.test(dni) === true) {
      const letter = dni.slice(-1);
      const substitutes = { X: 0, Y: 1, Z: 2, "-": "" };
      const number = dni
        .substr(0, dni.length - 1)
        .replace(/[XYZ-]/g, m => substitutes[m]);

      const letters = [
        "T",
        "R",
        "W",
        "A",
        "G",
        "M",
        "Y",
        "F",
        "P",
        "D",
        "X",
        "B",
        "N",
        "J",
        "Z",
        "S",
        "Q",
        "V",
        "H",
        "L",
        "C",
        "K",
        "E"
      ];
      const position = number % 23;

      return letter === letters[position];
    }

    return false;
  }
}
