export class BasicValidator {
  constructor(documentType, value) {
    this.documentType = documentType;
    this.value = value;
    this.functionMapping = {
      identity_card: "validateIdentityCard",
      passport: "validatePassport"
    };
  }

  validate() {
    // Call dynamic function by document type parameter
    return this.functionMapping[this.documentType]
      ? this[this.functionMapping[this.documentType]](this.value)
      : true;
  }

  validateIdentityCard() {
    return true;
  }

  validatePassport() {
    return true;
  }
}
