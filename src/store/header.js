export default {
  namespaced: true,
  state: {
    fixed: false
  },
  actions: {
    CLEAR_STATE: context => {
      context.commit("clearState");
    },
    TOGGLE_HEADER_FIXED(context, toggle) {
      context.commit("toggleHeader", toggle);
    }
  },
  mutations: {
    clearState: state => {
      state.fixed = false;
      state.exitOptIn = false;
    },
    toggleHeader(state, toggle) {
      state.fixed = toggle;
    }
  }
};
