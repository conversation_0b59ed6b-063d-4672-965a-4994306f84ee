const getDefaultState = () => {
  return {
    type: "error",
    show: false,
    title: "",
    name: "default",
    buttonMessage: ""
  };
};

export default {
  namespaced: true,
  state: getDefaultState(),
  actions: {
    VISIBLE: (context, visible) => {
      context.commit("visible", visible);
    },
    SET_TITLE: (context, title) => {
      context.commit("setTitle", title);
    },
    SET_NAME: (context, name) => {
      context.commit("setName", name);
    },
    SET_BUTTON_MESSAGE: (context, buttonMessage) => {
      context.commit("setButtonMessage", buttonMessage);
    },
    SET_TYPE: (context, type) => {
      context.commit("setType", type);
    },
    CLEAR_STATE: context => {
      context.commit("clearState");
    }
  },
  mutations: {
    visible: (state, visible) => {
      state.show = visible;
    },
    setTitle: (state, title) => {
      state.title = title;
    },
    setType: (state, type) => {
      state.type = type;
    },
    setName: (state, name) => {
      state.name = name;
    },
    setButtonMessage: (state, buttonMessage) => {
      state.buttonMessage = buttonMessage;
    },
    clearState: state => {
      Object.assign(state, getDefaultState());
    }
  }
};
