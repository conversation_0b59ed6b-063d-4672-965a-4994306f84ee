const getDefaultState = () => {
  return {
    data: {},
    attempt: 0,
    attemptCode: 0,
    validatedPhones: []
  };
};
export default {
  namespaced: true,
  state: getDefaultState(),
  actions: {
    SET_DATA: (context, data) => {
      context.commit("setData", data);
    },

    ADD_ATTEMPT: context => {
      context.commit("addAttempt");
    },

    ADD_ATTEMPT_CODE: context => {
      context.commit("addAttemptCode");
    },
    CLEAR_STATE: context => {
      context.commit("clearState");
    },
    ADD_VALIDATED_PHONE: (context, data) =>{
      context.commit("addValidatedPhone", data)
    }
  },
  mutations: {
    setData: (state, data) => {
      state.data = data;
    },

    addAttempt: state => {
      state.attempt += 1;
    },

    addAttemptCode: state => {
      state.attemptCode += 1;
    },

    clearState: state => {
      Object.assign(state, getDefaultState());
    },

    addValidatedPhone: (state, data) => {
      state.validatedPhones = [...state.validatedPhones, data]
    }
  }
};
