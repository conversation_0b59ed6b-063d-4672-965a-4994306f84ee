const getDefaultState = () => {
  return {
    data: {}
  };
};
export default {
  namespaced: true,
  state: getDefaultState(),
  actions: {
    SET_DATA: (context, queryParams) => {
      context.commit("setData", queryParams);
    },

    REMOVE_QUERY_PARAM: (context, key) => {
      context.commit("removeQueryParam", key);
    },
    CLEAR_STATE: context => {
      context.commit("clearState");
    }
  },
  mutations: {
    setData: (state, queryParams) => {
      state.data = { ...state.data, ...queryParams };
    },

    removeQueryParam: (state, key) => {
      delete state.data[key];
    },

    clearState: state => {
      Object.assign(state, getDefaultState());
    }
  }
};
