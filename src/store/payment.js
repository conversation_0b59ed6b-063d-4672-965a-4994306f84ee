export default {
  namespaced: true,
  state: {
    integrations: [],
    charges: {},
    pendingCharges: [],
    order: {},
    platformMethodAndPaymentData: {}
  },
  actions: {
    SET_PAYMENT_INTEGRATIONS: (context, integrations) => {
      context.commit("set_payment_integrations", integrations);
    },
    SET_PENDING_CHARGES: (context, charges) => {
      context.commit("set_pending_charges", charges);
    },
    SET_CHARGES_RESPONSE: (context, data) => {
      context.commit("set_charges_response", data);
    },
    SET_ORDER: (context, order) => {
      context.commit("set_order", order);
    },
    SET_PLATFORM_METHOD_AND_PAYMENT_DATA: (context, data) => {
      context.commit("set_platform_method_and_payment_data", data);
    },
    CLEAR_STATE: context => {
      context.commit("clearState");
    }
  },
  mutations: {
    set_platform_method_and_payment_data: (state, data) => {
      state.platformMethodAndPaymentData = data;
    },
    set_payment_integrations: (state, integrations) => {
      state.integrations = integrations;
    },
    set_pending_charges: (state, charges) => {
      state.charges = charges;
    },
    set_charges_response: (state, data) => {
      state.pendingCharges = data;
    },
    set_order: (state, order) => {
      state.order = order;
    },
    clearState: state => {
      state.integrations = [];
      state.charges = {};
      state.pendingCharges = [];
      state.order = {};
      state.platformMethodAndPaymentData = {};
    }
  }
};
