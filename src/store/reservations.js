const getDefaultState = () => {
	return {
		data: null,
		reservationSelected: null,
		inputsForm: [],
		dataRequested: null,
		hasMoreReservations: false,
		attempts: 0,
		filterAttempts: 0,
		childAgeAttempts: 0,
		numberGuestSession: 0,
		reservationCheckinComplete: false,
		multipleRooms: false,
		checkState: null,
		error: null,
	};
};

export default {
	namespaced: true,
	state: getDefaultState(),
	getters: {
		//This method only use when know the inputsForm in a reservation_filter input
		getInputFilterForm: (state) => state.inputsForm[0][0],
		getReservationCompleted: (state) => (forceReservationSelected = false) => {
			const data =
				state.multipleRooms && !forceReservationSelected
					? state.data
					: state.reservationSelected
					? [state.reservationSelected]
					: null;

			if (data) {
				const totalGuests = data.reduce(
					(accum, item) =>
						accum +
						(parseInt(item.res_adults) || 0) +
						(parseInt(item?.res_children) || 0) +
						(parseInt(item?.res_juniors) || 0) +
						(parseInt(item?.res_babies) || 0),
					0,
				);
				const totalGuestsCompleted = data.reduce(
					(accum, item) =>
						accum + item.guests.filter((guest) => guest.validated).length,
					0,
				);
				return totalGuests === totalGuestsCompleted + state.numberGuestSession;
			}
			return false;
		},
		getError: (state) => state.error,
		getValidatedGuests(state) {
			const data = state.multipleRooms
				? state.data
				: [state.reservationSelected];

			return data.reduce(function (filtered, option) {
				filtered.push(option.guests.filter((guest) => guest.validated));
				return filtered.flat();
			}, []);
		},
		getTotalGuests: (state) => {
      const data = state.multipleRooms ? state.data : [state.reservationSelected];
      
			return data.reduce((totalGuests, reservation) => {
        const guests = reservation?.guests?.length ?? 0;
        return totalGuests + guests;
      }, 0);
    },
		getSelectedLocalizer(state) {
			return state.reservationSelected?.res_localizer;
		},
	},
	actions: {
		SET_CHECK_STATE: (context, checkState) => {
			context.commit("setCheckState", checkState);
		},
		SET_ERROR: (context, error) => {
			context.commit("setError", error);
		},
		SET_RESERVATIONS: (context, reservations) => {
			context.commit("setReservations", reservations);
		},
		SET_RESERVATION_SELECTED: (context, reservation) => {
			context.commit("setReservationSelected", reservation);
		},
		SET_INPUTS_FORM: (context, inputs) => {
			context.commit("setInputsForm", inputs);
		},
		SET_DATA_REQUESTED: (context, data) => {
			context.commit("setDataRequested", data);
		},
		SET_HAS_MORE_RESERVATIONS: (context, flag) => {
			context.commit("setHasMoreReservations", flag);
		},
		SET_MULTIPLE_ROOMS: (context, flag) => {
			context.commit("setMultipleRooms", flag);
		},
		ADD_SESSION_GUEST: (context) => {
			context.commit("addSessionGuest");
		},
		RESET_SESSION_GUESTS: (context) => {
			context.commit("resetSessionGuests");
		},
		ADD_ATTEMPT: (context) => {
			context.commit("addAttemp");
		},
		ADD_FILTER_ATTEMPT: (context) => {
			context.commit("filterAttemp", 1);
		},
		REMOVE_FILTER_ATTEMPT: (context) => {
			context.commit("filterAttemp", -1);
		},
		CLEAR_INPUTS_FORM: (context) => {
			context.commit("clearInputsForm");
		},
		SET_RESERVATION_CHECKIN_COMPLETE: (context, reservationCheckinComplete) => {
			context.commit(
				"setReservationCheckinComplete",
				reservationCheckinComplete,
			);
		},
		CLEAR_STATE: (context) => {
			context.commit("clearState");
		},
	},
	mutations: {
		setCheckState: (state, checkState) => {
			state.checkState = checkState;
		},
		setReservations: (state, reservations) => {
			state.data = reservations;
		},
		setReservationSelected: (state, reservation) => {
			state.reservationSelected = reservation;
		},
		setInputsForm: (state, inputs) => {
			state.inputsForm = inputs;
		},
		setDataRequested: (state, data) => {
			state.dataRequested = data;
		},
		setHasMoreReservations: (state, flag) => {
			state.hasMoreReservations = flag;
		},
		setMultipleRooms: (state, flag) => {
			state.multipleRooms = flag;
		},
		addAttemp: (state) => {
			state.attempts += 1;
		},
		filterAttemp: (state, number) => {
			state.filterAttempts += number;
		},
		clearInputsForm(state) {
			this._vm.$set(
				state.inputsForm,
				state.inputsForm.map((inputs) =>
					inputs.map((input) => {
						input.value = "";
						input.hasError = true;
						return input;
					}),
				),
			);
		},
		addChildAgeAttempt: (state) => {
			state.childAgeAttempts++;
		},
		addSessionGuest: (state) => {
			state.numberGuestSession++;
		},
		resetSessionGuests: (state) => {
			state.numberGuestSession = 0;
		},
		resetChildAgeAttempts: (state) => {
			state.childAgeAttempts = 0;
		},
		setReservationCheckinComplete: (state, reservationCheckinComplete) => {
			state.reservationCheckinComplete = reservationCheckinComplete;
		},
		clearState(state) {
			Object.assign(state, getDefaultState());
		},
		setError(state, error) {
			state.error = error ? error : null;
		},
	},
};
