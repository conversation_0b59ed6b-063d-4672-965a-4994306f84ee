export default {
  namespaced: true,
  state: {
    retryCount: 0
  },
  actions: {
    ADD_TRY: ({ commit }) => {
      commit("addTry");
    },
    CLEAR_TRY: ({ commit }) => {
      commit("clearTry");
    },
    CLEAR_STATE: context => {
      context.commit("clearState");
    }
  },
  mutations: {
    addTry(state) {
      state.retryCount++;
    },
    clearTry(state) {
      state.retryCount = 0;
    },
    clearState(state) {
      state.retryCount = 0;
    }
  }
};
