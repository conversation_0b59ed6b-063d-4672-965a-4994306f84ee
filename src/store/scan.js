const getDefaultState = () => {
	return {
		currentAttemptsDocumentScan: 0,
		identityDocuments: {},
		face: null,
		scannedWithSignature: false,
		backPartScanned: false,
		signatureUploadRequired: false,
	};
};

export default {
	namespaced: true,
	state: getDefaultState(),
	getters: {
		getIdentityDocumentByName: (context) => (name) => {
			return context.identityDocuments[name] || null;
		},
		isDNI: (context) => {
			return (
				"identity_card_front" in context.identityDocuments ||
				"identity_card_back" in context.identityDocuments
			);
		},
		isResidencePermit: (context) => {
			return (
				"residence_permit_front" in context.identityDocuments ||
				"residence_permit_back" in context.identityDocuments
			);
		},
		getRequiredDocuments: (context) => (config) => {
			const hasFrontAndBack =
				("identity_card_front" in context.identityDocuments &&
					"identity_card_back" in context.identityDocuments) ||
				("residence_permit_front" in context.identityDocuments &&
					"residence_permit_back" in context.identityDocuments);

			const hasPassport = "passport" in context.identityDocuments;
			const hasDrivingLicense = "driving_license" in context.identityDocuments;

			if (config.identity_document_signature_required) {
				return (
					(hasFrontAndBack || hasPassport || hasDrivingLicense) &&
					context.scannedWithSignature
				);
			} else {
				return hasFrontAndBack || hasPassport || hasDrivingLicense;
			}
		},
		getScanData: (context) => (state) => {
			const isAdvancedScan = false || state;

			const mergedData = {};
			const identityDocuments = Object.values(context.identityDocuments);

			if (isAdvancedScan) {
				identityDocuments.reverse();
				// if first document in array is "signature", move it to the end, to avoid setting document type value of scanData as "signature"
				if (identityDocuments[0].title === "signature") {
					const signatureDoc = identityDocuments.shift();
					identityDocuments.push(signatureDoc);
				}
			}

			identityDocuments.forEach((document) => {
				Object.entries(document?.data).forEach((entry) => {
					const [key, value] = entry;
					if (typeof value === "object" && value) {
						Object.entries(value).forEach((objEntry) => {
							if (!mergedData[key]) {
								mergedData[key] = value;
							}
							const [objKey, objValue] = objEntry;
							if (!mergedData[key][objKey]) {
								mergedData[key] = { ...mergedData[key], [objKey]: objValue };
							}
						});
					}

					if (!mergedData[key]) {
						mergedData[key] = value;
					}
				});
			});
			return { ...mergedData };
		},
		getAllDocuments: (context) => {
			const documents = [];

			Object.values(context.identityDocuments).forEach((document) => {
				const { title, content, extension } = document;
				documents.push({
					title,
					content,
					extension,
				});
			});
			return documents;
		},
	},
	actions: {
		ADD_SCAN_DOCUMENT_ATTEMPT: (context) => {
			context.commit("addAttemptDocumentScan");
		},
		ADD_IDENTITY_DOCUMENT: (context, document) => {
			context.commit("addIdentityDocument", document);
		},
		ADD_FACE_IMAGE: (context, image) => {
			context.commit("addFaceImage", image);
		},
		CLEAR_SCAN_DOCUMENT_ATTEMPT: (context) => {
			context.commit("clearAttemptDocumentScan");
		},
		CLEAR_STATE: (context) => {
			context.commit("clearState");
		},
		CLEAR_SCAN_DATA: (context) => {
			context.commit("clearScanData");
		},
		CLEAR_DOCUMENT: (context, title) => {
			context.commit("clearDocument", title);
		},
		CLEAR_ALL_DOCUMENTS: (context) => {
			context.commit("clearAllDocuments");
		},
		SET_SCANNED_WITH_SIGNATURE: (context, value) => {
			context.commit("setScannedWithSignature", value);
		},
		SET_SIGNATURE_UPLOAD_REQUIRED(context, value) {
			context.commit("setSignatureUploadRequired", value);
		},
		SET_BACK_PART_SCANNED: (context, value) => {
			context.commit("setBackPartScanned", value);
		},
	},
	mutations: {
		addAttemptDocumentScan: (state) => {
			state.currentAttemptsDocumentScan += 1;
		},
		addFaceImage: (state, image) => {
			state.face = image;
		},
		addIdentityDocument: (state, { document }) => {
			// If user has inserted a passport, driving license or residence permit and inserts an id, remove previous one and start id process
			if (
				("passport" in state.identityDocuments ||
					"driving_license" in state.identityDocuments ||
					"residence_permit_front" in state.identityDocuments ||
					"residence_permit_back" in state.identityDocuments) &&
				(document.title === "identity_card_front" ||
					document.title === "identity_card_back")
			) {
				state.identityDocuments = clearDocument(state, "passport");
				state.identityDocuments = clearDocument(state, "driving_license");
				state.identityDocuments = clearDocument(
					state,
					"residence_permit_front",
				);
				state.identityDocuments = clearDocument(state, "residence_permit_back");
			}
			// If user has inserted an id, driving license, or residence permit and inserts a passport, remove previous one and start passport process
			if (
				("identity_card_front" in state.identityDocuments ||
					"identity_card_back" in state.identityDocuments ||
					"driving_license" in state.identityDocuments ||
					"residence_permit_front" in state.identityDocuments ||
					"residence_permit_back" in state.identityDocuments) &&
				document.title === "passport"
			) {
				state.identityDocuments = clearDocument(state, "identity_card_front");
				state.identityDocuments = clearDocument(state, "identity_card_back");
				state.identityDocuments = clearDocument(state, "driving_license");
				state.identityDocuments = clearDocument(
					state,
					"residence_permit_front",
				);
				state.identityDocuments = clearDocument(state, "residence_permit_back");
			}

			// If user has inserted an id, passport or residence permit and inserts a driving license, remove previous one and start driving license process
			if (
				("identity_card_front" in state.identityDocuments ||
					"identity_card_back" in state.identityDocuments ||
					"passport" in state.identityDocuments ||
					"residence_permit_front" in state.identityDocuments ||
					"residence_permit_back" in state.identityDocuments) &&
				document.title === "driving_license"
			) {
				state.identityDocuments = clearDocument(state, "identity_card_front");
				state.identityDocuments = clearDocument(state, "identity_card_back");
				state.identityDocuments = clearDocument(state, "passport");
				state.identityDocuments = clearDocument(
					state,
					"residence_permit_front",
				);
				state.identityDocuments = clearDocument(state, "residence_permit_back");
			}

			// If user has inserted an id, passport or driving license and inserts a residence permit, remove previous one and start residence permit process
			if (
				("identity_card_front" in state.identityDocuments ||
					"identity_card_back" in state.identityDocuments ||
					"passport" in state.identityDocuments ||
					"driving_license" in state.identityDocuments) &&
				(document.title === "residence_permit_front" ||
					document.title === "residence_permit_back")
			) {
				state.identityDocuments = clearDocument(state, "identity_card_front");
				state.identityDocuments = clearDocument(state, "identity_card_back");
				state.identityDocuments = clearDocument(state, "passport");
				state.identityDocuments = clearDocument(state, "driving_license");

				state.signatureUploadRequired = false;
			}

			state.identityDocuments = {
				...state.identityDocuments,
				[document.title]: { ...document },
			};
		},

		clearAttemptDocumentScan: (state) => {
			state.currentAttemptsDocumentScan = 0;
		},

		clearState: (state) => {
			Object.assign(state, getDefaultState());
		},

		clearScanData: (state) => {
			state.data = null;
		},
		clearDocument: (state, title) => {
			state.identityDocuments = clearDocument(state, title);
		},
		clearAllDocuments: (state) => {
			state.identiyDocuments = {};
		},
		setScannedWithSignature(state, value) {
			state.scannedWithSignature = value;
		},
		setBackPartScanned(state, value) {
			state.backPartScanned = value;
		},
		setSignatureUploadRequired(state, value) {
			state.signatureUploadRequired = value;
		},
	},
};

function clearDocument(state, title) {
	const documents = { ...state.identityDocuments };
	delete documents[title];

	state.scannedWithSignature = false;
	// If a document is cleared, change signatureUploadRequired to false because guest might scan a document with signature. However, if the document being eliminated is the signature one, keep the signatureUploadRequired to true.
	if (title !== "signature") {
		state.signatureUploadRequired = false;
	}
	if (title === "identity_card_back" || title === "residence_permit_back") {
		state.backPartScanned = false;
	}

	return { ...documents };
}
