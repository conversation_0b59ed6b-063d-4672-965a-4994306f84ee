export default {
  namespaced: true,
  state: {
    text: undefined,
    modal: undefined,
    accepted: false,
    lang: undefined
  },
  actions: {
    CLEAR_STATE: context => {
      context.commit("clearState");
    },
    SET_GDPR_TEXT: (context, text) => {
      context.commit("setGdprText", text);
    },
    SET_GDPR_MODAL: (context, text) => {
      context.commit("setGdprModal", text);
    },
    SET_GDPR_ACCEPTED: (context, boolean) => {
      context.commit("setGdprAccepted", boolean);
    },
    SET_GDPR_LANG: (context, lang) => {
      context.commit("setGdprLang", lang);
    }
  },
  mutations: {
    clearState: state => {
      state.text = undefined;
      state.modal = undefined;
      state.accepted = false;
      state.lang = "";
    },
    setGdprText: (state, text) => {
      state.text = text;
    },
    setGdprModal: (state, text) => {
      state.modal = text;
    },
    setGdprAccepted: (state, boolean) => {
      state.accepted = boolean;
    },
    setGdprLang: (state, lang) => {
      state.lang = lang;
    }
  }
};
