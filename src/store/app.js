import receptionUtils from "../utils/receptionUtils";
const getDefaultState = () => {
	return {
		isReceptionMode: receptionUtils.setReceptionMode() || false,
		authorizedBrands: [],
		appLanguage: undefined,
		previousComponent: "",
		isDemoMode: false,
	};
};

export default {
	namespaced: true,
	state: getDefaultState(),
	actions: {
		CLEAR_STATE: (context) => {
			context.commit("clearState");
		},
		SET_AUTHORIZED_BRANDS: (context, brands) => {
			context.commit("setAuthorizedBrands", brands);
		},
		SET_APP_LANGUAGE: (context, language) => {
			context.commit("setAppLanguage", language);
		},
		SET_PREVIOUS_COMPONENT: (context, componentName) => {
			context.commit("setPreviousComponent", componentName);
		},
		TOGGLE_DEMO_MODE: (context, switchValue) => {
			context.commit("toggleDemoMode", switchValue);
		},
	},
	mutations: {
		clearState: (state) => {
			Object.assign(state, {
				isReceptionMode: receptionUtils.setReceptionMode() || false,
				appLanguage: undefined,
				previousComponent: "",
			});
		},
		setAuthorizedBrands: (state, brands) => {
			state.authorizedBrands = brands;
		},
		setAppLanguage: (state, language) => {
			state.appLanguage = language;
		},
		setPreviousComponent: (state, componentName) => {
			state.previousComponent = componentName;
		},
		toggleDemoMode: (state, switchValue) => {
			state.isDemoMode = switchValue;
		},
	},
	getters: {
		isReceptionMode: (state) => state.isReceptionMode,
		getPreviousComponent: (state) => state.previousComponent,
		isDemoMode: (state) => state.isDemoMode,
	},
};
