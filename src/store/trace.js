const getDefaultState = () => {
	return {
		id: "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
			const r = (Math.random() * 16) | 0;
			const v = c === "x" ? r : (r & 0x3) | 0x8;
			return v.toString(16);
		}),
		bouncerUuid: null,
		parentBrand: null,
	};
};

export default {
	namespaced: true,
	state: getDefaultState(),
	actions: {
		SET_BOUNCER_UUID: (context, uuid) => {
			context.commit("setBouncerUuid", uuid);
		},
		SET_PARENT_BRAND_ID: (context, brandId) => {
			context.commit("setParentBrandId", brandId);
		},
	},
	mutations: {
		clearState: () => {
			// Do nothing, we never want to delete this state
		},
		setBouncerUuid: (state, uuid) => {
			state.bouncerUuid = uuid;
		},
		setParentBrandId: (state, brandId) => {
			state.parentBrand = brandId;
		},
	},
};
