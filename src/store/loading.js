export default {
  namespaced: true,
  state: {
    loading: false,
    spinner: false
  },
  actions: {
    LOADING: (context, status) => {
      context.commit("loading", status);
    },
    CLEAR_STATE: context => {
      context.commit("clearState");
    }
  },
  mutations: {
    loading: (state, status) => {
      state.loading = status;
      setTimeout(() => {
        state.spinner = status;
      }, 200);
    },
    clearState: state => {
      state.loading = false;
      state.spinner = false;
    }
  }
};
