import Vue from "vue";
import Vuex from "vuex";
import brand from "./brand";
import loading from "./loading";
import device from "./device";
import reservations from "./reservations";
import scan from "./scan";
import guest from "./guest";
import phone from "./phone";
import modal from "./modal";
import app from "./app";
import header from "./header";
import queryParams from "./queryParams";
import gdpr from "./gdpr";
import payment from "./payment";
import retryCount from "./retryCount";
import trace from "./trace";
import createPersistedState from "vuex-persistedstate";
import createPersistedStateIDB from "vuex-persist-indexeddb";

Vue.use(Vuex);

export default new Vuex.Store({
	plugins: [
		createPersistedState({
			key: "hlCheckin",
			paths: [
				"reservations",
				"brand",
				"guest",
				"phone",
				"app",
				"retryCount",
				"gdpr",
				"payment",
				"queryParams",
			],
		}),
		createPersistedState({
			storage: window.sessionStorage,
			key: "hlCheckin",
			paths: ["trace"],
		}),
		createPersistedStateIDB({
			key: "hlCheckin",
			paths: ["scan"],
		}),
	],
	state: {},
	mutations: {},
	actions: {},
	modules: {
		app,
		brand,
		loading,
		device,
		reservations,
		scan,
		guest,
		phone,
		modal,
		header,
		queryParams,
		gdpr,
		payment,
		retryCount,
		trace,
	},
});
