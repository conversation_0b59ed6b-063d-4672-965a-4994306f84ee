/* ./src/index.scss */
@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

$animationDuration: 0.5s;
@import "../../../node_modules/vue2-animate/src/sass/vue2-animate";
@import "../../../node_modules/@splidejs/splide/dist/css/splide.min.css";

select {
  background-repeat: no-repeat;
  background-position: 100%;
  -webkit-appearance: none;
}

.main-content {
  @apply flex flex-1 rounded-t-2xl;
}

.main-content-white {
  @apply bg-white flex-col;
}

.content {
  @apply px-8 pt-6 pb-8 sm:px-20 md:px-40 lg:px-64;
}
.content {
  @media only screen and (min-width: 1280px) {
    padding-left: 20rem;
    padding-right: 20rem;
  }

  @media only screen and (min-width: 1440px) {
    padding-left: 25rem;
    padding-right: 25rem;
  }

  @media only screen and (min-width: 1920px) {
    padding-left: 35rem;
    padding-right: 35rem;
  }
}

.go-back-button {
  @apply text-center uppercase text-xs text-red-500 hover:text-red-900 cursor-pointer pt-4  outline-none;
}

.slider-content {
  @apply px-8 sm:px-20 md:px-40 lg:px-64 xl:px-96;
}
.subheader,
.actual-process-name {
  @apply px-8 sm:px-20 md:px-40 lg:px-64 xl:px-96;
}

.degrade {
  background: rgb(255, 255, 255);
  background: linear-gradient(
    20deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(233, 233, 233, 1) 100%
  );
}
.icon-wrapper {
  min-width: 64px;

  & svg {
    margin: auto;
  }
}
.status-item {
  @extend .icon-wrapper;
  min-height: 64px;
}
.input-transition {
  & .bounce-leave-from,
  .bounce-leave-to {
    animation: opacity 0;
  }

  & .bounce-enter-active {
    animation: bounce-in 0.5s;
  }
  & .bounce-leave-active {
    opacity: 0;
  }
  @keyframes bounce-in {
    0% {
      transform: scale(0);
    }
    50% {
      transform: scale(1.25);
    }
    100% {
      transform: scale(1);
    }
  }
}
.fi {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  position: relative;
  display: inline-block;
  width: 1.33333333em;
  line-height: 1em;
}