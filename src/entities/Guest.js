import { generateUuidv4 } from "@/utils/stringUtils";
export default class Guest {
	constructor(data) {
		this.uuid = data.uuid || generateUuidv4();
		this.pms_id = data.pms_id || null;
		this.name = data.name || data.first_name || null;
		this.surname = data.surname || data.last_name || null;
		this.second_surname = data.second_surname || null;
		this.full_name = this.getFullName(data);
		this.gender = data.gender || null;
		this.birthday_date = data.birthday_date || data.birthday || null;
		this.email = data.email || null;
		this.telephone = data.telephone || null;
		this.holder = data.holder || false;
		this.lang = data.lang || null;
		this.document_type = data.document_type || null;
		this.document_number = data.document_number || data.document_id || null;
		this.date_of_issue = data.date_of_issue || data.document_date_issue || null;
		this.date_of_expiry =
			data.date_of_expiry || data.document_date_expiry || null;
		this.document_support_number = data.document_support_number || null;
		this.address = this.getAddress(data) || null;
		this.nationality = data.nationality || null;
		this.fiscal_code = data.fiscal_code || null;
		this.position = data.position || null;
		this.residence_country =
			data.residence_country || data.issuing_country || null;
		this.validated = data.validated || false;
		this.processCompleted = data.processCompleted || data.validated || false;
		this.pax_type = data.pax_type || null;
		this.comment = data.comment || {};
		this.signature = data.signature || null;
		this.documentSignature = data.documentSignature || null;
		this.timestampSignature = data.timestampSignature || null;
		this.documentsSaved = data.documentsSaved || null;
		this.documents = data.documents || null;
		this.identityDocumentsSaved = data.identityDocumentsSaved || null;
		this.selected = data.selected || false;
		this.signedDocuments = data.signedDocuments || false;
		this.kinship = data.kinship || null;
		this.checkboxStates = data.checkboxStates || null
		this.sendDocuments = data.sendDocuments ?? true; // use ?? because we want to allow false to be sent
	}

	getAddress(data) {
		return {
			street: this.getFullStreetAddress(data) || null,
			street_number:
				data.address?.street_number || data.address?.house_number || null,
			postal_code:
				data.address?.postal_code ||
				data.address?.postcode ||
				data.postal_code ||
				null,
			province: data.address?.province || data.province || null,
			city: data.address?.city || data.city || null,
			CCAA: data.address?.CCAA || data.address?.state || null,
			region: data.address?.region,
			subregion: data.address?.subregion,
			country: data?.residence_country || data.address?.issuing_country || null,
		};
	}
	getFullStreetAddress(data) {
		if (data.address instanceof Object) {
			let address = data.address?.street || data.address?.street_name || null;
			address =
				address && typeof address === "string"
					? this.sanitizeAddress(address)
					: null;
			return address;
		}
		return data.address;
	}

	sanitizeAddress(address) {
		const abbreviations = {
			Domicilio: "",
			C: "Calle",
			Crer: "Carrer",
			Pseo: "Paseo",
			Avda: "Avenida",
			Pssg: "Passeig",
		};

		return address
			?.split(" ")
			?.map((substring) => {
				// There's abbreviations that end with .
				const sanitizedSubstring = substring.replace(".", "").replace("/", "");
				return abbreviations[sanitizedSubstring] ?? sanitizedSubstring;
			})
			.filter((n) => n) // Remove whitespaces
			.join(" ")
			.trim();
	}

	getFullName() {
		return (
			[this.name, this.surname, this.second_surname]
				.filter((item) => item)
				.join(" ") || null
		);
	}
}
