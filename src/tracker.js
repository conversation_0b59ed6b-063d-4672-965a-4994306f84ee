import { AWSKinesisFirehoseProvider, Auth, Analytics } from "aws-amplify";

/**
 * AWSKinesisTrackerProvider is a decorator around the built-in
 * AWSKinesisFirehoseProvider that repackages events written by the amplify analytics
 * trackers.
 *
 * The kinesis provider expects an object with a property named 'event'.
 * This event should contain the streamName, optionally the
 * partitionKey, and the payload in a field called data. On the backend,
 * for example in a Studio notebook, is what you can query. You could
 * create a table in zeppelin/flink to read this data like so:
 *
 * CREATE TABLE `pviewsjsonv` (
 *    `name` STRING,
 *    `attributes` ROW<url STRING, clientTimestamp STRING, userAgent STRING>
 *    )
 * WITH (
 *    'connector' = 'kinesis',
 *    'stream' = 'mvpKinesis-dev',
 *    'aws.region' = 'us-east-2',
 *    'scan.stream.initpos' = 'LATEST',
 *    'format' = 'json'
 * );
 *
 * NOTE: this assumes you've added clientTimestamp and userAgent via a
 * custom attributes function on the tracker.
 *
 * To use, you need to add the tracker as a pluggable, set the region in
 * configuration, and the setup autotracking. For example:
 *
 * Analytics.addPluggable(
 *   new AWSKinesisTrackerProvider({streamName: 'myStream'}));
 *
 * Analytics.configure({
 *   AWSKinesis: {
 *     region: awsExports.aws_project_region
 *   },
 *   // note the stream name becomes part of the provider name. This is to
 *   // avoid collisions if you want to write tracker events to different
 *   // streams.
 *   'AWSKinesisTracker-myStream': {
 *      region: awsExports.aws_project_region
 *   }
 * });
 *
 * Analytics.autoTrack('pageView', {
 *   enable: true,
 *   type: 'SPA',
 *   provider: 'AWSKinesisTracker-myStream',
 *   attributes: getAdditionalTrackerData //this is a function that returns an object with additional event attributes
 * });
 */

const TRACKER_NAME = "AnalyticsTracker";

const MAX_SIZE = 1024;

export class TrackerProvider {
	constructor(trackerParams, config) {
		this._source = trackerParams.source;
		this._streamName = trackerParams.streamName;
		this._brand_id = trackerParams.brand_id;
		this._guest_id = trackerParams.guest_id;
		this._res_id = trackerParams.res_id;
		this._provider = new AWSKinesisFirehoseProvider(config);
		if (!this._brand_id) {
			throw Error(`Cannot instanciate ${TRACKER_NAME} : no brand_id set`);
		}
	}

	async init() {
		await this.getCredentials();
	}

	setGuestId(guestId) {
		this._guest_id = guestId;
	}

	setResId(resId) {
		this._res_id = resId;
	}

	setBrandId(brand_id) {
		this._brand_id = brand_id;
	}

	// using congito identity pool
	// we assign the generated identityId as the trace_id
	// format of identityId eu-west-1:xxxx-xxxxx-xxx-xxxx
	// so we remove the region first
	async getCredentials() {
		const creds = await Auth.currentCredentials();
		const identityId = creds?.identityId?.split(":")[1]; // removing region prefix
		this._trace_id = identityId;
	}

	configure(config) {
		return this._provider.configure(config);
	}

	record(params) {
		// addind brand_id and trace_id
		//TODO  should validate that they are properly set and always exists
		let event = {
			data: {
				...params.event,
				brand_id: this._brand_id,
				guest_id: this._guest_id,
				res_id: this._res_id,
				trace_id: this._trace_id,
				source: this._source,
				error: params.event?.attributes?.response?.error ? true : false,
				time: new Date().getTime().toString(),
			},
			streamName: this._streamName,
		};

		if (!this._trace_id || !this._brand_id) {
			throw Error(
				`Cannot record ${TRACKER_NAME} : no brand_id or trace_id set`,
			);
		}

		event = this.removeAttributesWithLargeValues(event);
		console.debug("analytics sending", JSON.stringify(event));
		return this._provider.record({ event });
	}

	/**
	 * get the category of the plugin
	 */
	getCategory() {
		return "Analytics";
	}

	/**
	 * get provider name of the plugin
	 */
	getProviderName() {
		return TRACKER_NAME;
	}

	removeAttributesWithLargeValues(obj) {
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				if (
					typeof obj[key] === "string" &&
					Buffer.byteLength(obj[key]) > 512
				) {
					obj[key] = "hidden"; //if its more than 512kb
				} else if (typeof obj[key] === "object") {
					//if the prop is an object, we call the function recursively for that property
					this.removeAttributesWithLargeValues(obj[key]);
				}
			}
		}
		return obj;
	}
}

export const Tracker = {
	record(event) {
		Analytics.record(event, TRACKER_NAME);
	},
	recordCustomEvent(name, data) {
		return this.record({
			name: "custom",
			attributes: {
				name,
				data,
			},
		});
	},
	recordRequest(name, params, response) {
		return this.record({
			name: "request",
			attributes: {
				name,
				params,
				response,
			},
		});
	},

	createError({ code, message, data }) {
		if (message) {
			return { error: { code, message, ...data } };
		} else {
			return { error: { code, ...data } };
		}
	},
};
