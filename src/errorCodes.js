/*
	The structure of an error is always {PROJECT}_{ERROR_TYPE}_{ERROR_CODE}.
	At the moment we define 3 ERROR_TYPE
		1 --> INTERNAL
		2 --> EXTERNAL
		3 --> LOGIC
	All error codes must be unique.
	Before adding a new error code, check with your team.
*/

export default {
	UNKNOWN_OCR: "OCR_1_1",
	UNKNOWN: "ACI_1_1",
	EMPTY_RESERVATION: "ACI_3_1",
	INVALID_RESERVATION: "ACI_3_2"
};

export const ERROR_CODE_MAP = {
  "Unknown": "OCR_1_1",
  "Invalid request, info missing": "OCR_1_2",
  "SchengenZoneError": "OCR_3_2",
  "drivingLicenseError": "OCR_3_3",
  "Document Expired": "OCR_3_4",
  "D<PERSON> doesn't have nationality": "OCR_3_5",
  "RequireValuesNotFoundError": "OCR_3_6",
  "DocumentNotDetectedError": "OCR_3_7",
  "ServiceNotAvailable": "OCR_3_8",
  "Request has invalid image format": "OCR_3_9",
}
