import VueRouter from "vue-router";
const { isNavigationFailure } = VueRouter;

export default {
  methods: {
    redirect(to) {
      return this.$router.push(to).catch(failure => {
        if (
          isNavigationFailure(failure) &&
          failure.to.meta.active &&
          !failure.to.meta.active()
        ) {
          // avoid navigation erros due to deactivated route
          return;
        }
        console.error(failure.message, { failure });
      });
    }
  }
};
