import moment from "moment";
import { ccaaList, provinces } from "@/utils/ccaa";
import { mapGetters } from "vuex";

export default {
	methods: {
		...mapGetters({
      getTotalGuests: 'reservations/getTotalGuests',
    }),
		setDocumentVariablesValues(guest) {			
			// We encapsulate the documentCount variable in a span with display none so that it is invisible
			// to the final user and can be changed later on the server with the corresponding value.
			return {
				documentCount: "<span style='display:none'>\\{{documentCount}}</span>",
				guestSignature:
					"<span style='display:none'>\\{{guestSignature}}</span>",
				bookingVoucherCode: "NOT RETRIEVED",
				bookingCode: this.reservationSelected?.res_id || "NOT RETRIEVED",
				bookingType: "NOT RETRIEVED",
				bookingChannel: this.reservationSelected?.res_channel
					? this.reservationSelected?.res_channel
					: "NOT RETRIEVED",
				guestTelephone:
					guest?.telephone?.dialCode &&
					guest?.telephone?.value
						? `${guest.telephone.dialCode} ${guest.telephone.value}`
						: "NOT RETRIEVED",
				guestEmail: guest?.email || "NOT RETRIEVED",
				bookingDate: this.reservationSelected?.res_date || "NOT RETRIEVED",
				bookingCheckInDate:
					this.reservationSelected?.check_in || "NOT RETRIEVED",
				bookingCheckOutDate:
					this.reservationSelected?.check_out || "NOT RETRIEVED",
				bookingNightsNumber:
					this.reservationSelected?.res_nights || "NOT RETRIEVED",
				bookingRoomType:
					this.reservationSelected?.res_room_type || "NOT RETRIEVED",
				bookingInvoiceRoomType: "NOT RETRIEVED",
				bookingRoomNumber:
					this.reservationSelected?.res_room_number || "NOT RETRIEVED",
				bookingBoardType: this.reservationSelected?.res_board || "NOT RETRIEVED",
				bookingInvoiceBoardType: "NOT RETRIEVED",
				bookingAdultsNumber:
					this.reservationSelected?.res_adults || "NOT RETRIEVED",
				bookingChildrenNumber:
					this.reservationSelected?.res_children || "NOT RETRIEVED",
				bookingGuestsNumber: this.getTotalGuests() || "NOT RETRIEVED",
				guestMinimumAge:
					this.config.child_required_identity_documents_age || "NOT RETRIEVED",
				guestType: "NOT RETRIEVED",
				guestFirstName: guest?.name || "NOT RETRIEVED",
				guestSurname: guest?.surname || "NOT RETRIEVED",
				guestSecondSurname: guest?.second_surname || "NOT RETRIEVED",
				guestFullSurname:
					[guest?.surname, guest?.second_surname]
						.filter((n) => !!n)
						.join(" ") || "NOT RETRIEVED",
				guestFullName:
					[
						guest?.name,
						guest?.surname,
						guest?.second_surname,
					]
						.filter((n) => !!n)
						.join(" ") || "NOT RETRIEVED",
				guestGender: guest?.gender || "NOT RETRIEVED",
				guestNationality: guest?.nationality || "NOT RETRIEVED",
				guestDateOfBirth: guest.birthday_date
					? moment(guest?.birthday_date, true).format("YYYY-MM-DD")
					: "NOT RETRIEVED",
				guestDocumentType:
					this.$t(`validatedata.${guest?.document_type}`) ||
					"NOT RETRIEVED",
				guestDocumentNumber: guest?.document_number || "NOT RETRIEVED",
				guestDocumentDateOfIssue: guest?.date_of_issue
					? moment(guest?.date_of_issue, true).format("YYYY-MM-DD")
					: "NOT RETRIEVED",
				guestDocumentDateOfExpiry: guest?.date_of_expiry
					? moment(guest?.date_of_expiry, true).format("YYYY-MM-DD")
					: "NOT RETRIEVED",
				guestDocumentSupportNumber:
				guest?.document_support_number || "NOT RETRIEVED",
				guestResidenceCountry:
				guest?.residence_country || "NOT RETRIEVED",
				guestAddress: guest?.address?.street || "NOT RETRIEVED",
				guestPostalCode: guest?.address?.postal_code || "NOT RETRIEVED",
				guestCCAA:
					ccaaList.find((ccaa) => ccaa.code === guest.address?.CCAA)
						?.value || "NOT RETRIEVED",
				guestMunicipality: guest?.address?.city || "NOT RETRIEVED",
				guestProvince:
					provinces.find(
						(province) => province.code === guest?.address?.province,
					)?.value || "NOT RETRIEVED",
				guestRegion: guest?.address?.region,
				guestSubRegion: guest?.address?.subregion,
				guestKinship: guest?.kinship || "NOT RETRIEVED",
				brandName: this.name,
				childData: this.getChildGuests?.length ? this.getChildGuests : null,
				isHolder: guest?.holder,
			};
		},
	},
};
