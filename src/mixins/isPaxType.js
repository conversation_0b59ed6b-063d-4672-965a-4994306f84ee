import moment from "moment";

export default {
  methods: {
    isChild(birthday, checkInDate, ADULT_AGE) {
      if(!checkInDate) checkInDate = moment().format('YYYY-MM-DD');
      const ageAtCheckinDate = moment(checkInDate).diff(
        moment(birthday),
        "years"
      );
      return ageAtCheckinDate < ADULT_AGE;
    },

    isAdult(birthday, checkInDate, ADULT_AGE) {
      const ageAtCheckinDate = moment(checkInDate).diff(
        moment(birthday),
        "years"
      );

      return ageAtCheckinDate >= ADULT_AGE;
    }
  }
};
