import moment from "moment-timezone";
import { cloneDeep } from "lodash";

export default {  
  methods: {
    isCheckinAvailable(
      checkin,
      timeLimitCheckin,
      closeTimeLimitCheckin,
      timezone
    ) {
      const timeLimitActive = this.config.activate_time_limit;
      const timeBeforeCheckin = this.getTimeBeforeCheckin(checkin, timezone);
      let available = true;
      let message = "";

      // If reservation has passed, allow checkin
      if (
        timeLimitActive &&
        timeBeforeCheckin.days >= 0 &&
        timeLimitCheckin > 0
      ) {
        available = timeLimitCheckin >= timeBeforeCheckin.days;
        message = this.$t("search.checkinStartingAt", {
          ...timeBeforeCheckin,
          days: timeBeforeCheckin.days - timeLimitCheckin
        });
      }

      if (
        timeLimitActive &&
        (closeTimeLimitCheckin || closeTimeLimitCheckin === 0) &&
        available
      ) {
        available = timeBeforeCheckin.days >= closeTimeLimitCheckin;
        message = this.$t("search.checkinClosedAt", {
          ...timeBeforeCheckin,
          days: Math.abs(closeTimeLimitCheckin - timeBeforeCheckin.days - 1),
          hours: 24 - timeBeforeCheckin.hours
        });
      }

      return { available, message };
    },
    getTimeBeforeCheckin(checkin, timezone) {
      const today = moment.tz(timezone);
      const checkinDate = moment.tz(checkin, timezone)

      const days = cloneDeep(checkinDate)
        .startOf("day")
        .diff(cloneDeep(today).startOf("day"), "days");
  
      const [hours, mins] = moment
        .tz(checkinDate - today, timezone)
        .format("H:mm:ss")
        .split(":");
        
      return { days, hours, mins };
    },
  },
};