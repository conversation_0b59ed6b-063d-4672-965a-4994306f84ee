import { mapState } from "vuex";

export const COUNTRY_CODE = ["BR"];

export const AMERICAN_FORMAT = "MM-DD-YYYY";

export const DEFAULT_FORMAT = "DD-MM-YYYY";

export const EMITTED_VALUE = "YYYY-MM-DD";

export default {
	methods: {
		countryFormat() {
			return COUNTRY_CODE.includes(this.placeCountry)
				? AMERICAN_FORMAT
				: DEFAULT_FORMAT;
		},
	},
	computed: {
		...mapState("brand", { placeCountry: "country" }),
	},
};
