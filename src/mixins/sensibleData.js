import { sanitizeString } from "@/utils/stringUtils.js";
import { countries, countryList } from "@/utils/countries";
import { mapState } from "vuex";

export default {
	data() {
		return {
			documentTypeValue: null,
			nationalityValue: "",
			issuingCountryValue: "",
			nationalityOptions: [],
			nationalitySuggestion: [],
			nationalityError: true,
			issuingCountryError: true,
		};
	},
	async created() {
		this.updateNationalityOptions();
	},

	computed: {
		...mapState("brand", {
			country: "country",
		}),
		translatedCountries() {
			const sanitizedCountries = countries.map((country) => {
				return {
					...country,
					name: this.$t(`countries.${country.value}`),
				};
			});
			return sanitizedCountries;
		},
		getTranslatedDocumentTypeOptions() {
			const options = [
				{
					value: "identity_card",
					name: this.$t("validatedata.identity_card"),
				},
				{
					value: "passport",
					name: this.$t("validatedata.passport"),
				},
				{
					value: "driving_license",
					name: this.$t("validatedata.driving_license"),
				},
				{
					value: "residence_permit",
					name: this.$t("validatedata.residence_permit"),
				},
			];
			return options;
		},
		isContinueButtonDisabled() {
			//in case the document retrieved from Klippa or selected by guest in the modal is ID or driving license, no nationality was retrieved from Klippa and no nationality was selected or selected with error
			const nationalityCondition =
				(!this.nationalityValue || this.nationalityError) &&
				this.imageOcr &&
				!this.imageOcr.nationality &&
				(this.documentTypeValue === "identity_card" ||
					this.documentTypeValue === "driving_license" ||
					this.imageOcr.document_type === "identity_card" ||
					this.imageOcr.document_type === "driving_license");

			//in case the document type was not retrieved from Klippa and it was not selected by guest either
			const documentTypeCondition =
				!this.documentTypeValue &&
				this.imageOcr &&
				!this.imageOcr.document_type;

			//in case the document retrieved from Klippa or selected by guest in the modal is a residence permit, no issuing country was retrieved from Klippa and no issuing country was selected or selected with error
			const issuingCountryCondition =
				(!this.issuingCountryValue || this.issuingCountryError) &&
				this.imageOcr &&
				!this.imageOcr.issuing_country &&
				(this.documentTypeValue === "residence_permit" ||
					this.imageOcr.document_type === "residence_permit");

			// disable button if any condition is true
			return (
				nationalityCondition || documentTypeCondition || issuingCountryCondition
			);
		},
	},
	watch: {
		"$i18n.locale": {
			handler(newValue, oldValue) {
				if (newValue !== oldValue) {
					this.updateNationalityOptions();
				}
			},
			immediate: true,
		},
	},

	methods: {
		updateNationalityOptions() {
			this.nationalityOptions = this.nationalitySuggestion =
				this.translatedCountries;
		},
		// When the nationality field is filled in, we update the list of possible countries to make an autocomplete of this field.
		nationalityChanged($event, input) {
			this.nationalitySuggestion = this.nationalityOptions.filter((country) =>
				sanitizeString(country.name.toLowerCase()).includes(
					sanitizeString($event.value.toLowerCase()),
				),
			);
			if (input === "Nationality") {
				this.nationalityValue = $event.value;
				this.nationalityError = $event.error;
			} else if (input === "IssuingCountry") {
				this.issuingCountryValue = $event.value;
				this.issuingCountryError = $event.error;
			}
		},
		async autocompleteSelect($event, input) {
			console.info(`${input} on sensible data filled`, {
				value: $event.value,
			});

			if (input === "Nationality") {
				this.nationalityValue = $event.value;
				this.nationalityError = false;
			} else if (input === "IssuingCountry") {
				this.issuingCountryValue = $event.value;
				this.issuingCountryError = false;
			}

			this.nationalitySuggestion = [];
		},
		documentTypeChanged($event) {
			console.info("Document Type on sensible data filled", {
				value: $event.value,
			});

			this.documentTypeValue = $event.value;
		},

		checkSchegenZone(input) {
			const isSchengenZone = countryList.find((codes) => {
				return codes.iso2Code === input;
			})?.isSchengenZone;
			return isSchengenZone ? true : false;
		},
		getISO2Value(country) {
			if (country?.length > 2) {
				return countryList.find((item) => {
					if (item.code === country) {
						return item;
					}
				}).iso2Code;
			}
			return country;
		},

		async schengenZoneHandler(
			iso2Nationality,
			iso2iIssuingCountry,
			iso2HotelCountry = "ES",
		) {
			const isHotelCountrySchengenZone =
				this.checkSchegenZone(iso2HotelCountry);
			const isNationalitySchengenZone = this.checkSchegenZone(iso2Nationality);

			if (this.imageOcr.document_type === "identity_card") {
				if (isHotelCountrySchengenZone) {
					if (isNationalitySchengenZone) {
						console.info("Sensible data modal filled correctly", {
							ocrData: this.imageOcr,
							nationalityValue: this.imageOcr.nationality,
							documentTypeValue: this.imageOcr.document_type,
						});
						return true;
					} else {
						await this.CLEAR_MODAL_STATE();
						await this.checkError("SchengenZoneError");
						this.nationalityValue = "";
						this.documentTypeValue = null;
						this.imageOcr.nationality = null;
						this.imageOcr.document_type = null;

						return false;
					}
				} else {
					//we check if hotel country and document nationality matches, for the case of a hotel outside schengen zone
					return this.checkSameCountry(
						"nationality",
						iso2Nationality,
						iso2HotelCountry,
					);
				}
			} else if (this.imageOcr.document_type === "driving_license") {
				if (!this.config.allow_driving_license) {
					console.info(
						"Driving license error when user complete sensible data",
						{
							ocrData: this.imageOcr,
							selectedCountryValue: this.imageOcr.nationality,
							documentTypeValue: this.imageOcr.document_type,
						},
					);
					this.nationalityValue = "";
					this.documentTypeValue = null;
					this.imageOcr.nationality = null;
					this.imageOcr.document_type = null;
					await this.CLEAR_MODAL_STATE();
					await this.checkError("drivingLicenseError");
					return false;
				} else {
					//we check if hotel country and document nationality matches, for the case of driving licenses
					return this.checkSameCountry(
						"nationality",
						iso2Nationality,
						iso2HotelCountry,
					);
				}
			} else if (this.imageOcr.document_type === "residence_permit") {
				//we check if hotel country and document issuing country matches, for the case of residence permit
				return this.checkSameCountry(
					"issuingCountry",
					iso2iIssuingCountry,
					iso2HotelCountry,
				);
			}
			return true;
		},

		async checkSameCountry(selectedInput, documentCountry, hotelCountry) {
			if (documentCountry === hotelCountry) {
				console.info("Sensible data modal filled correctly", {
					ocrData: this.imageOcr,
					selectedCountryValue:
						selectedInput === "nationality"
							? this.imageOcr.nationality
							: this.imageOcr.issuing_country,
					documentTypeValue: this.imageOcr.document_type,
				});
				return true;
			} else {
				console.info("Schengen error when user complete sensible data", {
					ocrData: this.imageOcr,
					selectedCountryValue:
						selectedInput === "nationality"
							? this.imageOcr.nationality
							: this.imageOcr.issuing_country,
					documentTypeValue: this.imageOcr.document_type,
				});
				await this.CLEAR_MODAL_STATE();
				await this.checkError("SchengenZoneError");
				if (selectedInput === "nationality") {
					this.nationalityValue = "";
					this.imageOcr.nationality = null;
				} else if (selectedInput === "issuingCountry") {
					this.issuingCountryValue = "";
					this.imageOcr.issuing_country = null;
				}
				this.imageOcr.document_type = null;
				this.documentTypeValue = null;
				return false;
			}
		},
		async checkSensibleData() {
			await this.LOADING(true);
			await this.VISIBLE(false);
			try {
				//we handle both cases, when data was retrieved from Klippa or selected by user in modal
				this.imageOcr.document_type =
					this.imageOcr.document_type || this.documentTypeValue;
				this.imageOcr.nationality =
					this.imageOcr.nationality || this.nationalityValue;
				this.imageOcr.issuing_country =
					this.imageOcr.issuing_country || this.issuingCountryValue;
				/*
				We reject the document and show an information modal if it's an ID card, and either the hotel country or the document nationality is not from the Schengen area, and they are not from the same country.
				Also, if its a driving license with a diferent nationality than the hotel's country.
				Also, if its a residence permit with a diferent issuing country than the hotel's country.
				*/
				const iso2HotelCountry = this.getISO2Value(this.country);
				
				const iso2Nationality = this.imageOcr.nationality
					? this.getISO2Value(this.imageOcr.nationality)
					: null;
				const iso2iIssuingCountry = this.imageOcr.issuing_country
					? this.getISO2Value(this.imageOcr.issuing_country)
					: null;

				const schengenZoneHandler = await this.schengenZoneHandler(
					iso2Nationality,
					iso2iIssuingCountry,
					iso2HotelCountry,
				);

				if (!schengenZoneHandler) {
					return false;
				}

				return true;
			} catch (error) {
				console.error("Scan completeSensibleData Error", { error });
				await this.checkError(error.message);
				return false;
			}
		},
	},
};
