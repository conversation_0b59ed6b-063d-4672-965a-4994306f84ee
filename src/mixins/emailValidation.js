import repository from "@/repository/repositoryFactory";

export default {
  methods: {
    async validateEmail(email, brandId) {
      let emailValidation;
      const apiEmail = repository.get("email");

      try {
        emailValidation = await apiEmail.validateEmail(email, brandId);
        return emailValidation.valid;
      } catch (error) {
        console.error("Email validation error", { error });
        return false;
      }
    }
  }
};
