import clearAllStore from "@/utils/clearAllStore";
import { mapState } from "vuex";
export default {
	computed:{
		...mapState("brand", ["brandId"]),
	},
	methods: {
		clearStoreAndRedirect: async function (forceBrandId = null) {
			const brandIdBeforeClearStore = forceBrandId || this.brandId;
			await clearAllStore(this.$store);
			await this.SET_BRAND_ID(brandIdBeforeClearStore);
			return this.$router.push({ path: `/${brandIdBeforeClearStore}` });
		},
	},
};
