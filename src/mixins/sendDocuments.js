import repository from "@/repository/repositoryFactory";
import { mapState, mapGetters } from "vuex";

export default {
  computed: {
    ...mapGetters("guest", { guestData: "getSelectedGuest" }),
    ...mapState("brand", [
      "brandId",
      "name",
      "backgroundImage_md",
      "logo",
      "config"
    ])
  },
  methods: {
    async sendDocuments(sendToGuest) {
      const apiCheckin = repository.get("checkin");

      const body = {
        documents: this.guestData.documentsSaved,
        brand: {
          id: this.brandId,
          name: this.name,
          background: this.backgroundImage_md,
          logo: this.logo
        },
        user: {
          name: this.guestData.name,
          gender: this.guestData.gender,
          email: sendToGuest ? this.guestData.email : "",
          lang: this.$i18n.locale
        }
      };
      return apiCheckin
        .sendDocuments(body)
        .catch(error => console.error("Send documents ERROR", { error }));
    }
  }
};
