import router from "../router";

export default {
  methods: {
    errorH<PERSON><PERSON>(error, view) {
      switch (error?.response?.status) {
        case 404:
          notFoundError(error, view);
          break;
        case 500:
          serverError(error, view);
          break;
        default:
          serverError(error, view);
          break;
      }
    }
  }
};

// 404
function notFoundError(error = null) {
  console.error("not found: ", {error});
  router.push({ name: "error404" });
}

//500
function serverError(error = null, view = "") {
  console.error(`Error 500 from server detected in view: ${view}`, {error});
  router.push({ name: "Error" });
}
