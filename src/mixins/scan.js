import imageCompression from "browser-image-compression";
import repository from "../repository/repositoryFactory";
import { mapActions, mapState } from "vuex";
import guestConfig from "@/mixins/guestConfig";
import moment from "moment";
import sensibleData from "../../src/mixins/sensibleData";


const Ocr = repository.get("ocr");

export default {
	data() {
		return {
			validImageFormats: ["image/jpeg", "image/jpg", "image/png"],
			similarGuest: {},
		};
	},
	mixins: [guestConfig, sensibleData],
	computed: {
		...mapState("brand", {
			brandId: "brandId",
			country: "country",
			config: "config",
		}),
		...mapState("scan", {
			scannedWithSignature: "scannedWithSignature",
		}),
	},
	methods: {
		...mapActions("guest", [
			"SIMILAR_GUESTS",
			"SELECT_GUEST",
			"CLEAR_SELECTED_GUESTS",
		]),
		...mapActions("scan", [
			"CLEAR_SCAN_DOCUMENT_ATTEMPT",
			"ADD_SCAN_DOCUMENT_ATTEMPT",
			"CLEAR_ALL_DOCUMENTS",
			"ADD_FACE_IMAGE",
			"ADD_IDENTITY_DOCUMENT",
			"SET_SCANNED_WITH_SIGNATURE",
			"SET_BACK_PART_SCANNED",
			"SET_SIGNATURE_UPLOAD_REQUIRED",
		]),
		async onFileChanged(event) {
			const images = event.target.files;
			if (images.length !== 0) {
				let image = images[0];
				if (!this.validImageFormats.includes(image.type)) {
					console.error(
						`File format is invalid (${image.type}). It must be PNG, JPG or JPEG.`,
					);
					this.$refs.fileUpload.value = null;
					this.ADD_SCAN_DOCUMENT_ATTEMPT();
					this.redirectIfReachedMaxAttempts();
					this.showModalError(this.$t("scan.error.invalidImageFormat"));
					return;
				}
				await this.LOADING(true);
				image = await this.compressImage(image);

				if (this.validateImage(image)) {
					this.startImageProcessing(image);
				}
			}
			this.$refs.fileUpload.value = null;
		},
		async compressImage(image) {
			if (image.size < 1024 * 1024) {
				console.info("The image is already small enough, don't compress it", {
					originalSize: `${(image.size / 1024).toFixed(2)} KB`,
				});
				return image;
			}

			const maxFinalSize = (image.size - image.size * 0.2) / (1024 * 1024);

			const options = {
				maxSizeMB: maxFinalSize,
				useWebWorker: true,
			};

			try {
				const compressedImage = await imageCompression(image, options);
				console.info("Original image compressed", {
					originalSize: `${(image.size / (1024 * 1024)).toFixed(2)} MB`,
					maxFinalSize: `${maxFinalSize} MB`,
					compressedImageSize: `${(
						compressedImage.size /
						(1024 * 1024)
					).toFixed(2)} MB`,
				});

				return compressedImage;
			} catch (error) {
				console.error("Error compressing image", { error });
				return image;
			}
		},
		/**
		 * Validate image if pass the request params, numbers in bytes
		 */
		validateImage(image) {
			// 30kb
			if (image.size <= 1024 * 30) {
				this.showModalError(this.$t("scan.error.undersizedImageError"));
				return false;
			}
			// 6mb
			if (image.size >= 6 * 1024 * 1024) {
				this.showModalError(this.$t("scan.error.oversizedImageError"));
				return false;
			}

			return true;
		},
		/**
		 * Parse image to base64 and remove prefix
		 */
		toBase64: (image) => {
			return new Promise((resolve, reject) => {
				const reader = new FileReader();
				reader.readAsDataURL(image);
				reader.onload = () =>
					resolve(reader.result.replace(/data:image\/.*,/, ""));
				reader.onerror = (error) => reject(error);
			});
		},
		async sendImage(images, documentScanned) {
			//images is an Array with base64 value
			try {
				const payload = {
					brandId: this.brandId,
					country: this.country,
					nationality: this.scanData?.nationality,
					images: images.map((image) => image.replace(/data:image\/.*,/, "")),
					res_id: this.reservationSelected.res_id,
					check_in: this.reservationSelected.check_in,
					check_out: this.reservationSelected.check_out,
				};
				const config = {
					allowExpiredDocuments: this.config.allow_expired_documents,
					allowDrivingLicense: this.config.allow_driving_license,
				};
				const document = await Ocr.getImageInfo(
					payload,
					config,
					documentScanned,
				);

				console.info("OCR getImage info response", { document });

				if (document.error) {
					this.checkError(document.error.message);
					return null;
				}

				return document.data;
			} catch (error) {
				const errorMessage = error?.response?.data?.error?.message;
				const errorCode = error?.response?.data?.error?.code;

				errorMessage
					? console.error(`Scan error: ${errorMessage}, code: ${errorCode}`)
					: console.error(
							`Scan error: ${error.name} ${error.message}, code: 0`,
					  );
				this.checkError(errorMessage);
				return null;
			}
		},
		async checkError(error) {
			this.ADD_SCAN_DOCUMENT_ATTEMPT();
			this.redirectIfReachedMaxAttempts();

			const errorMap = {
				SchengenZoneError: this.$t("scan.error.schengenZoneError"),
				drivingLicenseError: this.$t("scan.error.drivingLicenseError"),
				"Document Expired": this.$t("scan.error.expiredDocument"),
				"DNI doesn't have nationality": this.$t(
					"scan.error.noDataRequiredError",
				),
				RequireValuesNotFoundError: this.$t("scan.error.noDataRequiredError"),
				DocumentNotDetectedError: this.$t(
					"scan.error.documentNotDetectedError",
				),
				ServiceNotAvailable: this.config.optional_scan
					? this.$t("scan.error.serviceNotAvailableWithOptionalScan")
					: this.$t("scan.error.serviceNotAvailableWithoutOptionalScan"),
				"Request has invalid image format": this.$t(
					"scan.error.invalidImageFormat",
				),
				PassportSameCountryError: this.$t('scan.error.passportFromSameCountry')
			};

			this.showModalError(
				errorMap[error] ?? this.$t("scan.error.genericError"),
			);
		},
		/**
		 * Check if the user has reached the max attempts allowed,
		 * and redirect to an error view if it's true
		 */
		async redirectIfReachedMaxAttempts() {
			if (
				this.currentAttemptsDocumentScan >= this.config.max_attempts_document
			) {
				console.warn("Scan max max attempts exceeded");
				this.CLEAR_STATE();
				await this.redirectToErrorPage();
			}
		},
		async redirectToErrorPage() {
			if (this.config.optional_scan) {
				await this.SET_QUERY_PARAM({ manualProcess: true });
				return this.redirect({
					name: "Error",
					params: {
						error: "maxAttemptsScanErrorWithCompleteData",
						buttons: [
							{ name: "exitProcess", route: "Status" },
							{ name: "completeData", route: "ValidateData" },
						],
					},
				});
			} else {
				return this.redirect({
					name: "Error",
					params: {
						error: "maxAttemptsScanError",
						route: "Scan",
					},
				});
			}
		},
		async showModalError(message) {
			await this.SET_TYPE("error");
			await this.SET_NAME("errorModal");
			this.hasError = true;
			if (message === this.$t("scan.error.similarGuestError")) {
        await this.SET_TITLE("")
      } 
			this.messageError = message;
			await this.VISIBLE(true);
			this.stopLoading();
		},
		async openSignatureRequiredModal() {
			await this.SET_TYPE("info");
			await this.SET_NAME("signatureRequired");
			await this.SET_TITLE(this.$t("scan.modalSignatureRequiredTitle"));
			await this.VISIBLE(true);
			this.modalShown = true;
		},
		async handleSignatureRequired() {
			if (this.imageOcr.signature) {
				this.SET_SCANNED_WITH_SIGNATURE(true);
			} else if (
				this.documentSide === "signature" &&
				!this.imageOcr.signature
			) {
				await this.openSignatureRequiredModal();
				if (this.modalShown) return;
			}

			//TODO: when the Dominican Republic driver's license feature is developed, the following logic will have to be modified, since the driver's license may have a back
			const idFrontImage = this.getIdentityDocumentByName(
				"identity_card_front",
			);
			const idBackImage = this.getIdentityDocumentByName("identity_card_back");
			const passport = this.getIdentityDocumentByName("passport");
			const drivingLicense = this.getIdentityDocumentByName("driving_license");
			const residencePermitFrontImage = this.getIdentityDocumentByName(
				"residence_permit_front",
			);
			const residencePermitBackImage = this.getIdentityDocumentByName(
				"residence_permit_back",
			);

			if (idBackImage || residencePermitBackImage) {
				this.SET_BACK_PART_SCANNED(true);
			}
			// Check if either front and back of ID/residence permit, or passport, or driving license (front and back in case of some countries), have been scanned and there is no signature
			if (
				((idFrontImage && idBackImage) ||
					passport ||
					drivingLicense ||
					(residencePermitFrontImage && residencePermitBackImage)) &&
				!this.scannedWithSignature
			) {
				this.SET_SIGNATURE_UPLOAD_REQUIRED(true);
			}
		},

		calculateAge(data) {
			const checkInDate = this.reservationSelected.check_in;
			if (data) {
				return moment(checkInDate).diff(moment(data.birthday_date), "years");
			}
			return 0;
		},
		async validateData(data, { base64ImageSource, imageType }) {
			await this.LOADING(true);
			const ADULT_AGE = this.getAdultAge();

			if (
				this.guestData.pax_type !== "AD" &&
				this.calculateAge(data) >= ADULT_AGE
			) {
				this.showModalError(
					this.$t("scan.error.ageGuestError", {
						paxType: this.showPaxInfo[0],
						comparison: this.showPaxInfo[1],
					}),
				);
				return false;
			}
			this.similarGuest = await this.SIMILAR_GUESTS(data);

			if (
				this.similarGuest.coincidences?.some(
					coincidence =>
						coincidence.fieldName === "document_number" && coincidence.similarity === 1
				)
			){
				return this.handleSimilarGuestInReservation() 
			}
			
			// Check if DNI is null and if it is, we assume that it is a dni
			return await this.manageIdentityDocument(data, {
				base64ImageSource,
				imageType: imageType.replace("image/", ""),
			});
		},

		async manageIdentityDocument(scanData, { base64ImageSource, imageType }) {
			// We ensure if document side is not recieved, assign it depending which button user has clicked
			scanData.side = scanData.side ?? this.documentSide;

			if (scanData.face) {
				this.ADD_FACE_IMAGE(scanData.face);
			}

			await this.LOADING(true);

			const documentTypeMap = {
				identity_card: `identity_card_${scanData.side}`,
				signature: "signature",
				driving_license: "driving_license",
				passport: "passport",
				residence_permit: `residence_permit_${scanData.side}`,
			};

			const documentName = documentTypeMap[scanData.document_type];

			await this.ADD_IDENTITY_DOCUMENT({
				document: {
					data: scanData,
					title: documentName,
					content: base64ImageSource,
					extension: imageType.replace("image/", ""),
				},
			});
			this.CLEAR_SCAN_DOCUMENT_ATTEMPT();

			return true;
		},
		async handleSimilarGuestInReservation() {
			if (this.similarGuest?.processCompleted || this.similarGuest?.validated) {
				this.CLEAR_STATE();
				return this.showModalError(this.$t("scan.error.similarGuestError"));
			}
			// Children have its own form, unless config is setted
			if (
				(this.similarGuest?.pax_type === "CH" &&
					!this.config?.scan_children_like_adults) ||
				(this.similarGuest?.pax_type === "BB" &&
					!this.config?.scan_children_like_adults)
			) {
				this.CLEAR_STATE();
				this.SELECT_GUEST(this.similarGuest?.uuid);
				return this.redirect({ name: "ChildForm" });
			}

			await this.handleSelectedGuest(this.similarGuest);
		},

		async handleSelectedGuest(guest) {
			// First we select the new guest
			this.SELECT_GUEST(guest.uuid);
			await this.VISIBLE(false);
			await this.manageIdentityDocument(this.imageOcr, {
				base64ImageSource: this.base64Image,
				imageType: this.imageType,
			});

			if (this.$route.name === "AdvancedScan") {
				return this.redirect({ name: "Scan" });
			} else {
				await this.LOADING(false);
			}
		},
		modalClosed(modalName) {
			if (this.messageError === this.$t("scan.error.expiredDocument")) {
				this.CLEAR_MODAL_STATE();
				return this.goBack();
			}
			if (modalName === "signatureRequired") {
				this.CLEAR_MODAL_STATE();
				this.LOADING(false);
				// Remove scanned document from the signature slot if signature wasn't found
				this.CLEAR_DOCUMENT("signature");
			}
			if (this.messageError === this.$t("scan.error.similarGuestError")) {
				this.CLEAR_MODAL_STATE();
				return this.redirect({ name: "Status" });		
			}

			this.CLEAR_MODAL_STATE();
		},
		async goBack() {
			await this.LOADING(true);
			// Clear user data and scan
			await this.CLEAR_STATE();
			await this.CLEAR_SELECTED_GUESTS();
			await this.CLEAR_ALL_DOCUMENTS();
			return this.redirect({ name: "Status" });
		},
		async rejectInvalidPassport(document) { 
      const isPassport = document?.document_type === 'passport';
      const sameCountry = this.getISO2Value(document?.nationality) === this.country;
      const configEnabled = this.config.not_allow_passports_from_country_brand;

      if (isPassport && sameCountry && configEnabled) {
				this.checkError('PassportSameCountryError');
        return true;
      }

      return false;
    },
	},
};
