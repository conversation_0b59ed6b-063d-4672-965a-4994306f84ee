import moment from "moment";

export default {
	methods: {
		fillFormWithChildData(inputs, data, isReception = false) {
			inputs.forEach(
				function (input) {
					if (data.validated && !isReception) {
						input.disabled = true;
					}
					if (input.name === "name" && data?.name) {
						input.value = data?.name;
					}
					if (input.name === "surname" && data?.surname) {
						input.value = data?.surname;
					}
					if (input.name === "birthday" && data?.birthday_date) {
						input.value = data?.birthday_date;
					}
					if (input.options) {
						input.options.forEach(
							function (option) {
								if (
									input.name === "nationality" &&
									data?.nationality &&
									option.value === data.nationality
								) {
									input.value = option.value;
								} else if (
									(input.name === "kinship" &&
										data?.kinship &&
										option === data.kinship) ||
									isReception
								) {
									input.value = option || "son";
								}
							}.bind(this),
						);
					}
				}.bind(this),
			);

			return inputs;
		},
		childRequiresDocument(birthday, checkInDate, maxAgeToNotRequireID) {
			const ageAtCheckinDate = moment(checkInDate).diff(
				moment(birthday),
				"years",
			);
			return !(ageAtCheckinDate <= maxAgeToNotRequireID);
		},
	},
};
