import { i18n } from "../locales";
import { sortAlphabetically } from "./stringUtils";
import esCountries from "../locales/es/countries";
import enCountries from "../locales/en/countries";
import ptCountries from "../locales/pt/countries";
import deCountries from "../locales/de/countries";
export const countryList = [
	{
		dial_code: "+93",
		code: "AFG",
		iso2Code: "AF",
		name: i18n.t("countries.AFG"),
	},
	{
		dial_code: "+355",
		code: "ALB",
		iso2Code: "AL",
		isSchengenZone: true,
		name: i18n.t("countries.ALB"),
	},
	{
		dial_code: "+213",
		code: "DZA",
		iso2Code: "DZ",
		name: i18n.t("countries.DZA"),
	},
	{
		dial_code: "+1", // Area code 684
		code: "ASM",
		iso2Code: "AS",
		name: i18n.t("countries.ASM"),
	},
	{
		dial_code: "+376",
		code: "AND",
		iso2Code: "AD",
		isSchengenZone: true,
		name: i18n.t("countries.AND"),
	},
	{
		dial_code: "+244",
		code: "AGO",
		iso2Code: "AO",
		name: i18n.t("countries.AGO"),
	},
	{
		dial_code: "+1", // Area code 264
		code: "AIA",
		iso2Code: "AI",
		name: i18n.t("countries.AIA"),
	},
	{
		dial_code: "+672",
		code: "ATA",
		iso2Code: "AQ",
		name: i18n.t("countries.ATA"),
	},
	{
		dial_code: "+1", // Area code 268
		code: "ATG",
		iso2Code: "AG",
		name: i18n.t("countries.ATG"),
	},
	{
		dial_code: "+54",
		code: "ARG",
		iso2Code: "AR",
		name: i18n.t("countries.ARG"),
	},
	{
		dial_code: "+374",
		code: "ARM",
		iso2Code: "AM",
		name: i18n.t("countries.ARM"),
	},
	{
		dial_code: "+297",
		code: "ABW",
		iso2Code: "AW",
		name: i18n.t("countries.ABW"),
	},
	{
		dial_code: "+61",
		code: "AUS",
		preferred: true,
		iso2Code: "AU",
		name: i18n.t("countries.AUS"),
	},
	{
		dial_code: "+43",
		code: "AUT",
		iso2Code: "AT",
		isSchengenZone: true,
		name: i18n.t("countries.AUT"),
	},
	{
		dial_code: "+994",
		code: "AZE",
		iso2Code: "AZ",
		name: i18n.t("countries.AZE"),
	},
	{
		dial_code: "+1", // Area code 242
		code: "BHS",
		iso2Code: "BS",
		name: i18n.t("countries.BHS"),
	},
	{
		dial_code: "+973",
		code: "BHR",
		iso2Code: "BH",
		name: i18n.t("countries.BHR"),
	},
	{
		dial_code: "+880",
		code: "BGD",
		iso2Code: "BD",
		name: i18n.t("countries.BGD"),
	},
	{
		dial_code: "+1", // Area code 246
		code: "BRB",
		iso2Code: "BB",
		name: i18n.t("countries.BRB"),
	},
	{
		dial_code: "+375",
		code: "BLR",
		iso2Code: "BY",
		name: i18n.t("countries.BLR"),
	},
	{
		dial_code: "+32",
		code: "BEL",
		iso2Code: "BE",
		isSchengenZone: true,
		name: i18n.t("countries.BEL"),
	},
	{
		dial_code: "+501",
		code: "BLZ",
		iso2Code: "BZ",
		name: i18n.t("countries.BLZ"),
	},
	{
		dial_code: "+229",
		code: "BEN",
		iso2Code: "BJ",
		name: i18n.t("countries.BEN"),
	},
	{
		dial_code: "+1", // Area 441
		code: "BMU",
		iso2Code: "BM",
		name: i18n.t("countries.BMU"),
	},
	{
		dial_code: "+975",
		code: "BTN",
		iso2Code: "BT",
		name: i18n.t("countries.BTN"),
	},
	{
		dial_code: "+591",
		code: "BOL",
		iso2Code: "BO",
		name: i18n.t("countries.BOL"),
	},
	{
		dial_code: "+387",
		code: "BIH",
		iso2Code: "BA",
		isSchengenZone: true,
		name: i18n.t("countries.BIH"),
	},
	{
		dial_code: "+267",
		code: "BWA",
		iso2Code: "BW",
		name: i18n.t("countries.BWA"),
	},
	{
		dial_code: "+55",
		code: "BRA",
		iso2Code: "BR",
		name: i18n.t("countries.BRA"),
	},
	{
		dial_code: "+246",
		code: "IOT",
		iso2Code: "IO",
		name: i18n.t("countries.IOT"),
	},
	{
		dial_code: "+673",
		code: "BRN",
		iso2Code: "BN",
		name: i18n.t("countries.BRN"),
	},
	{
		dial_code: "+359",
		code: "BGR",
		iso2Code: "BG",
		isSchengenZone: true,
		name: i18n.t("countries.BGR"),
	},
	{
		dial_code: "+226",
		code: "BFA",
		iso2Code: "BF",
		name: i18n.t("countries.BFA"),
	},
	{
		dial_code: "+257",
		code: "BDI",
		iso2Code: "BI",
		name: i18n.t("countries.BDI"),
	},
	{
		dial_code: "+855",
		code: "KHM",
		iso2Code: "KH",
		name: i18n.t("countries.KHM"),
	},
	{
		dial_code: "+237",
		code: "CMR",
		iso2Code: "CM",
		name: i18n.t("countries.CMR"),
	},
	{
		dial_code: "+1",
		code: "CAN",
		iso2Code: "CA",
		name: i18n.t("countries.CAN"),
	},
	{
		dial_code: "+238",
		code: "CPV",
		iso2Code: "CV",
		name: i18n.t("countries.CPV"),
	},
	{
		dial_code: "+1", // Area code 345
		code: "CYM",
		iso2Code: "KY",
		name: i18n.t("countries.CYM"),
	},
	{
		dial_code: "+236",
		code: "CAF",
		iso2Code: "CF",
		name: i18n.t("countries.CAF"),
	},
	{
		dial_code: "+235",
		code: "TCD",
		iso2Code: "TD",
		name: i18n.t("countries.TCD"),
	},
	{
		dial_code: "+56",
		code: "CHL",
		iso2Code: "CL",
		name: i18n.t("countries.CHL"),
	},
	{
		dial_code: "+86",
		code: "CHN",
		iso2Code: "CN",
		name: i18n.t("countries.CHN"),
	},
	{
		dial_code: "+61",
		code: "CXR",
		iso2Code: "CX",
		name: i18n.t("countries.CXR"),
	},
	{
		dial_code: "+61",
		code: "CCK",
		iso2Code: "CC",
		name: i18n.t("countries.CCK"),
	},
	{
		dial_code: "+57",
		code: "COL",
		iso2Code: "CO",
		name: i18n.t("countries.COL"),
	},
	{
		dial_code: "+269",
		code: "COM",
		iso2Code: "KM",
		name: i18n.t("countries.COM"),
	},
	{
		dial_code: "+242",
		code: "COG",
		iso2Code: "CG",
		name: i18n.t("countries.COG"),
	},
	{
		dial_code: "+243",
		code: "COD",
		iso2Code: "CD",
		name: i18n.t("countries.COD"),
	},
	{
		dial_code: "+682",
		code: "COK",
		iso2Code: "CK",
		name: i18n.t("countries.COK"),
	},
	{
		dial_code: "+506",
		code: "CRI",
		iso2Code: "CR",
		name: i18n.t("countries.CRI"),
	},
	{
		dial_code: "+225",
		code: "CIV",
		iso2Code: "CI",
		name: i18n.t("countries.CIV"),
	},
	{
		dial_code: "+385",
		code: "HRV",
		iso2Code: "HR",
		isSchengenZone: true,
		name: i18n.t("countries.HRV"),
	},
	{
		dial_code: "+53",
		code: "CUB",
		iso2Code: "CU",
		name: i18n.t("countries.CUB"),
	},
	{
		dial_code: "+537",
		code: "CYP",
		iso2Code: "CY",
		isSchengenZone: true,
		name: i18n.t("countries.CYP"),
	},
	{
		dial_code: "+420",
		code: "CZE",
		iso2Code: "CZ",
		isSchengenZone: true,
		name: i18n.t("countries.CZE"),
	},
	{
		dial_code: "+45",
		code: "DNK",
		iso2Code: "DK",
		isSchengenZone: true,
		name: i18n.t("countries.DNK"),
	},
	{
		dial_code: "+253",
		code: "DJI",
		iso2Code: "DJ",
		name: i18n.t("countries.DJI"),
	},
	{
		dial_code: "+1", // Area code 767
		code: "DMA",
		iso2Code: "DM",
		name: i18n.t("countries.DMA"),
	},
	{
		dial_code: "+1", // Area code 809 & 829 & 849
		code: "DOM",
		iso2Code: "DO",
		name: i18n.t("countries.DOM"),
	},
	{
		dial_code: "+593",
		code: "ECU",
		iso2Code: "EC",
		name: i18n.t("countries.ECU"),
	},
	{
		dial_code: "+20",
		code: "EGY",
		iso2Code: "EG",
		name: i18n.t("countries.EGY"),
	},
	{
		dial_code: "+503",
		code: "SLV",
		iso2Code: "SV",
		name: i18n.t("countries.SLV"),
	},
	{
		dial_code: "+240",
		code: "GNQ",
		iso2Code: "GQ",
		name: i18n.t("countries.GNQ"),
	},
	{
		dial_code: "+291",
		code: "ERI",
		iso2Code: "ER",
		name: i18n.t("countries.ERI"),
	},
	{
		dial_code: "+372",
		code: "EST",
		iso2Code: "EE",
		isSchengenZone: true,
		name: i18n.t("countries.EST"),
	},
	{
		dial_code: "+251",
		code: "ETH",
		iso2Code: "ET",
		name: i18n.t("countries.ETH"),
	},
	{
		dial_code: "+500",
		code: "FLK",
		iso2Code: "FK",
		name: i18n.t("countries.FLK"),
	},
	{
		dial_code: "+298",
		code: "FRO",
		iso2Code: "FO",
		name: i18n.t("countries.FRO"),
	},
	{
		dial_code: "+679",
		code: "FJI",
		iso2Code: "FJ",
		name: i18n.t("countries.FJI"),
	},
	{
		dial_code: "+358",
		code: "FIN",
		iso2Code: "FI",
		isSchengenZone: true,
		name: i18n.t("countries.FIN"),
	},
	{
		dial_code: "+33",
		code: "FRA",
		iso2Code: "FR",
		isSchengenZone: true,
		name: i18n.t("countries.FRA"),
	},
	{
		dial_code: "+594",
		code: "GUF",
		iso2Code: "GF",
		name: i18n.t("countries.GUF"),
	},
	{
		dial_code: "+689",
		code: "PYF",
		iso2Code: "PF",
		name: i18n.t("countries.PYF"),
	},
	{
		dial_code: "+241",
		code: "GAB",
		iso2Code: "GA",
		name: i18n.t("countries.GAB"),
	},
	{
		dial_code: "+220",
		code: "GMB",
		iso2Code: "GM",
		name: i18n.t("countries.GMB"),
	},
	{
		dial_code: "+995",
		code: "GEO",
		iso2Code: "GE",
		isSchengenZone: true,
		name: i18n.t("countries.GEO"),
	},
	{
		dial_code: "+49",
		code: "DEU",
		iso2Code: "DE",
		isSchengenZone: true,
		name: i18n.t("countries.DEU"),
	},
	{
		dial_code: "+233",
		code: "GHA",
		iso2Code: "GH",
		name: i18n.t("countries.GHA"),
	},
	{
		dial_code: "+350",
		code: "GIB",
		iso2Code: "GI",
		name: i18n.t("countries.GIB"),
	},
	{
		dial_code: "+30",
		code: "GRC",
		iso2Code: "GR",
		isSchengenZone: true,
		name: i18n.t("countries.GRC"),
	},
	{
		dial_code: "+299",
		code: "GRL",
		iso2Code: "GL",
		name: i18n.t("countries.GRL"),
	},
	{
		dial_code: "+1", // Area code 473
		code: "GRD",
		iso2Code: "GD",
		name: i18n.t("countries.GRD"),
	},
	{
		dial_code: "+590",
		code: "GLP",
		iso2Code: "GP",
		name: i18n.t("countries.GLP"),
	},
	{
		dial_code: "+1", // Area code 671
		code: "GUM",
		iso2Code: "GU",
		name: i18n.t("countries.GUM"),
	},
	{
		dial_code: "+502",
		code: "GTM",
		iso2Code: "GT",
		name: i18n.t("countries.GTM"),
	},
	{
		dial_code: "+44",
		code: "GGY",
		iso2Code: "GG",
		name: i18n.t("countries.GGY"),
	},
	{
		dial_code: "+224",
		code: "GIN",
		iso2Code: "GN",
		name: i18n.t("countries.GIN"),
	},
	{
		dial_code: "+245",
		code: "GNB",
		iso2Code: "GW",
		name: i18n.t("countries.GNB"),
	},
	{
		dial_code: "+595",
		code: "GUY",
		iso2Code: "GY",
		name: i18n.t("countries.GUY"),
	},
	{
		dial_code: "+509",
		code: "HTI",
		iso2Code: "HT",
		name: i18n.t("countries.HTI"),
	},
	{
		dial_code: "+379",
		code: "VAT",
		iso2Code: "VA",
		isSchengenZone: true,
		name: i18n.t("countries.VAT"),
	},
	{
		dial_code: "+504",
		code: "HND",
		iso2Code: "HN",
		name: i18n.t("countries.HND"),
	},
	{
		dial_code: "+852",
		code: "HKG",
		iso2Code: "HK",
		name: i18n.t("countries.HKG"),
	},
	{
		dial_code: "+36",
		code: "HUN",
		iso2Code: "HU",
		isSchengenZone: true,
		name: i18n.t("countries.HUN"),
	},
	{
		dial_code: "+354",
		code: "ISL",
		iso2Code: "IS",
		isSchengenZone: true,
		name: i18n.t("countries.ISL"),
	},
	{
		dial_code: "+91",
		code: "IND",
		preferred: true,
		iso2Code: "IN",
		name: i18n.t("countries.IND"),
	},
	{
		dial_code: "+62",
		code: "IDN",
		iso2Code: "ID",
		name: i18n.t("countries.IDN"),
	},
	{
		dial_code: "+98",
		code: "IRN",
		iso2Code: "IR",
		name: i18n.t("countries.IRN"),
	},
	{
		dial_code: "+964",
		code: "IRQ",
		iso2Code: "IQ",
		name: i18n.t("countries.IRQ"),
	},
	{
		dial_code: "+353",
		code: "IRL",
		iso2Code: "IE",
		isSchengenZone: true,
		name: i18n.t("countries.IRL"),
	},
	{
		dial_code: "+44",
		code: "IMN",
		iso2Code: "IM",
		name: i18n.t("countries.IMN"),
	},
	{
		dial_code: "+972",
		code: "ISR",
		iso2Code: "IL",
		name: i18n.t("countries.ISR"),
	},
	{
		dial_code: "+39",
		code: "ITA",
		iso2Code: "IT",
		isSchengenZone: true,
		name: i18n.t("countries.ITA"),
	},
	{
		dial_code: "+1", // Area code 876 & 658
		code: "JAM",
		iso2Code: "JM",
		name: i18n.t("countries.JAM"),
	},
	{
		dial_code: "+81",
		code: "JPN",
		iso2Code: "JP",
		name: i18n.t("countries.JPN"),
	},
	{
		dial_code: "+44",
		code: "JEY",
		iso2Code: "JE",
		name: i18n.t("countries.JEY"),
	},
	{
		dial_code: "+962",
		code: "JOR",
		iso2Code: "JO",
		name: i18n.t("countries.JOR"),
	},
	{
		dial_code: "+77",
		code: "KAZ",
		iso2Code: "KZ",
		name: i18n.t("countries.KAZ"),
	},
	{
		dial_code: "+254",
		code: "KEN",
		iso2Code: "KE",
		name: i18n.t("countries.KEN"),
	},
	{
		dial_code: "+686",
		code: "KIR",
		iso2Code: "KI",
		name: i18n.t("countries.KIR"),
	},
	{
		dial_code: "+850",
		code: "PRK",
		iso2Code: "KP",
		name: i18n.t("countries.PRK"),
	},
	{
		dial_code: "+82",
		code: "KOR",
		iso2Code: "KR",
		name: i18n.t("countries.KOR"),
	},
	{
		dial_code: "+965",
		code: "KWT",
		iso2Code: "KW",
		name: i18n.t("countries.KWT"),
	},
	{
		dial_code: "+996",
		code: "KGZ",
		iso2Code: "KG",
		name: i18n.t("countries.KGZ"),
	},
	{
		dial_code: "+856",
		code: "LAO",
		iso2Code: "LA",
		name: i18n.t("countries.LAO"),
	},
	{
		dial_code: "+371",
		code: "LVA",
		iso2Code: "LV",
		isSchengenZone: true,
		name: i18n.t("countries.LVA"),
	},
	{
		dial_code: "+961",
		code: "LBN",
		iso2Code: "LB",
		name: i18n.t("countries.LBN"),
	},
	{
		dial_code: "+266",
		code: "LSO",
		iso2Code: "LS",
		name: i18n.t("countries.LSO"),
	},
	{
		dial_code: "+231",
		code: "LBR",
		iso2Code: "LR",
		name: i18n.t("countries.LBR"),
	},
	{
		dial_code: "+218",
		code: "LBY",
		iso2Code: "LY",
		name: i18n.t("countries.LBY"),
	},
	{
		dial_code: "+423",
		code: "LIE",
		iso2Code: "LI",
		isSchengenZone: true,
		name: i18n.t("countries.LIE"),
	},
	{
		dial_code: "+370",
		code: "LTU",
		iso2Code: "LT",
		isSchengenZone: true,
		name: i18n.t("countries.LTU"),
	},
	{
		dial_code: "+352",
		code: "LUX",
		iso2Code: "LU",
		isSchengenZone: true,
		name: i18n.t("countries.LUX"),
	},
	{
		dial_code: "+853",
		code: "MAC",
		iso2Code: "MO",
		name: i18n.t("countries.MAC"),
	},
	{
		dial_code: "+389",
		code: "MKD",
		iso2Code: "MK",
		isSchengenZone: true,
		name: i18n.t("countries.MKD"),
	},
	{
		dial_code: "+261",
		code: "MDG",
		iso2Code: "MG",
		name: i18n.t("countries.MDG"),
	},
	{
		dial_code: "+265",
		code: "MWI",
		iso2Code: "MW",
		name: i18n.t("countries.MWI"),
	},
	{
		dial_code: "+60",
		code: "MYS",
		iso2Code: "MY",
		name: i18n.t("countries.MYS"),
	},
	{
		dial_code: "+960",
		code: "MDV",
		iso2Code: "MV",
		name: i18n.t("countries.MDV"),
	},
	{
		dial_code: "+223",
		code: "MLI",
		iso2Code: "ML",
		name: i18n.t("countries.MLI"),
	},
	{
		dial_code: "+356",
		code: "MLT",
		iso2Code: "MT",
		isSchengenZone: true,
		name: i18n.t("countries.MLT"),
	},
	{
		dial_code: "+692",
		code: "MHL",
		iso2Code: "MH",
		name: i18n.t("countries.MHL"),
	},
	{
		dial_code: "+596",
		code: "MTQ",
		iso2Code: "MQ",
		name: i18n.t("countries.MTQ"),
	},
	{
		dial_code: "+222",
		code: "MRT",
		iso2Code: "MR",
		name: i18n.t("countries.MRT"),
	},
	{
		dial_code: "+230",
		code: "MUS",
		iso2Code: "MU",
		name: i18n.t("countries.MUS"),
	},
	{
		dial_code: "+262",
		code: "MYT",
		iso2Code: "YT",
		name: i18n.t("countries.MYT"),
	},
	{
		dial_code: "+52",
		code: "MEX",
		iso2Code: "MX",
		name: i18n.t("countries.MEX"),
	},
	{
		dial_code: "+691",
		code: "FSM",
		iso2Code: "FM",
		name: i18n.t("countries.FSM"),
	},
	{
		dial_code: "+373",
		code: "MDA",
		iso2Code: "MD",
		isSchengenZone: true,
		name: i18n.t("countries.MDA"),
	},
	{
		dial_code: "+377",
		code: "MCO",
		iso2Code: "MC",
		isSchengenZone: true,
		name: i18n.t("countries.MCO"),
	},
	{
		dial_code: "+976",
		code: "MNG",
		iso2Code: "MN",
		name: i18n.t("countries.MNG"),
	},
	{
		dial_code: "+382",
		code: "MNE",
		iso2Code: "ME",
		isSchengenZone: true,
		name: i18n.t("countries.MNE"),
	},
	{
		dial_code: "+1", // Area code 664
		code: "MSR",
		iso2Code: "MS",
		name: i18n.t("countries.MSR"),
	},
	{
		dial_code: "+212",
		code: "MAR",
		iso2Code: "MA",
		name: i18n.t("countries.MAR"),
	},
	{
		dial_code: "+258",
		code: "MOZ",
		iso2Code: "MZ",
		name: i18n.t("countries.MOZ"),
	},
	{
		dial_code: "+95",
		code: "MMR",
		iso2Code: "MM",
		name: i18n.t("countries.MMR"),
	},
	{
		dial_code: "+264",
		code: "NAM",
		iso2Code: "NA",
		name: i18n.t("countries.NAM"),
	},
	{
		dial_code: "+674",
		code: "NRU",
		iso2Code: "NR",
		name: i18n.t("countries.NRU"),
	},
	{
		dial_code: "+977",
		code: "NPL",
		iso2Code: "NP",
		name: i18n.t("countries.NPL"),
	},
	{
		dial_code: "+31",
		code: "NLD",
		iso2Code: "NL",
		isSchengenZone: true,
		name: i18n.t("countries.NLD"),
	},
	{
		dial_code: "+599",
		code: "ANT",
		iso2Code: "AN",
		name: i18n.t("countries.ANT"),
	},
	{
		dial_code: "+687",
		code: "NCL",
		iso2Code: "NC",
		name: i18n.t("countries.NCL"),
	},
	{
		dial_code: "+64",
		code: "NZL",
		iso2Code: "NZ",
		name: i18n.t("countries.NZL"),
	},
	{
		dial_code: "+505",
		code: "NIC",
		iso2Code: "NI",
		name: i18n.t("countries.NIC"),
	},
	{
		dial_code: "+227",
		code: "NER",
		iso2Code: "NE",
		name: i18n.t("countries.NER"),
	},
	{
		dial_code: "+234",
		code: "NGA",
		iso2Code: "NG",
		name: i18n.t("countries.NGA"),
	},
	{
		dial_code: "+683",
		code: "NIU",
		iso2Code: "NU",
		name: i18n.t("countries.NIU"),
	},
	{
		dial_code: "+672",
		code: "NFK",
		iso2Code: "NF",
		name: i18n.t("countries.NFK"),
	},
	{
		dial_code: "+1", // Area code 670
		code: "MNP",
		iso2Code: "MP",
		name: i18n.t("countries.MNP"),
	},
	{
		dial_code: "+47",
		code: "NOR",
		iso2Code: "NO",
		isSchengenZone: true,
		name: i18n.t("countries.NOR"),
	},
	{
		dial_code: "+968",
		code: "OMN",
		iso2Code: "OM",
		name: i18n.t("countries.OMN"),
	},
	{
		dial_code: "+92",
		code: "PAK",
		iso2Code: "PK",
		name: i18n.t("countries.PAK"),
	},
	{
		dial_code: "+680",
		code: "PLW",
		iso2Code: "PW",
		name: i18n.t("countries.PLW"),
	},
	{
		dial_code: "+970",
		code: "PSE",
		iso2Code: "PS",
		name: i18n.t("countries.PSE"),
	},
	{
		dial_code: "+507",
		code: "PAN",
		iso2Code: "PA",
		name: i18n.t("countries.PAN"),
	},
	{
		dial_code: "+675",
		code: "PNG",
		iso2Code: "PG",
		name: i18n.t("countries.PNG"),
	},
	{
		dial_code: "+595",
		code: "PRY",
		iso2Code: "PY",
		name: i18n.t("countries.PRY"),
	},
	{
		dial_code: "+51",
		code: "PER",
		iso2Code: "PE",
		name: i18n.t("countries.PER"),
	},
	{
		dial_code: "+63",
		code: "PHL",
		iso2Code: "PH",
		name: i18n.t("countries.PHL"),
	},
	{
		dial_code: "+872",
		code: "PCN",
		iso2Code: "PN",
		name: i18n.t("countries.PCN"),
	},
	{
		dial_code: "+48",
		code: "POL",
		iso2Code: "PL",
		isSchengenZone: true,
		name: i18n.t("countries.POL"),
	},
	{
		dial_code: "+351",
		code: "PRT",
		iso2Code: "PT",
		isSchengenZone: true,
		name: i18n.t("countries.PRT"),
	},
	{
		dial_code: "+1", // Area code 939 & 787
		code: "PRI",
		iso2Code: "PR",
		name: i18n.t("countries.PRI"),
	},
	{
		dial_code: "+974",
		code: "QAT",
		iso2Code: "QA",
		name: i18n.t("countries.QAT"),
	},
	{
		dial_code: "+40",
		code: "ROU",
		iso2Code: "RO",
		isSchengenZone: true,
		name: i18n.t("countries.ROU"),
	},
	{
		dial_code: "+7",
		code: "RUS",
		iso2Code: "RU",
		name: i18n.t("countries.RUS"),
	},
	{
		dial_code: "+250",
		code: "RWA",
		iso2Code: "RW",
		name: i18n.t("countries.RWA"),
	},
	{
		dial_code: "+262",
		code: "REU",
		iso2Code: "RE",
		name: i18n.t("countries.REU"),
	},
	{
		dial_code: "+590",
		code: "BLM",
		iso2Code: "BL",
		name: i18n.t("countries.BLM"),
	},
	{
		dial_code: "+290",
		code: "SHN",
		iso2Code: "SH",
		name: i18n.t("countries.SHN"),
	},
	{
		dial_code: "+1", // Area code 869
		code: "KNA",
		iso2Code: "KN",
		name: i18n.t("countries.KNA"),
	},
	{
		dial_code: "+1", // Area code 758
		code: "LCA",
		iso2Code: "LC",
		name: i18n.t("countries.LCA"),
	},
	{
		dial_code: "+590",
		code: "MAF",
		iso2Code: "MF",
		name: i18n.t("countries.MAF"),
	},
	{
		dial_code: "+508",
		code: "SPM",
		iso2Code: "PM",
		name: i18n.t("countries.SPM"),
	},
	{
		dial_code: "+1", // Area code 784
		code: "VCT",
		iso2Code: "VC",
		name: i18n.t("countries.VCT"),
	},
	{
		dial_code: "+685",
		code: "WSM",
		iso2Code: "WS",
		name: i18n.t("countries.WSM"),
	},
	{
		dial_code: "+378",
		code: "SMR",
		iso2Code: "SM",
		isSchengenZone: true,
		name: i18n.t("countries.SMR"),
	},
	{
		dial_code: "+239",
		code: "STP",
		iso2Code: "ST",
		name: i18n.t("countries.STP"),
	},
	{
		dial_code: "+966",
		code: "SAU",
		iso2Code: "SA",
		name: i18n.t("countries.SAU"),
	},
	{
		dial_code: "+221",
		code: "SEN",
		iso2Code: "SN",
		name: i18n.t("countries.SEN"),
	},
	{
		dial_code: "+381",
		code: "SRB",
		iso2Code: "RS",
		isSchengenZone: true,
		name: i18n.t("countries.SRB"),
	},
	{
		dial_code: "+248",
		code: "SYC",
		iso2Code: "SC",
		name: i18n.t("countries.SYC"),
	},
	{
		dial_code: "+232",
		code: "SLE",
		iso2Code: "SL",
		name: i18n.t("countries.SLE"),
	},
	{
		dial_code: "+65",
		code: "SGP",
		iso2Code: "SG",
		name: i18n.t("countries.SGP"),
	},
	{
		dial_code: "+421",
		code: "SVK",
		iso2Code: "SK",
		isSchengenZone: true,
		name: i18n.t("countries.SVK"),
	},
	{
		dial_code: "+386",
		code: "SVN",
		iso2Code: "SI",
		isSchengenZone: true,
		name: i18n.t("countries.SVN"),
	},
	{
		dial_code: "+677",
		code: "SLB",
		iso2Code: "SB",
		name: i18n.t("countries.SLB"),
	},
	{
		dial_code: "+252",
		code: "SOM",
		iso2Code: "SO",
		name: i18n.t("countries.SOM"),
	},
	{
		dial_code: "+27",
		code: "ZAF",
		iso2Code: "ZA",
		name: i18n.t("countries.ZAF"),
	},
	{
		dial_code: "+500",
		code: "SGS",
		iso2Code: "GS",
		name: i18n.t("countries.SGS"),
	},
	{
		dial_code: "+34",
		code: "ESP",
		iso2Code: "ES",
		isSchengenZone: true,
		name: i18n.t("countries.ESP"),
	},
	{
		dial_code: "+94",
		code: "LKA",
		iso2Code: "LK",
		name: i18n.t("countries.LKA"),
	},
	{
		dial_code: "+249",
		code: "SDN",
		iso2Code: "SD",
		name: i18n.t("countries.SDN"),
	},
	{
		dial_code: "+597",
		code: "SUR",
		iso2Code: "SR",
		name: i18n.t("countries.SUR"),
	},
	{
		dial_code: "+47",
		code: "SJM",
		iso2Code: "SJ",
		name: i18n.t("countries.SJM"),
	},
	{
		dial_code: "+268",
		code: "SWZ",
		iso2Code: "SZ",
		name: i18n.t("countries.SWZ"),
	},
	{
		dial_code: "+46",
		code: "SWE",
		iso2Code: "SE",
		isSchengenZone: true,
		name: i18n.t("countries.SWE"),
	},
	{
		dial_code: "+41",
		code: "CHE",
		iso2Code: "CH",
		isSchengenZone: true,
		name: i18n.t("countries.CHE"),
	},
	{
		dial_code: "+963",
		code: "SYR",
		iso2Code: "SY",
		name: i18n.t("countries.SYR"),
	},
	{
		dial_code: "+886",
		code: "TWN",
		iso2Code: "TW",
		name: i18n.t("countries.TWN"),
	},
	{
		dial_code: "+992",
		code: "TJK",
		iso2Code: "TJ",
		name: i18n.t("countries.TJK"),
	},
	{
		dial_code: "+255",
		code: "TZA",
		iso2Code: "TZ",
		name: i18n.t("countries.TZA"),
	},
	{
		dial_code: "+66",
		code: "THA",
		iso2Code: "TH",
		name: i18n.t("countries.THA"),
	},
	{
		dial_code: "+670",
		code: "TLS",
		iso2Code: "TL",
		name: i18n.t("countries.TLS"),
	},
	{
		dial_code: "+228",
		code: "TGO",
		iso2Code: "TG",
		name: i18n.t("countries.TGO"),
	},
	{
		dial_code: "+690",
		code: "TKL",
		iso2Code: "TK",
		name: i18n.t("countries.TKL"),
	},
	{
		dial_code: "+676",
		code: "TON",
		iso2Code: "TO",
		name: i18n.t("countries.TON"),
	},
	{
		dial_code: "+1", // Area code 868
		code: "TTO",
		iso2Code: "TT",
		name: i18n.t("countries.TTO"),
	},
	{
		dial_code: "+216",
		code: "TUN",
		iso2Code: "TN",
		name: i18n.t("countries.TUN"),
	},
	{
		dial_code: "+90",
		code: "TUR",
		iso2Code: "TR",
		name: i18n.t("countries.TUR"),
	},
	{
		dial_code: "+993",
		code: "TKM",
		iso2Code: "TM",
		name: i18n.t("countries.TKM"),
	},
	{
		dial_code: "+1", // Area code 649
		code: "TCA",
		iso2Code: "TC",
		name: i18n.t("countries.TCA"),
	},
	{
		dial_code: "+688",
		code: "TUV",
		iso2Code: "TV",
		name: i18n.t("countries.TUV"),
	},
	{
		dial_code: "+256",
		code: "UGA",
		iso2Code: "UG",
		name: i18n.t("countries.UGA"),
	},
	{
		dial_code: "+380",
		code: "UKR",
		iso2Code: "UA",
		name: i18n.t("countries.UKR"),
	},
	{
		dial_code: "+971",
		code: "ARE",
		preferred: true,
		iso2Code: "AE",
		name: i18n.t("countries.ARE"),
	},
	{
		dial_code: "+44",
		code: "GBR",
		preferred: true,
		iso2Code: "GB",
		name: i18n.t("countries.GBR"),
	},
	{
		dial_code: "+1",
		code: "USA",
		preferred: true,
		iso2Code: "US",
		name: i18n.t("countries.USA"),
	},
	{
		dial_code: "+598",
		code: "URY",
		iso2Code: "UY",
		name: i18n.t("countries.URY"),
	},
	{
		dial_code: "+998",
		code: "UZB",
		iso2Code: "UZ",
		name: i18n.t("countries.UZB"),
	},
	{
		dial_code: "+678",
		code: "VUT",
		iso2Code: "VU",
		name: i18n.t("countries.VUT"),
	},
	{
		dial_code: "+58",
		code: "VEN",
		iso2Code: "VE",
		name: i18n.t("countries.VEN"),
	},
	{
		dial_code: "+84",
		code: "VNM",
		iso2Code: "VN",
		name: i18n.t("countries.VNM"),
	},
	{
		dial_code: "+1", // Area code 284
		code: "VGB",
		iso2Code: "VG",
		name: i18n.t("countries.VGB"),
	},
	{
		dial_code: "+1", // Area code 340
		code: "VIR",
		iso2Code: "VI",
		name: i18n.t("countries.VIR"),
	},
	{
		dial_code: "+681",
		code: "WLF",
		iso2Code: "WF",
		name: i18n.t("countries.WLF"),
	},
	{
		dial_code: "+967",
		code: "YEM",
		iso2Code: "YE",
		name: i18n.t("countries.YEM"),
	},
	{
		dial_code: "+260",
		code: "ZMB",
		iso2Code: "ZM",
		name: i18n.t("countries.ZMB"),
	},
	{
		dial_code: "+263",
		code: "ZWE",
		iso2Code: "ZW",
		name: i18n.t("countries.ZWE"),
	},
	{
		dial_code: "+358",
		code: "ALA",
		iso2Code: "AX",
		name: i18n.t("countries.ALA"),
	},
	{
		dial_code: "+599",
		code: "BES",
		iso2Code: "BQ",
		name: i18n.t("countries.BES"),
	},
	{
		dial_code: "+55",
		code: "BVT",
		iso2Code: "BV",
		name: i18n.t("countries.BVT"),
	},
	{
		dial_code: "+599",
		code: "CUW",
		iso2Code: "CW",
		name: i18n.t("countries.CUW"),
	},
	{
		dial_code: "+262",
		code: "ATF",
		iso2Code: "TF",
		name: i18n.t("countries.ATF"),
	},
	{
		dial_code: "+672",
		code: "HMD",
		iso2Code: "HM",
		name: i18n.t("countries.HMD"),
	},
	{
		dial_code: "+1", // Area code 721
		code: "SXM",
		iso2Code: "SX",
		name: i18n.t("countries.SXM"),
	},
	{
		dial_code: "+211",
		code: "SSD",
		iso2Code: "SS",
		name: i18n.t("countries.SSD"),
	},
	{
		dial_code: "+246",
		code: "UMI",
		iso2Code: "UM",
		name: i18n.t("countries.UMI"),
	},
	{
		dial_code: "+212",
		code: "ESH",
		iso2Code: "EH",
		name: i18n.t("countries.ESH"),
	},
];
export const countries = sortAlphabetically(
	countryList.map((country) => {
		return {
			value: country.code,
			name: country.name,
		};
	}, "name"),
);

const countriesArray = [esCountries, enCountries, ptCountries, deCountries];

export const countryNamesArray = Object.keys(esCountries).map((code) => {
	const names = countriesArray.map((countries) => countries[code]);
	return { code, names };
});

export const phoneCountries = sortAlphabetically(
	countryList.map((country) => {
		return {
			dialCode: country.dial_code.split("+")[1],
			name: country.name,
			iso2: country.iso2Code,
			iso3: country.code,
		};
	}),
	"name",
);
export const countryCodes = countryList.map((country) => {
	return {
		code: country.code,
		dial_code: country.dial_code,
	};
});
