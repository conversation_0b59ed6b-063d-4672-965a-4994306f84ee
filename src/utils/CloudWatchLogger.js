import CloudWatchLogs from "aws-sdk/clients/cloudwatchlogs";
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import StackTrace from "stacktrace-js";
import Vue from "vue";
import store from "@/store/index";

export default class CloudWatchLogger {
  events = [];
  originalConsole = null;
  intervalId = null;
  valid = false;
  client = null;
  accessKey = null;
  secretKey = null;
  // constructor(accessKeyId, secretAccessKey, region, group, levels = ['error'], interval = 3000, mute = false) {
  constructor({
    region,
    group,
    levels = ["error"],
    accessKey,
    secretKey,
    interval = 3000,
    mute = true
  }) {
    this.valid = region && group;
    this.region = region;
    this.group = group;
    this.levels = levels;
    this.interval = interval;
    this.mute = mute;
    this.accessKey = accessKey;
    this.secretKey = secretKey;
    // this.init()
  }

  setCache(key, value) {
    window.localStorage.setItem(`ConsoleCloudWatch:${key}`, value);
  }

  getCache(key) {
    return window.localStorage.getItem(`ConsoleCloudWatch:${key}`);
  }

  deleteCache(key) {
    return window.localStorage.removeItem(`ConsoleCloudWatch:${key}`);
  }

  async initCloudWatchClient() {
    this.client = new CloudWatchLogs({
      region: this.region,
      accessKeyId: this.accessKey,
      secretAccessKey: this.secretKey
    });
  }

  async init() {
    try {
      //get credentials and init cloudwathc client
      await this.initCloudWatchClient();
      const original = {};

      //hook into the global consoles
      for (const level of ["debug", "log", "info", "warn", "error"]) {
        original[level] = window.console[level];
        window.console[level] = (message, ...args) => {
          // Check if need to send to cloudWatch
          if (this.levels.includes(level)) {
            this.publishLog(message, level, ...args);
          }
          // Check if need to output on console
          if (!this.mute) {
            original[level](message, ...args);
          }
        };
      }
      this.originalConsole = original;
      this.intervalId = window.setInterval(
        this.onInterval.bind(this),
        this.interval
      );

      //hook into the vue errorHandler
      Vue.config.errorHandler = (error, vm, info) => {
        // handle error
        if (process.env.NODE_ENV !== "production") {
          console.log(error);
        }

        this.publishLog(error, "error", {
          component: vm.$options.name,
          lifecycleHook: info
        });
      };
    } catch (error) {
      this.valid = false;
      console.error("Could not init CloudWatchLogger", error);
    }
  }

  refresh() {
    this.deleteCache("key");
    this.deleteCache("sequenceToken");
    this.events.splice(0);
  }

  async publishLog(e, logLevel, info = {}) {
    if (!this.valid) {
      return;
    }
    this.events.push({
      message: await this.createPushMessage(e, logLevel, info),
      timestamp: new Date().getTime()
    });
  }

  async onInterval() {
    if (!this.valid) {
      return;
    }
    const pendingEvents = this.events.splice(0);
    if (!pendingEvents.length) {
      return;
    }
    const key = await this.createOrRetrieveKey();
    if (!key) {
      return;
    }
    const params = {
      logEvents: pendingEvents,
      logGroupName: this.group,
      logStreamName: key
    };
    const sequenceToken = this.getCache("sequenceToken");
    if (sequenceToken) {
      params.sequenceToken = sequenceToken;
    }
    let nextSequenceToken;
    let match;
    try {
      ({ nextSequenceToken } = await this.client
        .putLogEvents(params)
        .promise());
    } catch (e) {
      // If anything but InvalidSequenceTokenException error happens,
      // clear session storage with cloudwatch info
      if (!e || e.code !== "InvalidSequenceTokenException") {
        this.originalConsole.error(e);
        this.refresh();
        return;
      }
      // When InvalidSequenceTokenException happens, first batch of logs is lost
      // unless we send them again with the new sequence token
      if (e.code === "InvalidSequenceTokenException") {
        match = e.message.match(/The next expected sequenceToken is: (\w+)/);
        params.sequenceToken = match[1];
        ({ nextSequenceToken } = await this.client
          .putLogEvents(params)
          .promise());
      }
    }
    this.setCache("sequenceToken", nextSequenceToken);
  }

  async createOrRetrieveKey() {
    let key;
    if ((key === this.getCache("key"))) {
      return key;
    }
    try {
      const fingerprint = await FingerprintJS.load();
      const values = await fingerprint.get();
      // Generate hash value
      key = FingerprintJS.hashComponents(values.components);
      await this.client
        .createLogStream({
          logGroupName: this.group,
          logStreamName: key
        })
        .promise();
    } catch (e) {
      if (!e || e.code !== "ResourceAlreadyExistsException") {
        this.originalConsole.error(e);
        this.refresh();
        return;
      }
    }
    this.setCache("key", key);
    return key;
  }

  async createPushMessage(e, level, info = {}) {
    const message = e?.message ? e.message : e;
    const timestamp = new Date().getTime();
    const userAgent = window.navigator.userAgent;

    let stack = null;
    if (e?.message && e.stack) {
      stack = e.stack;
      try {
        stack = await StackTrace.fromError(e, {
          offline: true
        });
      } catch (_) {
        console.log("Empty");
      }
    }

    return JSON.stringify({
      message,
      timestamp,
      userAgent,
      stack,
      level_name: level,
      route: window.location.href,
      brand_id: store.state.brand.brandId,
      res_id: store.state.reservations.reservationSelected?.res_id,
      res_localizer:
        store.state.reservations.reservationSelected?.res_localizer,
      trace_id: store.state.trace.id,
      bouncer_uuid: store.state.trace.bouncerUuid,
      ...info
    });
  }
}
