export function sortByHolder(a, b) {
  return Number(a?.holder || 0) - Number(b?.holder || 0);
}

export function sortByValidated(a, b) {
  return Number(a?.validated || 0) - Number(b?.validated || 0);
}

export function sortByPaxType(a, b) {
  return (
    Number(a?.pax_type === "AD") - Number(b?.pax_type === "AD") ||
    Number(a?.pax_type === null) - Number(b?.pax_type === null) ||
    Number(a?.pax_type === "JR") - Number(b?.pax_type === "JR") ||
    Number(a?.pax_type === "CH") - Number(b?.pax_type === "CH")
  );
}

export function sortByName(a, b) {
  const guestHasName = function(name) {
    return name !== undefined && name !== null && name !== "";
  };
  return (
    Number(guestHasName(a?.full_name)) - Number(guestHasName(b?.full_name)) ||
    Number(guestHasName(a?.full_name)) - Number(guestHasName(b?.full_name))
  );
}
