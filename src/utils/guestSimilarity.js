import {
  sanitizeString,
} from "@/utils/stringUtils.js";

/**
 * @param {Object} data user information to match
 * @param {Array} guestList Array of guest objects
 * @param  {Number} minSimilarity similarity to match params
 *
 * @returns {Object} Most similar guest
 */

export function getMostSimilarGuest(
  data,
  guestList,
  selectedGuest = null,
) {
  if (Object.keys(data).length) {
    if (selectedGuest) {
      const selectedGuestSimilarity = getGuestSimilarity(
        data,
        selectedGuest,
      );

      if (selectedGuestSimilarity?.coincidences?.length > 0) {
        // If the scanned document looks like the selected guest we let it pass without checking it against other guests to avoid
        // the problem that the modal always pops up when the holder's data is present in all other guests.
        return {};
      }
    }

    const guestSimilarity = guestList?.map(pmsGuest => {
      return getGuestSimilarity(data, pmsGuest);
    });

    if (!guestSimilarity?.some(guest => guest?.coincidences?.length > 0)) {
      return {};
    }

    guestSimilarity.sort(
      (a, b) => b.coincidences?.length - a.coincidences?.length
    );
 
    // If there's a guest with exact document, return it
    return (
      guestSimilarity.find(guest =>
        guest.coincidences?.find(
          coincidence =>
            coincidence?.fieldName === "document_number" &&
            coincidence?.similarity === 1
        )
      ) || {}
    );
  }

  return {};
}

function getGuestSimilarity(data, pmsGuest) {
  if (
    pmsGuest?.document_number &&
    data?.document_number &&
    sanitizeString(pmsGuest?.document_number) ===
      sanitizeString(data?.document_number)
  ) {
    return {
      ...pmsGuest,
      coincidences: [
        {
          fieldName: "document_number",
          similarity: 1
        }
      ]
    };
  }
  return pmsGuest;
}


