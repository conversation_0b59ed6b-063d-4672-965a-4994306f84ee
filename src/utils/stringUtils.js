import stringSimilarity from "string-similarity";

/**
 * Method that compares 2 strings and returns a value between 0-1 depending on similarity
 * @param {string} firstValue
 * @param {string} secondValue
 * @returns value between 0-1
 */
export function getDifference(firstValue, secondValue) {
  try {
    return stringSimilarity.compareTwoStrings(firstValue, secondValue);
  } catch (e) {
    return new TypeError("One or both parameters are not strings");
  }
}

/**
 * Removes any special character from strings and returns string lowercarse
 * @param {string} str
 * @returns {string}
 */
export function sanitizeString(str) {
  return str.normalize("NFKD").replace(/[\u0300-\u036f]/g, "");
}

export function getStringRecombinations(strings = []) {
  const result = [];

  const permute = (arrayToPermute, auxArray = []) => {
    if (arrayToPermute.length === 0) {
      // push the names after joining them in string
      result.push(auxArray.join(" ").trim());
    } else {
      for (let i = 0; i < arrayToPermute.length; i++) {
        const curr = arrayToPermute.slice();
        const next = curr.splice(i, 1);
        permute(curr.slice(), auxArray.concat(next));
      }
    }
  };
  permute(strings);

  return result;
}

/**
 * Sorts an array alphabeticaly based on param name
 * @param {array} items
 * @param {string} param
 *
 * @returns {array}
 */
export function sortAlphabetically(items, param) {
  return items.sort((a, b) => {
    if (a[param] > b[param]) {
      return 1;
    }
    if (a[param] < b[param]) {
      return -1;
    }
    return 0;
  });
}
/**
 * Generates a unique uuid
 *
 * @returns {String} unique uuid
 */
export function generateUuidv4() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
