import { format } from "date-fns";

export const receptionInputs = [
	[
		{
			active: "true",
			name: "room_number",
			type: "text",
			minLength: "2",
			maxLength: "20",
			value: "",
			error: false,
		},
		{
			active: "false",
			name: "last_name",
			type: "text",
			minLength: "2",
			maxLength: "50",
			value: "",
			error: false,
		},
		{
			active: "true",
			name: "check_in",
			type: "date",
			value: format(new Date(), "YYYY-MM-DD"),
			error: false,
			checkOnStart: true,
		},
		{ active: "false", name: "check_out", type: "date" },
		{ active: "false", name: "email", type: "email" },
	],
];
