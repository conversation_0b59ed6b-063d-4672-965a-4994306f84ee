{"HL_ENV": null, "pluginsFile": "tests/e2e/plugins/index.js", "baseUrl": "http://localhost:8080/", "chromeWebSecurity": false, "videoCompression": false, "env": {"dev": {"brandId": 76, "optionalBrandId": 77, "childBrandId": 78, "guid": "a3d7d730-c268-437f-82b8-b513b2ad13fd", "logoImage": "https://dev-images.hotelinking.com/brands/a3d7d730-c268-437f-82b8-b513b2ad13fd/images/logo/small.jpg", "documents": [7], "documentsOptionalBrand": [8], "awsAuthConfig": {"userPoolId": "eu-west-1_PWO8DoiFf", "userPoolWebClientId": "2gfrorbnv6dppovg8lkr07ks44", "identityPoolId": "eu-west-1:f4214dc5-4f87-428b-80e1-b7f4580f24bb", "region": "eu-west-1", "mandatorySignIn": true, "signUpVerificationMethod": "link"}}, "testing": {"brandId": 304, "optionalBrandId": 304, "childBrandId": 304, "guid": "a3d7d730-c268-437f-82b8-b513b2ad13fd", "logoImage": "https://dev-images.hotelinking.com/brands/a3d7d730-c268-437f-82b8-b513b2ad13fd/images/logo/small.jpg", "documents": [7], "documentsOptionalBrand": [7], "awsAuthConfig": {"userPoolId": "eu-west-1_PWO8DoiFf", "userPoolWebClientId": "2gfrorbnv6dppovg8lkr07ks44", "identityPoolId": "eu-west-1:f4214dc5-4f87-428b-80e1-b7f4580f24bb", "region": "eu-west-1", "mandatorySignIn": true, "signUpVerificationMethod": "link"}}, "beta": {"brandId": 2, "optionalBrandId": 2, "childBrandId": 100, "guid": "6ab5ac63-6978-4691-bd2a-991e2c0072fb", "logoImage": "https://beta-images.hotelinking.com/brands/6ab5ac63-6978-4691-bd2a-991e2c0072fb/images/logo/original-1582043253041.jpg", "documents": [3], "documentsOptionalBrand": [4], "awsAuthConfig": {"userPoolId": "eu-west-1_BdsYImVI5", "userPoolWebClientId": "53ci3uf01eu1eqiv7vannl64np", "identityPoolId": "eu-west-1:94df4d2e-ef62-4dcc-98df-b04cd5f10192", "region": "eu-west-1", "mandatorySignIn": true, "signUpVerificationMethod": "link"}}, "prod": {"brandId": 304, "optionalBrandId": 304, "childBrandId": 100, "guid": "6ab5ac63-6978-4691-bd2a-991e2c0072fb", "logoImage": "https://beta-images.hotelinking.com/brands/6ab5ac63-6978-4691-bd2a-991e2c0072fb/images/logo/original-1582043253041.jpg", "documents": [3], "documentsOptionalBrand": [4], "awsAuthConfig": {"userPoolId": "eu-west-1_8Vafhocmp", "userPoolWebClientId": "7teseb7vk3h9679s3mltnjnehh", "identityPoolId": "eu-west-1:913ab445-c95e-442f-8ef8-24d30a5faa42", "region": "eu-west-1", "mandatorySignIn": true, "signUpVerificationMethod": "link"}}, "errorReservationId": "ErrorTest", "errorReservationName": "ErrorTest", "completedReservationId": "02<PERSON><PERSON>king", "completedReservationName": "mart<PERSON>z", "completedReservationDate": "01-05-2021", "completedReservationCheckinDate": "19-11-2020", "partialReservationId": "TESTLOCALIZER7", "partialReservationName": "Surname", "partialReservationCheckinDate": "10-10-2021", "futureReservationId": "TESTFUTURE", "futureReservationName": "Test", "futureReseravatonCheckinDate": "01-01-2040", "allPaxReservationId": "PAX-TEST", "allPaxReservationName": "Pax", "allPaxReservationCheckinDate": "12-12-2022", "multipleRoomsReservationId": "MULTIPLE-TEST", "multipleRoomsReservationName": "Multiple", "multipleRoomsReservationCheckinDate": "15-12-2022", "multipleLocalizersReservationId": "reserv8", "multipleLocalizersReservationName": "<PERSON><PERSON><PERSON><PERSON>", "multipleLocalizersReservationCheckinDate": "16-02-2023", "multipleLocalizersReservationCheckoutDate": "21-02-2023", "multipleLocalizersReservationFirstName": "<PERSON><PERSON><PERSON>", "cognitoUser": "<EMAIL>", "cognitoPassword": "Temporal123"}}