const plugins = [
  "@babel/plugin-proposal-optional-chaining",
  [
    "transform-imports",
    {
      lodash: {
        transform: "lodash/${member}",
        preventFullImport: true
      }
    }
  ]
];

if (process.env.NODE_ENV === "testing") {
  plugins.push([
    "babel-plugin-istanbul",
    {
      extension: [".js", ".vue"]
    }
  ]);
}

module.exports = {
  presets: ["@vue/cli-plugin-babel/preset"],
  plugins
};
