---
title: Bitbucket pipeline
layout: layout.html
eleventyNavigation:
  key: Bitbucket pipeline
---

# Bitbucket pipeline

As in all projects, we use bitbucket pipelines to have continuous integration, however this one has some particularities that should be commented on.

Initially we had the problem that the pipeline took too long (more than 15 minutes) and slowed down our continuous delivery. To avoid this, parallel steps have been used to run the unit tests, the application build and each e2e test individually at the same time.

## Git Hooks

Obviously we have to add the e2e tests one by one in the pipeline. To avoid problems of forgetting to add them, a pre-push git-hook has been added thanks to [<PERSON><PERSON>](https://typicode.github.io/husky/#/).

When we try to push the branch it will read all the files we have under the /tests/e2e folder and compare them with the tests defined in the bitbucket pipeline. If we notice that one of them is missing it will not allow us to push until it is added.
