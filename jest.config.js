module.exports = {
	preset: "@vue/cli-plugin-unit-jest",
	moduleFileExtensions: ["js", "ts", "json", "vue"],
	transform: {
		"^.+\\.svg": "<rootDir>/svgTransform.js",
	},
	moduleNameMapper: {
		"\\.(css|less|scss|sass|jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
			"<rootDir>/mocks/fileMock.js",
	},
	verbose: true,
	setupFilesAfterEnv: ["<rootDir>/tests/unit/setup.js"],
};
