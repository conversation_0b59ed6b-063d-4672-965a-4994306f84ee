<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>Check in Online</title>
    <% if (VUE_APP_ENV === 'dev') { %>
    <script>
      (function(n, i, v, r, s, c, x, z) {
        x = window.AwsRumClient = { q: [], n: n, i: i, v: v, r: r, c: c };
        window[n] = function(c, p) {
          x.q.push({ c: c, p: p });
        };
        z = document.createElement("script");
        z.async = true;
        z.src = s;
        document.head.insertBefore(
          z,
          document.getElementsByTagName("script")[0]
        );
      })(
        "cwr",
        "87ecc427-9e83-4e31-909b-322469c5c884",
        "1.0.0",
        "eu-west-1",
        "https://client.rum.us-east-1.amazonaws.com/1.0.2/cwr.js",
        {
          sessionSampleRate: 1,
          guestRoleArn:
            "arn:aws:iam::060632736596:role/RUM-Monitor-eu-west-1-060632736596-3176370728361-Unauth",
          identityPoolId: "eu-west-1:18e96138-dff5-4e59-a890-f2dcad3343bd",
          endpoint: "https://dataplane.rum.eu-west-1.amazonaws.com",
          telemetries: [
            "performance",
            "errors",
            [
              "http",
              {
                urlsToExclude: [
                  /googleanalytics\.com|doubleclick\.net|google-analytics\.com|amazonaws\.com|hotjar\.com|hotjar\.io/,
                ],
                addXRayTraceIdHeader: true,
              },
            ],
          ],
          allowCookies: true,
          enableXRay: true,
        }
      );
    </script>
    <% } %> <% if (VUE_APP_ENV === 'beta') { %>
    <script>
      (function(n, i, v, r, s, c, x, z) {
        x = window.AwsRumClient = { q: [], n: n, i: i, v: v, r: r, c: c };
        window[n] = function(c, p) {
          x.q.push({ c: c, p: p });
        };
        z = document.createElement("script");
        z.async = true;
        z.src = s;
        document.head.insertBefore(
          z,
          document.getElementsByTagName("script")[0]
        );
      })(
        "cwr",
        "7c9a4a99-c068-460c-ba59-116841b6cafb",
        "1.0.0",
        "eu-west-1",
        "https://client.rum.us-east-1.amazonaws.com/1.0.2/cwr.js",
        {
          sessionSampleRate: 1,
          guestRoleArn:
            "arn:aws:iam::060632736596:role/RUM-Monitor-eu-west-1-060632736596-3795582409361-Unauth",
          identityPoolId: "eu-west-1:7a8a52a8-b463-4ae5-88b5-52c452ae1264",
          endpoint: "https://dataplane.rum.eu-west-1.amazonaws.com",
          telemetries: [
            "performance",
            "errors",
            [
              "http",
              {
                urlsToExclude: [
                  /googleanalytics\.com|doubleclick\.net|google-analytics\.com|amazonaws\.com|hotjar\.com|hotjar\.io/,
                ],
                addXRayTraceIdHeader: true,
              },
            ],
          ],
          allowCookies: true,
          enableXRay: true,
        }
      );
    </script>
    <% } %> <% if (VUE_APP_ENV === 'prod') { %>
    <script>
      // (function(n, i, v, r, s, c, x, z) {
      //   x = window.AwsRumClient = { q: [], n: n, i: i, v: v, r: r, c: c };
      //   window[n] = function(c, p) {
      //     x.q.push({ c: c, p: p });
      //   };
      //   z = document.createElement("script");
      //   z.async = true;
      //   z.src = s;
      //   document.head.insertBefore(
      //     z,
      //     document.getElementsByTagName("script")[0]
      //   );
      // })(
      //   "cwr",
      //   "a08e12c6-5650-4c0b-8f61-bfea81bd5f60",
      //   "1.0.0",
      //   "eu-west-1",
      //   "https://client.rum.us-east-1.amazonaws.com/1.0.2/cwr.js",
      //   {
      //     sessionSampleRate: 1,
      //     guestRoleArn:
      //       "arn:aws:iam::111343607835:role/RUM-Monitor-eu-west-1-111343607835-8897442609361-Unauth",
      //     identityPoolId: "eu-west-1:55fe915c-f9a2-4fae-a13d-1f807918d2d8",
      //     endpoint: "https://dataplane.rum.eu-west-1.amazonaws.com",
      //     telemetries: [
      //       "performance",
      //       "errors",
      //       [
      //         "http",
      //         {
      //           urlsToExclude: [
      //             /googleanalytics\.com|doubleclick\.net|google-analytics\.com|amazonaws\.com|lr-in-prod\.com|hotjar\.com|hotjar\.io/,
      //           ],
      //           addXRayTraceIdHeader: true,
      //         },
      //       ],
      //     ],
      //     allowCookies: true,
      //     enableXRay: true,
      //   }
      // );
    </script>
    <% } %>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
