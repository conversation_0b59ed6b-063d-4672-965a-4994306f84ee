import integrationRepository from "../../../src/repository/integration/integrationRepository"
import { API } from "@aws-amplify/api";
import Reservation from "@/entities/Reservation";
import { defaultReservation, multipleReservations } from "../../../mocks/modules/integration/data";

jest.mock("@aws-amplify/api")

describe("getReservation", () => {
  const brandId = "80";
  const commonParams = { cache: true, manual: true, reservation_code: "hello" };

  afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
		jest.restoreAllMocks()
	});

  it("should return a single reservation when it receives a single reservation with check_state 'none' ", async () => {

    const modifiedReservation = {
      data: [
        {...defaultReservation.data[0], 
        check_state: "none" 
        }
      ]
    }

    API.get.mockResolvedValue(modifiedReservation);

    const result = await integrationRepository.getReservation(brandId, commonParams);
    const reservation = result[0]

    expect(reservation).toBeInstanceOf(Reservation);
    expect(result.length).toBe(1);
    
  });

  it("should return a single reservation when it receives a single reservation with check_state null and booking_state is eligible", async () => {

    const modifiedReservation = {
      data: [
        {...defaultReservation.data[0], 
        check_state: null,
        booking_state: 'reserved' 
        }
      ]
    }

    API.get.mockResolvedValue(modifiedReservation);

    const result = await integrationRepository.getReservation(brandId, commonParams);
    const reservation = result[0]

    expect(reservation).toBeInstanceOf(Reservation);
    expect(result.length).toBe(1);
    
  });

  it("should return an object with action redirect when it receives a single reservation with check_state that indicates guest has already done the checkin", async () => {

    const handleNotEligibleSpy = jest.spyOn(integrationRepository, 'handleNotEligible');

    const modifiedReservation = {
      data: [
        {...defaultReservation.data[0], 
        check_state: 'check_in',
        }
      ]
    }

    API.get.mockResolvedValue(modifiedReservation);

    const result = await integrationRepository.getReservation(brandId, commonParams);

    expect(handleNotEligibleSpy).toHaveBeenCalled();
    expect(result).toHaveProperty('reservation');
    expect(result).toHaveProperty('action', 'redirect');
    expect(result).toHaveProperty('checkState', 'check_in');
    
  });

  it("should return an object with action show modal when it receives an invalid reservation where the guest has already checked out", async () => {

    const handleNotEligibleSpy = jest.spyOn(integrationRepository, 'handleNotEligible');

    const modifiedReservation = {
      data: [
        {...defaultReservation.data[0], 
        check_state: 'check_out',
        }
      ]
    }

    API.get.mockResolvedValue(modifiedReservation);

    const result = await integrationRepository.getReservation(brandId, commonParams);

    expect(handleNotEligibleSpy).toHaveBeenCalled();
    expect(result).toHaveProperty('reservation');
    expect(result).toHaveProperty('action', 'show modal');
    expect(result).toHaveProperty('checkState', 'check_out');
    
  });

  it("should return an object with action show modal when it receives an invalid reservation where the booking state is not eligible", async () => {

    const handleNotEligibleSpy = jest.spyOn(integrationRepository, 'handleNotEligible');

    const modifiedReservation = {
      data: [
        {...defaultReservation.data[0], 
        check_state: null,
        booking_state: 'cancelled'
        }
      ]
    }

    API.get.mockResolvedValue(modifiedReservation);

    const result = await integrationRepository.getReservation(brandId, commonParams);

    expect(handleNotEligibleSpy).toHaveBeenCalled();
    expect(result).toHaveProperty('reservation');
    expect(result).toHaveProperty('action', 'show modal');
    
  });

  it("should return multiple eligible reservations when it receives multiple eligible reservations", async () => {

    const filterEligibleReservationsSpy = jest.spyOn(integrationRepository, 'filterEligibleReservations');
    
    const modifiedReservations = {
      data: [
        { ...multipleReservations.data[0], 
          check_state: 'none' 
        },
        { ...multipleReservations.data[1], 
          check_state: null,
          booking_state: 'none'
        },
        { ...multipleReservations.data[2],
          check_state: null, 
          booking_state: 'reserved'
        },
      ]
    }

    API.get.mockResolvedValue(modifiedReservations);

    const result = await integrationRepository.getReservation(brandId, commonParams);

    expect(filterEligibleReservationsSpy).toHaveBeenCalled();
    expect(result.length).toBe(3);

    result.forEach((item) => {
      expect(item).toBeInstanceOf(Reservation);
    });
  });

  it("should return only the eligible reservations when it receives multiple reservations", async () => {

    const filterEligibleReservationsSpy = jest.spyOn(integrationRepository, 'filterEligibleReservations');
    
    const modifiedReservations = {
      data: [
        { ...multipleReservations.data[0], 
          check_state: 'none' 
        },
        { ...multipleReservations.data[1], 
          check_state: null,
          booking_state: 'none'
        },
        { ...multipleReservations.data[2],
          check_state: 'check_out', 
          booking_state: 'reserved'
        },
      ]
    }

    API.get.mockResolvedValue(modifiedReservations);

    const result = await integrationRepository.getReservation(brandId, commonParams);
    
    expect(filterEligibleReservationsSpy).toHaveBeenCalled();
    expect(result.length).toBe(2);

    result.forEach((item) => {
      expect(item).toBeInstanceOf(Reservation);
    });
  });

  it("should handle the first invalid reservation when it receives multiple invalid reservations with the same localizer", async () => {

    const filterEligibleReservationsSpy = jest.spyOn(integrationRepository, 'filterEligibleReservations');

    const handleNotEligibleSpy = jest.spyOn(integrationRepository, 'handleNotEligible');
    
    const modifiedReservations = {
      data: [
        { ...multipleReservations.data[0], 
          check_state: 'check_out',
          res_localizer: 'testLocalizer' 
        },
        { ...multipleReservations.data[1], 
          check_state: null,
          booking_state: 'cancelled',
          res_localizer: 'testLocalizer' 
        },
      ]
    }

    API.get.mockResolvedValue(modifiedReservations);

    const result = await integrationRepository.getReservation(brandId, commonParams);
    
    expect(filterEligibleReservationsSpy).toHaveBeenCalled();
    expect(handleNotEligibleSpy).toHaveBeenCalled();
    expect(result).toHaveProperty('checkState', 'check_out');
    expect(result).toHaveProperty('action', 'show modal');
  });

  it("should return all the reservations when it receives multiple invalid reservations with the different localizer", async () => {

    const filterEligibleReservationsSpy = jest.spyOn(integrationRepository, 'filterEligibleReservations');

    const handleNotEligibleSpy = jest.spyOn(integrationRepository, 'handleNotEligible');
    
    const modifiedReservations = {
      data: [
        { ...multipleReservations.data[0], 
          check_state: 'check_out',
          res_localizer: 'testLocalizer' 
        },
        { ...multipleReservations.data[1], 
          check_state: null,
          booking_state: 'cancelled',
          res_localizer: 'testLocalizer2' 
        },
      ]
    }

    API.get.mockResolvedValue(modifiedReservations);

    const result = await integrationRepository.getReservation(brandId, commonParams);
    
    expect(filterEligibleReservationsSpy).toHaveBeenCalled();
    expect(handleNotEligibleSpy).toHaveBeenCalled();
    expect(result.length).toBe(2);
  });

});