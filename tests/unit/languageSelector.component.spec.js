import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import { i18n } from "@/locales";
import languageSelector from "@/components/shared/languageSelector.component.vue";
import queryParams from "@/store/queryParams.js";
import loading from "@/store/loading.js";
import app from "@/store/app";
import brand from "@/store/brand";
import router from "@/router";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("languageSelector component", () => {
	let store;

	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				queryParams,
				loading,
				app,
				brand,
			},
		});
	});

	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
	});

	it("should change language based on query parameter", async () => {
		await router.push({ query: { lang: "pt" } });

		const wrapper = mount(languageSelector, {
			localVue,
			store,
			i18n,
			router,
		});

		expect(store.state.queryParams.data.lang).toBe("pt");
		expect(wrapper.vm.$root.$i18n.locale).toBe("pt");
	});

	it("should default to english if the query parameter is not an available language", async () => {
		await router.push({ query: { lang: "nl" } });

		const wrapper = mount(languageSelector, {
			localVue,
			store,
			i18n,
			router,
		});

		expect(store.state.queryParams.data.lang).toBe("nl");
		expect(wrapper.vm.$root.$i18n.locale).toBe("en");
	});
});
