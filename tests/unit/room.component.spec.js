import roomComponent from "@/components/selectRoom/room.component.vue";
import { shallowMount } from "@vue/test-utils";
import { i18n } from "@/locales";

describe("RoomComponent", () => {
  it("Should render name of the room", () => {
    const wrapper = shallowMount(roomComponent, {
      propsData: {
        roomType: "Double",
        roomCompleted: false
      },
      mocks: {
        $store: {
          state: {
            brand: {
              mainColor: "123"
            }
          }
        }
      },
      i18n
    });
    expect(wrapper.find(".document-button").exists()).toBe(true);
    expect(wrapper.find(".uppercase").text()).toBe("room.Double");
  });

  it("Should render name of the room differnt", () => {
    const wrapper = shallowMount(roomComponent, {
      propsData: {
        roomType: "Triple",
        roomCompleted: true
      },
      mocks: {
        $store: {
          state: {
            brand: {
              mainColor: "123"
            }
          }
        }
      },
      i18n
    });
    expect(wrapper.find(".document-button").exists()).toBe(true);
    expect(wrapper.find(".uppercase").text()).toBe("room.Triple");
  });
});
