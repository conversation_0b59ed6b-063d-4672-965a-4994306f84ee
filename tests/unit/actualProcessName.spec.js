import { shallowMount, createLocalVue } from "@vue/test-utils";
import actualProcessName from "@/components/shared/actualProcessName.component.vue";
//import guest from "@/store/guest";

import Vuex from "vuex";
import { i18n } from "@/locales";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("Actual Process Name", () => {
  it("Should render guest name", () => {
    const wrapper = shallowMount(actualProcessName, {
      localVue,
      i18n,
      mocks: {
        $store: {
          state: {
            guest: {
              data: {
                name: "Testing"
              }
            }
          }
        }
      }
    });

    expect(wrapper.find(".actual-process-name").exists()).toBe(true);

    expect(wrapper.find('[data-test="guest-name"]').text()).toBe("Testing");
  });

  it("Should render N/A if guest name not set", () => {
    const wrapper = shallowMount(actualProcessName, {
      localVue,
      i18n,
      mocks: {
        $store: {
          state: {
            guest: {
              data: {
                name: null
              }
            }
          }
        }
      }
    });

    expect(wrapper.find(".actual-process-name").exists()).toBe(true);

    expect(wrapper.find('[data-test="guest-name"]').text()).toBe("N/A");
  });
});
