import statusComponent from "@/components/status/status.component.vue";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import brand from "@/store/brand";
import Vuex from "vuex";
import { i18n } from "@/locales";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("StatusComponent", () => {
  let store;
  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            mainColor: "#FFFFFFA",
            config: {
              partial_checkin: true,
              show_holder: true
            }
          }
        }
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it("Should render adult when pax is AD", () => {
    const pax = {
      pax_type: "AD"
    };
    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    expect(wrapper.find('[data-test="adult"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="child"]').exists()).toBe(false);
  });

  it("Should render junior when pax is JR", () => {
    const pax = {
      pax_type: "JR"
    };
    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    expect(wrapper.find('[data-test="junior"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="adult"]').exists()).toBe(false);
  });

  it("Should render child when pax is CH", () => {
    const pax = {
      pax_type: "CH"
    };
    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    expect(wrapper.find('[data-test="child"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="adult"]').exists()).toBe(false);
  });

  it("Should render baby when pax is BB", () => {
    const pax = {
      pax_type: "BB"
    };
    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    expect(wrapper.find('[data-test="baby"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="adult"]').exists()).toBe(false);
  });

  it("Should render adult when pax is null", () => {
    const pax = {
      pax_type: null
    };
    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    expect(wrapper.find('[data-test="adult"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="child"]').exists()).toBe(false);
  });

  it("Should render validate pax", () => {
    const pax = {
      first_name: "Pax_Name",
      last_name: "Pax_Last_Name",
      full_name: "Pax_Name Pax_Last_Name",
      pax_type: "AD",
      validated: true,
      processCompleted: false
    };
    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    const spanAD = wrapper.find('[data-test="pax-validated"]');

    expect(spanAD.exists()).toBe(true);
    expect(spanAD.text()).toBe(`${pax.first_name} ${pax.last_name}`);
    expect(
      wrapper.find('[data-test="pax-no-validated-with-name"]').exists()
    ).toBe(false);
    expect(
      wrapper.find('[data-test="pax-no-validated-with-name"]').exists()
    ).toBe(false);
    expect(wrapper.find('[data-test="child"]').exists()).toBe(false);
  });

  it("Should render pax not validated and not name", () => {
    const pax = {
      first_name: "",
      last_name: "",
      full_name: "",
      pax_type: "AD",
      validated: false,
      processCompleted: false
    };

    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    const spanAD = wrapper.find('[data-test="pax-no-validated"]');
    expect(spanAD.exists()).toBe(true);

    expect(spanAD.text()).toBe("Available");

    expect(wrapper.find('[data-test="pax-validated"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="child"]').exists()).toBe(false);
  });

  it("Should render pax not validated with name", () => {
    const pax = {
      first_name: "Pax_Name",
      last_name: "Pax_Last_Name",
      full_name: "Pax_Name Pax_Last_Name",
      pax_type: "AD",
      validated: false,
      processCompleted: false
    };

    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    const spanAD = wrapper.find('[data-test="pax-no-validated"]');
    expect(spanAD.exists()).toBe(true);

    expect(spanAD.text()).toBe(`${pax.first_name} ${pax.last_name}`);

    expect(wrapper.find('[data-test="pax-validated"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="child"]').exists()).toBe(false);
  });

  it("Should show holder if pax is holder", () => {
    const pax = {
      first_name: "",
      last_name: "",
      full_name: "",
      pax_type: "AD",
      validated: false,
      processCompleted: false,
      holder: true
    };

    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    const holder = wrapper.find('[data-test="holder"]');

    expect(holder.exists()).toBe(true);
    expect(holder.text()).toBe("Adult  - Reservation holder");
  });

  it("Should not show holder if pax is not holder", () => {
    const pax = {
      first_name: "",
      last_name: "",
      full_name: "",
      pax_type: "AD",
      validated: false,
      processCompleted: false,
      holder: false
    };

    const wrapper = shallowMount(statusComponent, {
      propsData: {
        pax: pax
      },
      store,
      localVue,
      i18n
    });

    const holder = wrapper.find('[data-test="holder"]');

    expect(holder.exists()).toBe(true);
    expect(holder.text()).toBe("Adult");
  });
});
