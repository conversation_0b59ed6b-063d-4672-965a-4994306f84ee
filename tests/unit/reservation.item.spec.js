import { shallowMount } from "@vue/test-utils";
import reservationItem from "@/components/reservation/Reservation.item.component";
import bed from "@/assets/images/icons/bed.svg";
describe("Reservation item component", () => {
  const wrapper = shallowMount(reservationItem, {
    propsData: {
      label: "test",
      name: "test",
      icon: "bed",
      linkName: "link"
    },
    components: { bed },
    mocks: {
      $store: {
        state: {
          brand: {
            mainColor: "123"
          }
        }
      }
    }
  });

  it("should render component", () => {
    expect(wrapper.is(reservationItem)).toBe(true);
  });
  it("if label is given, component should render it", () => {
    expect(wrapper.find("span").text()).toBe("test");
  });

  // });
  it("if linkname is not given, anchor should not be rendered", () => {
    const wrapper = shallowMount(reservationItem, {
      propsData: {
        label: "test",
        name: "test",
        icon: "bed"
      },
      components: { bed },
      mocks: {
        $store: {
          state: {
            brand: {
              mainColor: "123"
            }
          }
        }
      }
    });
    expect(wrapper.find("a")).not.toBe(true);
  });
  it("if anchor is click, an event is emitted", async () => {
    const wrapper = shallowMount(reservationItem, {
      propsData: {
        label: "test",
        name: "test",
        icon: "bed",
        linkName: "link"
      },
      components: { bed },
      mocks: {
        $store: {
          state: {
            brand: {
              mainColor: "123"
            }
          }
        }
      }
    });

    const anchor = wrapper.find("a");
    await anchor.trigger("click");
    expect(wrapper.emitted).toBeTruthy();
  });
});
