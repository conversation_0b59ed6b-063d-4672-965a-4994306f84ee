import { shallowMount } from "@vue/test-utils";
import Documents from "@/components/documents/documents.component.vue";
import documentSign from "@/assets/images/icons/document-sign.svg";
import completed from "@/assets/images/icons/completed.svg";

describe("Documents component", () => {
  it("should render active", () => {
    const wrapper = shallowMount(Documents, {
      propsData: {
        document: {
          title: "test",
          read: false
        }
      },
      mocks: {
        $store: {
          state: {
            brand: {
              mainColor: "123"
            }
          }
        }
      }
    });
    const sign = wrapper.findAllComponents(documentSign).at(0);
    expect(sign.exists()).toBeTruthy();
    // const completed = wrapper.findAllComponents(completed).at(0);
    // expect(completed.exists()).toBeFalsy();
    expect(
      Documents.computed.isRead.call({ document: { read: true } })
    ).toEqual(true);
    expect(
      Documents.computed.isRead.call({ document: { read: false } })
    ).toEqual(false);
  });
  it("should render documents already completed", () => {
    const wrapper = shallowMount(Documents, {
      propsData: {
        document: {
          title: "test",
          read: true
        }
      },
      mocks: {
        $store: {
          state: {
            brand: {
              mainColor: "123"
            }
          }
        }
      }
    });
    const comp = wrapper.findAllComponents(completed).at(0);
    expect(comp.exists()).toBeTruthy();
  });
});
