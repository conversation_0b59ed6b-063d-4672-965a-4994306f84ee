import { TrackerProvider } from "@/tracker";

TrackerProvider.prototype.record = jest
	.fn()
	.mockImplementation(async (params) => {
		const mockResponse = { success: true };
		return Promise.resolve(mockResponse);
	});

describe("TrackerProvider setup.js mock test", () => {
	const trackerParams = {
		source: "autocheckin",
		streamName: "x",
		brand_id: "76",
	};
	it("should return response set on setup.js", async () => {
		const trackerProvider = new TrackerProvider(trackerParams);
		const event = {};

		const result = await trackerProvider.record(event);

		expect(trackerProvider.record).toHaveBeenCalledTimes(1);
		expect(trackerProvider.record).toBeCalledWith({});
		//Response we get is set on setup.js file for every unit test in the project
		expect(result).toEqual({ success: true });
	});

	it("should ovverride setup.js mock", async () => {
		TrackerProvider.prototype.record = jest
			.fn()
			.mockImplementation(async (params) => {
				return Promise.resolve({ success: false });
			});

		const trackerProvider = new TrackerProvider(trackerParams);
		const event = {};
		const result = await trackerProvider.record(event);

		expect(result).toEqual({ success: false });
	});
});
