import CheckinCounter from "@/components/confirmation/CheckinCounter.component";
import { mount, createLocalVue } from "@vue/test-utils";
import { i18n } from "@/locales";
import Vuex from "vuex";
import guestModule from "@/store/guest";

const localVue = createLocalVue();
localVue.use(Vuex);
describe("Confirmation checkin counter", () => {
  let store;
  let wrapper;
  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        guest: {
          ...guestModule,
          state: {
            list: [
              { processCompleted: true },
              { processCompleted: true },
              { processCompleted: false },
              { processCompleted: false }
            ]
          }
        }
      }
    });

    wrapper = mount(CheckinCounter, {
      localVue,
      store,
      i18n
    });
  });

  it("Renders the component", () => {
    expect(wrapper.exists()).toBe(true);
  });

  it("Component shows correct count of checkedGuests and totalGuests", async () => {
    expect(wrapper.find('[data-test="checkinCounter"]').text()).toContain(
      "2 Out of 4"
    );
  });
});
