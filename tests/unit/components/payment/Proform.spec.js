import Proform from "@/components/payment/proform.component.vue";
import { mount, createLocalVue } from "@vue/test-utils";
import { sanetizedCharges } from "../../../../mocks/modules/integration/data.js";
import { i18n } from "@/locales";
import Vuex from "vuex";
import paymentModule from "@/store/payment.js";

const localVue = createLocalVue();
localVue.use(Vuex);
describe("Payment proform", () => {
  let store;
  let wrapper;
  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        payment: {
          ...paymentModule,
          state: {
            charges: sanetizedCharges
          }
        }
      }
    });

    wrapper = mount(Proform, {
      localVue,
      store,
      i18n,
      propsData: {
        reservations: sanetizedCharges.reservations
      },
      currency: "EUR"
    });
  });

  it("Renders the component", () => {
    expect(wrapper.exists()).toBe(true);
  });

  it("Contract data is rendered", async () => {
    expect(wrapper.findAll('[data-test="product-item"]').length).toBe(2);

    expect(wrapper.find('[data-test="total"]').text()).toContain(450);
    expect(wrapper.find('[data-test="paid"]').text()).toContain(0);
    expect(wrapper.find('[data-test="remain"]').text()).toContain(450);
  });
});
