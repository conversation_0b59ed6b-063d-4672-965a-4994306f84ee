import guestItemComponent from "@/components/reservation/Guest.item.component.vue";
import brand from "@/store/brand";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import Vuex from "vuex";
import { i18n } from "@/locales";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("GuestItem component", () => {
  let store;
  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            mainColor: "#FFFFFFA",
            config: {
              partial_checkin: true
            }
          }
        }
      }
    });
  });

  it("Should render validated adult", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "AD",
      validated: true
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="adult-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="child-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render validated junior", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "JR",
      validated: true
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="junior-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="adult-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render validated child", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "CH",
      validated: true
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="child-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="adult-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render validated baby", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "BB",
      validated: true
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="baby-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="adult-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render validated adult when pax_type is null and is validated", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: null,
      validated: true
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="adult-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="child-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render NOT validated adult", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "AD",
      validated: false
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="adult-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="child-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render NOT validated junior", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "JR",
      validated: false
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="junior-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="adult-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render NOT validated child", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "CH",
      validated: false
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="child-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="adult-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render NOT validated baby", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: "BB",
      validated: false
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="baby-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="adult-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });

  it("Should render NOT validated adult when pax_type is null and is NOT validated", () => {
    const guest = {
      full_name: "Name Surname",
      pax_type: null,
      validated: false
    };
    const wrapper = shallowMount(guestItemComponent, {
      propsData: {
        guest: guest
      },
      store,
      localVue,
      i18n
    });
    expect(wrapper.find('[data-test="adult-guest-item"]').isVisible()).toBe(
      true
    );
    expect(wrapper.find('[data-test="child-guest-item"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="guest-name"]').text()).toBe(
      "Name Surname"
    );
  });
});
