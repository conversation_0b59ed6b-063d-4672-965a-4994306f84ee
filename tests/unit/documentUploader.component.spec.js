import { mount, createLocalVue } from "@vue/test-utils";
import flushPromises from "flush-promises";
import documentUploader from "@/components/scan/documentUploader.component.vue";
import { i18n } from "@/locales";
import brand from "@/store/brand";
import Vuex from "vuex";

const localVue = createLocalVue();
localVue.use(Vuex);
describe("Document Uploader component", () => {
	let store;
	let wrapper;
	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						mainColor: "#FFFFFFA",
						config: {
							allow_driving_license: true,
						},
					},
				},
			},
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		wrapper = mount(documentUploader, {
			propsData: {
				type: "front",
				uploadedDocument: null,
			},
			store,
			localVue,
			i18n,
		});
	});

	it("Renders correctly", () => {
		expect(wrapper.exists()).toBe(true);
	});

	it("If no documents are uploaded, only 'scan' button should be present", async () => {
		expect(wrapper.find('[data-test="uploadDocument"]').exists()).toBe(true);
		expect(wrapper.find('[data-test="deleteDocument"]').exists()).toBe(false);
	});

	it("If scan button is clicked, handleClickEvent is emitted", async () => {
		wrapper.find('[data-test="uploadDocument"]').trigger("click");
		await flushPromises();
		expect(wrapper.emitted("handleClickEvent")).toBeTruthy();
	});

	describe("When document is uploaded", () => {
		beforeEach(() => {
			store = new Vuex.Store({
				modules: {
					brand: {
						...brand,
						state: {
							mainColor: "#FFFFFFA",
							config: {
								identity_document_signature_required: false,
							},
						},
					},
				},
			});

			afterEach(() => {
				jest.clearAllMocks();
			});

			wrapper = mount(documentUploader, {
				propsData: {
					type: "front",
					uploadedDocument: {
						data: {
							document_type: "identity_card",
							document_subtype: "D",
							side: "front",
							nationality: "ESP",
							document_number: "99999999R",
							surname: "Española",
							name: "Carmen",
							birthday_date: "1980-01-01T00:00:00",
							place_of_birth: null,
							gender: "female",
							date_of_issue: "2015-01-01T00:00:00",
							date_of_expiry: "2025-01-01T00:00:00",
							issuing_country: null,
							issuing_institution: null,
							residence_country: null,
							address: {
								street_name: null,
								house_number: null,
								postcode: null,
								province: null,
							},
							signature: "",
							second_surname: "Española",
						},
						title: "identity_card_front",
						content: "ALONGBASE64",
						extension: "jpeg",
					},
				},
				store,
				localVue,
				i18n,
			});
		});

		it("Both 'Scan again' and 'Delete' buttons should exist", async () => {
			expect(wrapper.find('[data-test="uploadDocument"]').exists()).toBe(true);
			expect(wrapper.find('[data-test="deleteDocument"]').exists()).toBe(true);
		});

		it("If delete button is clicked, handleDeleteDocumentEvent should be emmited", async () => {
			wrapper.find('[data-test="deleteDocument"]').trigger("click");
			await flushPromises();
			expect(wrapper.emitted("handleDeleteDocumentEvent")).toBeTruthy();
			/*
    wrapper.emitted() returns the following object:
    {
      foo: [[123]]
    }
    */

			expect(wrapper.emitted("handleDeleteDocumentEvent")).toEqual([
				[
					{
						documentType: "identity_card_front",
					},
				],
			]);
		});

		it("If document iamge is clicked, handleShowDocumentEvent should be emmited", async () => {
			wrapper.find('[data-test="showDocumentModal"]').trigger("click");
			await flushPromises();
			expect(wrapper.emitted("handleShowDocumentEvent")).toBeTruthy();
			/*
    wrapper.emitted() returns the following object:
    {
      foo: [[123]]
    }
    */

			expect(wrapper.emitted("handleShowDocumentEvent")).toEqual([
				[
					{
						image: "data:image/jpeg;base64,ALONGBASE64",
					},
				],
			]);
		});
	});
});
