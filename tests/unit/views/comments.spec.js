import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import { i18n } from "@/locales";
import Comments from "@/views/Comments.vue";
import brandModule from "@/store/brand";
import { guestData } from "../../../mocks/data/guestData";
import guestModule from "@/store/guest";
import loading from "@/store/loading";
import { commentsConfig } from "../../../mocks/modules/brand/data";
import redirectMixin from "@/mixins/redirect";
import router from "@/router";
import repository from "@/repository/repositoryFactory";
import VueRouter from "vue-router";
import flushPromises from "flush-promises";

const localVue = createLocalVue();
localVue.use(VueRouter);
localVue.use(Vuex);

describe("Comments Page", () => {
	let store;
	let wrapper;
	let redirect;
	beforeEach(() => {
		redirect = jest.spyOn(redirectMixin.methods, "redirect");
		store = new Vuex.Store({
			modules: {
				brand: {
					...brandModule,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: commentsConfig.data,
					},
				},
				guest: {
					...guestModule,
					getters: {
						getSelectedGuest: () => {
							return guestData[0];
						},
					},
				},
				loading,
			},
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("If the holder is doing the process and show_comments_only_on_holder is true, then the comments page should be displayed", async () => {
		store.state.guest = {
			...guestModule,
			getters: { getSelectedGuest: () => guestData[0] },
		};
		wrapper = shallowMount(Comments, {
			store,
			localVue,
			i18n,
		});
		expect(wrapper.is(Comments)).toBe(true);
	});

	describe("Not holder process", () => {
		beforeEach(() => {
			redirect = jest.spyOn(redirectMixin.methods, "redirect");
			store = new Vuex.Store({
				modules: {
					brand: {
						...brandModule,
						state: {
							brandId: 1,
							mainColor: "123",
							country: "ES",
							config: commentsConfig.data,
						},
					},
					guest: {
						...guestModule,
						getters: {
							getSelectedGuest: () => {
								return guestData[1];
							},
						},
					},
					loading,
				},
			});
		});
		it("If not the holder, then comments view should not appear, and should instead redirect to confirmation view", async () => {
			store.state.guest = {
				...guestModule,
				getters: { getSelectedGuest: () => guestData[1] },
			};

			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});

			await localVue.nextTick();
			await new Promise(process.nextTick);
			expect(redirect).toBeCalledWith({ name: "Confirmation" });
			wrapper.destroy();
		});

		it("If comments config is active and arrival time is active, display both the comment section and the arrival time section", async () => {
			store.state.guest = {
				...guestModule,
				getters: { getSelectedGuest: () => guestData[1] },
			};

			store.state.brand.config.comments = true; 
    	store.state.brand.config.arrival_time = true; 

			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});

			await localVue.nextTick();
			
			expect(wrapper.find('[data-test="comment-section"]').exists()).toBe(true);
			expect(wrapper.find('[data-test="arrival-time-section"]').exists()).toBe(true);
			
			wrapper.destroy();
		});

		it("If comments config is active and arrival time is not active, display only the comment section", async () => {
			store.state.guest = {
				...guestModule,
				getters: { getSelectedGuest: () => guestData[1] },
			};

			store.state.brand.config.comments = true; 
    	store.state.brand.config.arrival_time = false; 

			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});

			await localVue.nextTick();
			
			expect(wrapper.find('[data-test="comment-section"]').exists()).toBe(true);
			expect(wrapper.find('[data-test="arrival-time-section"]').exists()).toBe(false);
			
			wrapper.destroy();
		});

		it("If comments config is not active and arrival time is active, display only the arrival time section", async () => {
			store.state.guest = {
				...guestModule,
				getters: { getSelectedGuest: () => guestData[1] },
			};

			store.state.brand.config.comments = false; 
    	store.state.brand.config.arrival_time = true; 

			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});

			await localVue.nextTick();
			
			expect(wrapper.find('[data-test="comment-section"]').exists()).toBe(false);
			expect(wrapper.find('[data-test="arrival-time-section"]').exists()).toBe(true);
			
			wrapper.destroy();
		});
	});

	describe("getCustomCommentsText", () => {
		let AutoCheckinApi;
		
		beforeEach(() => {
			AutoCheckinApi = repository.get("checkin");
		});

		it("should set commentsCustomText when API call is successful", async () => {
			AutoCheckinApi.getCustomizedText = jest.fn(() =>
				Promise.resolve({
					data: [
						{
							translations: [
								{
									text: "test comment text",
								},
							],
						},
					],
				})
			);

			const wrapper = shallowMount(Comments, {
				store,
				i18n,
				router,
			});

			await wrapper.vm.getCustomCommentsText();
			await flushPromises();

			expect(wrapper.vm.commentsCustomText).toBe("test comment text");
		});

		it("should set commentsCustomText to null when API call fails", async () => {
			AutoCheckinApi.getCustomizedText = jest.fn(() => Promise.reject(new Error("API Error")));

			const wrapper = shallowMount(Comments, {
				store,
				i18n,
				router,
			});

			await wrapper.vm.getCustomCommentsText();
			await flushPromises();

			expect(wrapper.vm.commentsCustomText).toBeNull();
		});

		it("should set commentsCustomText to null when response has no translations", async () => {
			AutoCheckinApi.getCustomizedText = jest.fn(() =>
				Promise.resolve({
					data: [
						{
							translations: [],
						},
					],
				})
			);

			const wrapper = shallowMount(Comments, {
				store,
				i18n,
				router,
			});

			await wrapper.vm.getCustomCommentsText();
			await flushPromises();

			expect(wrapper.vm.commentsCustomText).toBeNull();
		});
	});

	describe("Custom Comments Text behavior", () => {
		let getCustomCommentsText;

		beforeEach(() => {
			getCustomCommentsText = jest.spyOn(Comments.methods, "getCustomCommentsText");
		});

		it("should call getCustomCommentsText on created when config.custom_comments_text is true", async () => {
			store.state.brand.config.custom_comments_text = true;
			
			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});
			
			await flushPromises();
			
			expect(getCustomCommentsText).toHaveBeenCalled();
		});

		it("should not call getCustomCommentsText on created when config.custom_comments_text is false", async () => {
			store.state.brand.config.custom_comments_text = false;
			
			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});
			
			await flushPromises();
			
			expect(getCustomCommentsText).not.toHaveBeenCalled();
		});

		it("should call getCustomCommentsText when i18n locale changes and config.custom_comments_text is true", async () => {
			store.state.brand.config.custom_comments_text = true;

			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});

			await flushPromises();
			getCustomCommentsText.mockClear();

			i18n.locale = 'en';
			await flushPromises();

			i18n.locale = 'es';
			await flushPromises();

			expect(getCustomCommentsText).toHaveBeenCalled();
		});

		it("should not call getCustomCommentsText when i18n locale changes and config.custom_comments_text is false", async () => {
			store.state.brand.config.custom_comments_text = false;

			wrapper = shallowMount(Comments, {
				store,
				localVue,
				i18n,
				router,
			});

			await flushPromises();
			getCustomCommentsText.mockClear();

			i18n.locale = 'en';
			await flushPromises();
			
			i18n.locale = 'es';
			await flushPromises();

			expect(getCustomCommentsText).not.toHaveBeenCalled();
		});
	});
});
