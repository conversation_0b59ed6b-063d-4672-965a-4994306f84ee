import Vuex from "vuex";
import { mount, shallowMount, createLocalVue } from "@vue/test-utils";
import Status from "@/views/Status";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import guestModule from "@/store/guest";
import brandModule from "@/store/brand";
import appModule from "@/store/app";
import modal from "@/store/modal";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { defaultReservation } from "../../../mocks/modules/integration/data";
import { i18n } from "@/locales";
import {
  unorderedGuestList,
  orderedGuestList
} from "../../../mocks/data/guests";
import redirectMixin from "@/mixins/redirect";
import * as guestSort from "@/utils/guestSort";
const localVue = createLocalVue();

// need to import vuex
localVue.use(Vuex);

describe("Status View", () => {
  let store;
  let wrapper;
  let mockRouter;
  let guest;
  let brand;
  let reservations
  let app;
  const redirect = jest.spyOn(redirectMixin.methods, "redirect");
  beforeEach(async () => {
    mockRouter = {
      push: jest.fn(() => Promise.resolve()),
      currentRoute: {
        name: "Status",
        meta: {
          allowBack: true,
          backRoute: "Reservations"
        }
      }
    };
    brand = {
      ...brandModule,
      state: {
        brandId: 1,
        mainColor: "123",
        country: "ES",
        config: defaultConfig.data
      }
    };
    brand.state.config.show_holder = true;
    reservations = {
      ...reservationsModule,
      state: {
        data: defaultReservation.data,
        childAgeAttempts: 0,
        reservationSelected: {
          res_localizer: "234kj",
          res_adults: 1,
          res_children: 0,
          guests: unorderedGuestList
        },
        numberGuestSession: 1,
        multipleRooms: false
      }
    };
    guest = {
      ...guestModule,
      state: {
        list: [
          {
            first_name: "first_name",
            validated: true
          }
        ]
      }
    };
    app = {
      ...appModule
    };
    store = () => {
      return new Vuex.Store({
        modules: {
          reservations,
          guest,
          brand,
          loading,
          modal,
          app
        }
      });
    };
  });

  it("Confirmation modal pops up if user tries to go back to Reservations view and partial checkin is disabled, and data is kept if user decides to stay in the Status page", async () => {
    brand.state.config.partial_checkin = false;
    wrapper = mount(Status, {
      mocks: {
        $router: mockRouter
      },
      store,
      localVue,
      i18n,
      attachTo: document.body
    });

    const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
    const goBackBtn = wrapper.find('[data-test="go-back"]');
    const modal = wrapper.find('[data-test="go-back-to-reservations-modal"]');
    const closeModal = wrapper.find('[data-test="close-modal"]');
    const goBackToReservationsBtn = wrapper.find(
      '[data-test="go-back-to-reservations"]'
    );
    const guestsInSession = () =>
      wrapper.vm.$store.state.reservations.numberGuestSession;
    const guestList = wrapper.vm.$store.state.guest.list;

    await goBackBtn.trigger("click");
    expect(storeDispatch.mock.calls).toContainEqual([
      "modal/SET_NAME",
      "goBackToReservationsView"
    ]);
    await new Promise(r => setTimeout(r, 200)); // Wait for modal to show up
    expect(modal.isVisible()).toBe(true);

    await closeModal.trigger("click");
    // Expect RESET_SESSION_GUESTS action was NOT called
    expect(storeDispatch.mock.calls).not.toContainEqual([
      "reservations/RESET_SESSION_GUESTS",
      undefined
    ]);
    expect(guestsInSession()).toBe(1);
    expect(guestList.length).toBe(1);
    expect(redirect).not.toBeCalled();

    await goBackBtn.trigger("click");
    await goBackToReservationsBtn.trigger("click");
    await new Promise(r => setTimeout(r, 200));
    // Expect RESET_SESSION_GUESTS action was called
    expect(storeDispatch.mock.calls).toContainEqual([
      "reservations/RESET_SESSION_GUESTS",
      undefined
    ]);
    expect(guestsInSession()).toBe(0);
    expect(redirect).toBeCalledWith({ name: "Reservations" });
    wrapper.destroy();
  });

  it("Confirmation modal doesn't pops up if user tries to go back to Reservations view with partial checkin enabled", async () => {
    brand.state.config.partial_checkin = true;
    wrapper = mount(Status, {
      mocks: {
        $route: { name: "Reservations" },
        $router: mockRouter
      },
      store,
      localVue,
      i18n,
      attachTo: document.body
    });

    const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
    const goBackBtn = wrapper.find('[data-test="go-back"]');

    await goBackBtn.trigger("click");
    expect(storeDispatch.mock.calls).not.toContainEqual([
      "modal/SET_NAME",
      "goBackToReservationsView"
    ]);
    expect(wrapper.vm.$router.push).toBeCalledWith({ name: "Reservations" });
    wrapper.destroy();
  });

  it("OrderGuests method works properly", async () => {
    const orderGuests = Status.methods.orderGuests;
    jest.spyOn(guestSort, "sortByHolder");
    jest.spyOn(guestSort, "sortByValidated");
    jest.spyOn(guestSort, "sortByPaxType");
    jest.spyOn(guestSort, "sortByName");

    // Sort by holder
    expect(
      orderGuests([
        {
          validated: true,
          pms_id: "HUES-2"
        },
        {
          validated: true,
          pms_id: "HUES-1",
          holder: true
        },
        {
          validated: false,
          pms_id: "HUES-3"
        }
      ])
    ).toEqual([
      {
        validated: true,
        pms_id: "HUES-1",
        holder: true
      },
      {
        validated: true,
        pms_id: "HUES-2"
      },
      {
        validated: false,
        pms_id: "HUES-3"
      }
    ]);

    // Sort by validated
    expect(
      orderGuests([
        {
          validated: true,
          pms_id: "HUES-1"
        },
        {
          validated: false,
          pms_id: "HUES-3"
        },
        {
          validated: true,
          pms_id: "HUES-2"
        }
      ])
    ).toEqual([
      {
        validated: true,
        pms_id: "HUES-1"
      },
      {
        validated: true,
        pms_id: "HUES-2"
      },
      {
        validated: false,
        pms_id: "HUES-3"
      }
    ]);

    // Sort by pax_type
    expect(
      orderGuests([
        {
          pax_type: "BB",
          pms_id: "HUES-5"
        },
        {
          pax_type: "AD",
          pms_id: "HUES-1"
        },
        {
          pax_type: "CH",
          pms_id: "HUES-4"
        },
        {
          pax_type: null,
          pms_id: "HUES-2"
        },
        {
          pax_type: "JR",
          pms_id: "HUES-3"
        }
      ])
    ).toEqual([
      {
        pax_type: "AD",
        pms_id: "HUES-1"
      },
      {
        pax_type: null,
        pms_id: "HUES-2"
      },
      {
        pax_type: "JR",
        pms_id: "HUES-3"
      },
      {
        pax_type: "CH",
        pms_id: "HUES-4"
      },
      {
        pax_type: "BB",
        pms_id: "HUES-5"
      }
    ]);

    // Sort by name
    expect(
      orderGuests([
        {
          full_name: null,
          pms_id: "HUES-2"
        },
        {
          full_name: "Pepe Viyuela",
          pms_id: "HUES-1"
        }
      ])
    ).toEqual([
      {
        full_name: "Pepe Viyuela",
        pms_id: "HUES-1"
      },
      {
        full_name: null,
        pms_id: "HUES-2"
      }
    ]);

    // Sorting guests with full data
    expect(orderGuests(unorderedGuestList)).toEqual(orderedGuestList);
  });

  it("On mounted component, orderGuests method is called", async () => {
    const orderGuests = jest.spyOn(Status.methods, "orderGuests");
    shallowMount(Status, {
      store,
      localVue,
      i18n
    });

    expect(orderGuests).toHaveBeenCalled();
  });

  it("redirects to validate data if guest is adult and disable scan is enabled", async() => {
    const adultGuest = { name: 'Guest 1', pax_type: 'AD'};
    brand.state.config.disable_scan = true;

    const wrapper = shallowMount(Status, {
      store,
      localVue,
      i18n,
      mocks: {
        $router: mockRouter
      },
    });

    await wrapper.vm.goToPage(adultGuest);
  
    expect(redirect).toBeCalledWith({ name: "ValidateData" });
    });

  it("redirects to scan if guest is adult and disable scan is disabled", async() => {
      const adultGuest = { name: 'Guest 1', pax_type: 'AD'};
      brand.state.config.disable_scan = false;
  
      const wrapper = shallowMount(Status, {
        store,
        localVue,
        i18n,
        mocks: {
          $router: mockRouter
        },
      });
  
      await wrapper.vm.goToPage(adultGuest);
    
      expect(redirect).toBeCalledWith({ name: "Scan" });
    });

  it("redirects to validate data if guest is child, disable scan is enabled and scan children like adults is enabled", async() => {
      const childGuest = { name: 'Guest 1', pax_type: 'CH'};
      brand.state.config.disable_scan = true;
      brand.state.config.scan_children_like_adults = true;

      const wrapper = shallowMount(Status, {
        store,
        localVue,
        i18n,
        mocks: {
          $router: mockRouter
        },
      });
  
      await wrapper.vm.goToPage(childGuest);
    
      expect(redirect).toBeCalledWith({ name: "ValidateData" });
    });

    it("redirects to scan if guest is child, disable scan is disabled and scan children like adults is enabled", async() => {
      const childGuest = { name: 'Guest 1', pax_type: 'CH'};
      brand.state.config.disable_scan = false;
      brand.state.config.scan_children_like_adults = true;

      const wrapper = shallowMount(Status, {
        store,
        localVue,
        i18n,
        mocks: {
          $router: mockRouter
        },
      });
  
      await wrapper.vm.goToPage(childGuest);
    
      expect(redirect).toBeCalledWith({ name: "Scan" });
    });

    it("redirects to child form if guest is child and scan children like adults is disabled", async() => {
      const childGuest = { name: 'Guest 1', pax_type: 'CH'};
      brand.state.config.disable_scan = true;
      brand.state.config.scan_children_like_adults = false;

      const wrapper = shallowMount(Status, {
        store,
        localVue,
        i18n,
        mocks: {
          $router: mockRouter
        },
      });
  
      await wrapper.vm.goToPage(childGuest);
    
      expect(redirect).toBeCalledWith({ name: "ChildForm" });
    });

  it("Changes language when guest has a different lang than holder if in reception mode", () => {
    const holderGuest = { name: 'Guest 1', lang: 'es' };
    const secondGuest = { name: 'Guest 2', lang: 'de' };

    const storeInstance = store();
  
    storeInstance.state.app.isReceptionMode = true;
    storeInstance.state.app.appLanguage = holderGuest.lang;

    const wrapper = shallowMount(Status, {
      store: storeInstance,
      localVue,
      i18n,
      mocks: {
        $router: mockRouter
      },
    });

    const initialLocale = i18n.locale;
  
    return wrapper.vm.goToPage(secondGuest).then(() => {
			expect(wrapper.vm.$root.$i18n.locale).toBe('de');
			expect(storeInstance.state.app.appLanguage).toBe('de');
		}).finally(() => {
      // Reset the locale to the initial value to prevent it from affecting subsequent tests
      i18n.locale = initialLocale;
    });

    });
  
  it("Doesn't change language when guest has a different lang if not in reception mode", () => {
      const secondGuest = { name: 'Guest 2', lang: 'de' };
    
      const storeInstance = store();
    
      storeInstance.state.app.isReceptionMode = false;
    
      const wrapper = shallowMount(Status, {
        store: storeInstance,
        localVue,
        i18n,
        mocks: {
          $router: mockRouter
        },
      });
    
      const initialLocale = i18n.locale;
    
      return wrapper.vm.goToPage(secondGuest).then(() => {
        expect(wrapper.vm.$root.$i18n.locale).toBe(initialLocale);
        expect(wrapper.vm.$root.$i18n.locale).not.toBe('de');
      }).finally(() => {
        i18n.locale = initialLocale;
      });
    });
    
});
