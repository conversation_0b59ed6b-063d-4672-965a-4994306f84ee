import Vuex from "vuex";
import Vuelidate from "vuelidate";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import Share from "@/views/Share";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import guestModule from "@/store/guest";
import brandModule from "@/store/brand";
import { i18n } from "@/locales";
import redirectMixin from "@/mixins/redirect";
import router from "@/router";
const localVue = createLocalVue();

// need to import vuex
localVue.use(Vuex);
// if the components mounted reference $v we need to import Vuelidate
localVue.use(Vuelidate);

describe("Share View", () => {
	let store;
	let wrapper;
	let mockRouter;
	let guest;
	let brand;
	let reservations;
	let redirect;

	beforeEach(async () => {
		redirect = jest.spyOn(redirectMixin.methods, "redirect");
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		guest = guestModule;
		brand = brandModule;
		reservations = {
			...reservationsModule,
			state: {
				childAgeAttempts: 0,
				reservationSelected: {
					res_localizer: "234kj",
					res_adults: 1,
					res_children: 0,
					guests: [{ validated: false }],
				},
				numberGuestSession: 1,
				multipleRooms: false,
			},
		};

		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					guest,
					brand,
					loading,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});
	it("Share page is mounted", async () => {
		wrapper = shallowMount(Share, {
			store,
			i18n,
			router,
		});
		expect(wrapper.is(Share)).toBe(true);
	});
});
