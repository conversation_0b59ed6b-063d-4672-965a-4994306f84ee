import { mount } from "@vue/test-utils";
import ValidateDataCard from "@/components/shared/validateDataCard.vue";
import { i18n } from "@/locales";

describe("ValidateDataCard", () => {
	afterEach(() => {
		jest.resetAllMocks();
	});
	it("should render the title correctly", async () => {
		i18n.locale = "en";
		const wrapper = mount(ValidateDataCard, {
			propsData: {
				inputs: [
					{ name: "name", value: "<PERSON>", active: "true" },
					{ name: "surname", value: "<PERSON><PERSON><PERSON><PERSON>", active: "true" },
					{
						name: "second_surname",
						value: "<PERSON><PERSON>",
						active: "false",
					},
					{
						name: "birthay_date",
						value: "1998-02-05",
						active: "true",
					},
				],
			},
			i18n,
		});

		const title = wrapper.find("#title");
		const data = wrapper.find("#data");
		const toggleCard = wrapper.find("#toggleCard");
		await wrapper.vm.$nextTick();

		expect(wrapper.vm.shownInputs.length).toBe(3); //Number of inputs with value, no errors and active property on true
		expect(title.text()).toBe("Validated Information"); //Title text
		expect(data.text()).toBe("+ 1 Data"); //Number is the ammount of inputs unshown due to no value, have errors or are inactive
		expect(toggleCard.text()).toBe("Show all"); //Toggle text
	});
	it("should render the title correctly in spanish", async () => {
		i18n.locale = "es";
		const wrapper = mount(ValidateDataCard, {
			propsData: {
				inputs: [
					{ name: "name", value: "Alex", active: "true" },
					{ name: "surname", value: "Quiroga", active: "true" },
					{
						name: "second_surname",
						value: "Marcelo",
						active: "false",
					},
					{
						name: "birthay_date",
						value: "1998-02-05",
						active: "true",
					},
				],
			},
			i18n,
		});

		const title = wrapper.find("#title");
		const data = wrapper.find("#data");
		const toggleCard = wrapper.find("#toggleCard");
		await wrapper.vm.$nextTick();

		expect(wrapper.vm.shownInputs.length).toBe(3); //Number of inputs with value, no errors and active property on true
		expect(title.text()).toBe("Información validada"); //Title text
		expect(data.text()).toBe("+ 1 Datos"); //Number is the ammount of inputs unshown due to no value, have errors or are inactive
		expect(toggleCard.text()).toBe("Ver todo"); //Toggle text
	});
	it("should emit 'cardToggle' event when toggle card is clicked", async () => {
		const wrapper = mount(ValidateDataCard, {
			propsData: {
				inputs: [
					{ name: "name", value: "John Doe" },
					{ name: "age", value: "25" },
				],
			},
			i18n,
		});

		const toggleCard = wrapper.find("#toggleCard");
		await toggleCard.trigger("click");

		expect(wrapper.emitted("cardToggle")).toBeTruthy();
	});
	it("should not render the card if 'inputs' prop is empty", () => {
		const wrapper = mount(ValidateDataCard, {
			propsData: {
				inputs: [],
			},
			i18n,
		});

		const card = wrapper.find("#validateDataCard");
		expect(card.exists()).toBe(false);
	});
});
