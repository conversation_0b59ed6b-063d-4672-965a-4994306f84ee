import Vuex from "vuex";
import { createLocalVue, shallowMount } from "@vue/test-utils";
import ChildsData from "@/views/ChildsData";
import brandModule from "@/store/brand";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import modal from "@/store/modal";
import guestModule from "@/store/guest";
import { i18n } from "@/locales";
import { format } from "date-fns";
import { DEFAULT_FORMAT, EMITTED_VALUE } from "@/mixins/dateFormat.js";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { selectedReservation } from "../../../mocks/modules/integration/data";
import flushPromises from "flush-promises";

const localVue = createLocalVue();
localVue.use(Vuex);

// Get birth date according to the argument passed
function getBirthDate(yearsOld) {
	const checkInDate = new Date(selectedReservation.check_in);
	let birthDate = new Date(checkInDate.setFullYear(checkInDate.getFullYear() - yearsOld));
	birthDate = format(birthDate, EMITTED_VALUE);
	return birthDate;
}

describe("Childs Data Page functionality", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getChildGuests: () => {
					return [
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: null,
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: false,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddag",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: null,
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: false,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
					];
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: defaultConfig.data,
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Continue button is enabled when all data is filled", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		const submitButton = wrapper.find('[data-test="submitButton"]');
		expect(submitButton.props()["disabled"]).toBe(true);

		const formInputs = wrapper.vm.formInputs.map((inputs) => {
			return inputs.map((input) => {
				if (input.active === "true") {
					input.value = getBirthDate(1);
				}

				return input;
			});
		});
		await wrapper.setData({
			formInputs,
		});

		expect(submitButton.props()["disabled"]).toBe(false);
		submitButton.trigger("click");
		await flushPromises();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Documents",
		});

		wrapper.destroy();
	});

	it("Show modal when continue button is clicked and birthday form is filled with an adult age", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const submitButton = wrapper.find('[data-test="submitButton"]');
		expect(submitButton.props()["disabled"]).toBe(true);

		const formInputs = wrapper.vm.formInputs.map((inputs) => {
			return inputs.map((input) => {
				if (input.active === "true") {
					input.value = getBirthDate(21);
				}

				return input;
			});
		});
		await wrapper.setData({
			formInputs,
		});

		expect(submitButton.props()["disabled"]).toBe(false);
		submitButton.trigger("click");
		await flushPromises();

		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);

		wrapper.destroy();
	});
});

describe("Childs Data Page with validated guests", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getChildGuests: () => {
					return [
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: "2024-01-01",
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: true,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddag",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: "2020-05-01",
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: true,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
					];
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: defaultConfig.data,
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Redirect to Document page if all childs are validated", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Documents",
		});

		wrapper.destroy();
	});
});
