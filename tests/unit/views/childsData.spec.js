import Vuex from "vuex";
import { createLocalVue, shallowMount } from "@vue/test-utils";
import ChildsData from "@/views/ChildsData";
import brandModule from "@/store/brand";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import modal from "@/store/modal";
import guestModule from "@/store/guest";
import { i18n } from "@/locales";
import { format } from "date-fns";
import { DEFAULT_FORMAT, EMITTED_VALUE } from "@/mixins/dateFormat.js";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { selectedReservation } from "../../../mocks/modules/integration/data";
import flushPromises from "flush-promises";

const localVue = createLocalVue();
localVue.use(Vuex);

// Get birth date according to the argument passed
function getBirthDate(yearsOld) {
	const checkInDate = new Date(selectedReservation.check_in);
	let birthDate = new Date(checkInDate.setFullYear(checkInDate.getFullYear() - yearsOld));
	birthDate = format(birthDate, EMITTED_VALUE);
	return birthDate;
}

describe("Childs Data Page functionality", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getChildGuests: () => {
					return [
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: null,
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: false,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddag",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: null,
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: false,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
					];
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: defaultConfig.data,
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Continue button is enabled when all data is filled", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		const submitButton = wrapper.find('[data-test="submitButton"]');
		expect(submitButton.props()["disabled"]).toBe(true);

		const formInputs = wrapper.vm.formInputs.map((inputs) => {
			return inputs.map((input) => {
				if (input.active === "true") {
					input.value = getBirthDate(1);
				}

				return input;
			});
		});
		await wrapper.setData({
			formInputs,
		});

		expect(submitButton.props()["disabled"]).toBe(false);
		submitButton.trigger("click");
		await flushPromises();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Documents",
		});

		wrapper.destroy();
	});

	it("Show modal when continue button is clicked and birthday form is filled with an adult age", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const submitButton = wrapper.find('[data-test="submitButton"]');
		expect(submitButton.props()["disabled"]).toBe(true);

		const formInputs = wrapper.vm.formInputs.map((inputs) => {
			return inputs.map((input) => {
				if (input.active === "true") {
					input.value = getBirthDate(21);
				}

				return input;
			});
		});
		await wrapper.setData({
			formInputs,
		});

		expect(submitButton.props()["disabled"]).toBe(false);
		submitButton.trigger("click");
		await flushPromises();

		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);

		wrapper.destroy();
	});
});

describe("Childs Data Page with validated guests", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				reservationSelected: selectedReservation,
			},
		};
		guest = {
			...guestModule,
			getters: {
				getChildGuests: () => {
					return [
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: "2024-01-01",
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: true,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
						{
							uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddag",
							pms_id: null,
							name: null,
							surname: null,
							second_surname: null,
							full_name: null,
							gender: null,
							birthday_date: "2020-05-01",
							email: null,
							telephone: null,
							holder: false,
							lang: null,
							document_type: null,
							document_number: null,
							date_of_issue: null,
							date_of_expiry: null,
							document_support_number: null,
							address: {
								street: null,
								street_number: null,
								postal_code: null,
								province: null,
								city: null,
								CCAA: null,
								region: undefined,
								subregion: undefined,
								country: null,
							},
							nationality: null,
							position: null,
							residence_country: null,
							validated: true,
							processCompleted: false,
							pax_type: "CH",
							comment: {},
							signature: null,
							documentSignature: null,
							timestampSignature: null,
							documentsSaved: null,
							documents: null,
							identityDocumentsSaved: null,
							selected: false,
							signedDocuments: false,
							kinship: null,
						},
					];
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: defaultConfig.data,
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Redirect to Document page if all childs are validated", async () => {
		wrapper = await shallowMount(ChildsData, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await flushPromises();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Documents",
		});

		wrapper.destroy();
	});

	describe("Holder data prefill functionality", () => {
		let store;
		let wrapper;
		let mockRouter;
		let brand;
		let guest;
		let reservations;

		beforeEach(async () => {
			mockRouter = {
				push: jest.fn(() => Promise.resolve()),
			};
			reservations = {
				...reservationsModule,
				state: {
					reservationSelected: selectedReservation,
				},
			};
			guest = {
				...guestModule,
				getters: {
					getChildGuests: () => {
						return [
							{
								uuid: "a496f57c-5fd5-40ab-9bee-c3419a8eddaf",
								pms_id: null,
								name: null,
								surname: null,
								second_surname: null,
								full_name: null,
								gender: null,
								birthday_date: null,
								email: null,
								telephone: null,
								holder: false,
								lang: null,
								document_type: null,
								document_number: null,
								date_of_issue: null,
								date_of_expiry: null,
								document_support_number: null,
								address: {
									street: null,
									street_number: null,
									postal_code: null,
									province: null,
									city: null,
									CCAA: null,
									region: undefined,
									subregion: undefined,
									country: null,
								},
								nationality: null,
								position: null,
								residence_country: null,
								validated: false,
								processCompleted: false,
								pax_type: "CH",
								comment: {},
								signature: null,
								documentSignature: null,
								timestampSignature: null,
								documentsSaved: null,
								documents: null,
								identityDocumentsSaved: null,
								selected: false,
								signedDocuments: false,
								kinship: null,
							},
						];
					},
					getHolderGuest: () => {
						return {
							uuid: "holder-uuid",
							pms_id: "holder-123",
							name: "John",
							surname: "Doe",
							email: "<EMAIL>",
							telephone: {
								value: "123456789",
								countryCode: "ES",
								dialCode: "+34"
							},
							address: {
								street: "Main Street",
								street_number: "123",
								postal_code: "12345",
								city: "Madrid",
								province: "Madrid",
								CCAA: "Madrid",
								region: "Madrid",
								subregion: "Centro"
							},
							nationality: "ES",
							holder: true
						};
					},
				},
			};
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		it("Should prefill child form inputs with holder data when fill_from_holder is true", async () => {
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "municipality",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that the form inputs have been prefilled with holder data
			const formInputs = wrapper.vm.formInputs[0];

			const addressInput = formInputs.find(input => input.name === "address");
			expect(addressInput.value).toBe("Main Street");

			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			expect(postalCodeInput.value).toBe("12345");

			const municipalityInput = formInputs.find(input => input.name === "municipality");
			expect(municipalityInput.value).toBe("Madrid");

			// Check that street number is also prefilled
			expect(wrapper.vm.streetNumbers[0]).toBe("123");

			wrapper.destroy();
		});

		it("Should handle address autocomplete functionality", async () => {
			// Mock AWS Geo
			const mockGeo = {
				searchForSuggestions: jest.fn().mockResolvedValue([
					{ text: "Main Street 123, Madrid, Spain" },
					{ text: "Main Avenue 456, Madrid, Spain" }
				]),
				searchByText: jest.fn().mockResolvedValue([{
					street: "Main Street",
					addressNumber: "123",
					municipality: "Madrid",
					postalCode: "28001",
					region: "Madrid",
					country: "ESP",
					subRegion: "Centro"
				}])
			};

			// Mock the Geo import
			jest.doMock("@aws-amplify/geo", () => ({
				Geo: mockGeo
			}));

			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that address input is set as autocomplete type
			const formInputs = wrapper.vm.formInputs[0];
			const addressInput = formInputs.find(input => input.name === "address");
			expect(addressInput.type).toBe("autocomplete");
			expect(addressInput.options).toEqual([]);

			// Check that addressData is initialized
			expect(wrapper.vm.addressData[0]).toBeDefined();
			expect(wrapper.vm.addressData[0].street).toBe("");

			wrapper.destroy();
		});

		it("Should handle nationality selection and country filtering for address search", async () => {
			// Mock AWS Geo
			const mockGeo = {
				searchForSuggestions: jest.fn().mockResolvedValue([
					{ text: "Main Street 123, Madrid, Spain" },
					{ text: "Main Avenue 456, Madrid, Spain" }
				]),
				searchByText: jest.fn().mockResolvedValue([{
					street: "Main Street",
					addressNumber: "123",
					municipality: "Madrid",
					postalCode: "28001",
					region: "Madrid",
					country: "ESP",
					subRegion: "Centro"
				}])
			};

			// Mock the Geo import
			jest.doMock("@aws-amplify/geo", () => ({
				Geo: mockGeo
			}));

			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "nationality",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that nationality and country tracking is initialized
			expect(wrapper.vm.nationalityValues[0]).toBe(null);
			expect(wrapper.vm.selectedCountries[0]).toBe(null);

			// Simulate nationality selection
			const formInputs = wrapper.vm.formInputs[0];
			const nationalityInput = formInputs.find(input => input.name === "nationality");

			await wrapper.vm.autocompleteSelect(
				{ value: "Spain", error: false, selectedOption: { value: "Spain" } },
				nationalityInput
			);

			// Check that nationality and country are updated
			expect(wrapper.vm.nationalityValues[0]).toBe("Spain");
			expect(wrapper.vm.selectedCountries[0]).toBe("ESP");

			// Check that badLocationCountries are respected
			expect(wrapper.vm.badLocationCountries).toContain("JPN");
			expect(wrapper.vm.badLocationCountries).toContain("CHN");

			wrapper.destroy();
		});

		it("Should have updateInputValue method for field updates", async () => {
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that updateInputValue method exists
			expect(typeof wrapper.vm.updateInputValue).toBe('function');

			// Check that addressData is properly initialized
			expect(wrapper.vm.addressData[0]).toBeDefined();
			expect(wrapper.vm.addressData[0].street).toBe("");
			expect(wrapper.vm.addressData[0].postal_code).toBe("");

			wrapper.destroy();
		});

		it("Should NOT prefill inputs for validated guests", async () => {
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										fill_from_holder: "true",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			// Mock a validated child guest
			guest = {
				...guestModule,
				getters: {
					...guestModule.getters,
					getChildGuests: () => [
						{
							name: "Child Name",
							surname: "Child Surname",
							birthday_date: "2015-01-01",
							nationality: "ES",
							validated: true, // This child is already validated
							address: {
								street: "Existing Street",
								postal_code: "54321"
							}
						}
					],
					getHolderGuest: () => {
						return {
							name: "Holder Name",
							surname: "Holder Surname",
							birthday_date: "1990-01-01",
							telephone: {
								value: "123456789",
								countryCode: "ES",
								dialCode: "+34"
							},
							address: {
								street: "Main Street",
								street_number: "123",
								postal_code: "12345",
								city: "Madrid",
								province: "Madrid",
								CCAA: "Madrid",
								region: "Madrid",
								subregion: "Centro"
							},
							nationality: "ES",
							holder: true
						};
					},
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that the form inputs have NOT been prefilled with holder data
			const formInputs = wrapper.vm.formInputs[0];

			const addressInput = formInputs.find(input => input.name === "address");
			// Should keep existing child data, not be prefilled with holder data
			expect(addressInput.value).toBe("Existing Street");
			expect(addressInput.disabled).toBe(true); // Should be disabled for validated guests

			const postalCodeInput = formInputs.find(input => input.name === "postal_code");
			// Should keep existing child data, not be prefilled with holder data
			expect(postalCodeInput.value).toBe("54321");
			expect(postalCodeInput.disabled).toBe(true); // Should be disabled for validated guests

			// Check that the isChildValidated computed property works correctly
			expect(wrapper.vm.isChildValidated(0)).toBe(true); // First child is validated

			wrapper.destroy();
		});

		it("Should preserve address field values when switching tabs", async () => {
			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			// Mock multiple child guests
			guest = {
				...guestModule,
				getters: {
					...guestModule.getters,
					getChildGuests: () => [
						{
							name: "Child 1",
							surname: "Surname 1",
							birthday_date: "2015-01-01",
							nationality: "ES",
							address: {
								street: "First Street"
							}
						},
						{
							name: "Child 2",
							surname: "Surname 2",
							birthday_date: "2016-01-01",
							nationality: "ES",
							address: {
								street: "Second Street"
							}
						}
					],
					getHolderGuest: () => null,
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Check that we have multiple tabs
			expect(wrapper.vm.tabNames.length).toBe(2);
			expect(wrapper.vm.currentGroup).toBe(0); // Should start with first tab

			// Check first child's address
			const firstChildFormInputs = wrapper.vm.formInputs[0];
			const firstAddressInput = firstChildFormInputs.find(input => input.name === "address");
			expect(firstAddressInput.value).toBe("First Street");

			// Check second child's address
			const secondChildFormInputs = wrapper.vm.formInputs[1];
			const secondAddressInput = secondChildFormInputs.find(input => input.name === "address");
			expect(secondAddressInput.value).toBe("Second Street");

			// Switch to second tab
			wrapper.vm.onTabClicked(1);
			await wrapper.vm.$nextTick();

			expect(wrapper.vm.currentGroup).toBe(1);

			// Check that updateUIForCurrentTab method exists and can be called
			expect(typeof wrapper.vm.updateUIForCurrentTab).toBe('function');
			wrapper.vm.updateUIForCurrentTab();

			// Values should still be preserved
			expect(firstAddressInput.value).toBe("First Street");
			expect(secondAddressInput.value).toBe("Second Street");

			wrapper.destroy();
		});

		it("Should correctly identify child index for different tabs", async () => {

			brand = {
				...brandModule,
				state: {
					brandId: 1,
					mainColor: "123",
					country: "ES",
					config: {
						...defaultConfig.data,
						identification: {
							...defaultConfig.data.identification,
							child_form: [
								[
									{
										name: "address",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "postal_code",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "municipality",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									},
									{
										name: "CCAA",
										type: "text",
										active: "true",
										required: "false",
										value: ""
									}
								]
							]
						}
					}
				},
			};

			// Mock multiple child guests
			guest = {
				...guestModule,
				getters: {
					...guestModule.getters,
					getChildGuests: () => [
						{
							name: "Child 1",
							surname: "Surname 1",
							birthday_date: "2015-01-01",
							nationality: "ES"
						},
						{
							name: "Child 2",
							surname: "Surname 2",
							birthday_date: "2016-01-01",
							nationality: "ES"
						}
					],
					getHolderGuest: () => null,
				},
			};

			const storeWithConfig = () => {
				return new Vuex.Store({
					modules: {
						reservations,
						modal,
						loading,
						brand,
						guest,
					},
				});
			};

			wrapper = await shallowMount(ChildsData, {
				mocks: {
					$router: mockRouter,
				},
				store: storeWithConfig,
				localVue,
				i18n,
				attachTo: document.body,
			});

			await flushPromises();

			// Test that we have multiple tabs
			expect(wrapper.vm.tabNames.length).toBe(2);
			expect(wrapper.vm.currentGroup).toBe(0); // Should start with first tab

			// Test child index identification for first tab
			const firstChildFormInputs = wrapper.vm.formInputs[0];
			const firstAddressInput = firstChildFormInputs.find(input => input.name === "address");

			const firstChildIndex = wrapper.vm.formInputs.findIndex(formInput =>
				formInput.some(formInputItem => formInputItem === firstAddressInput)
			);
			expect(firstChildIndex).toBe(0);

			// Switch to second tab
			wrapper.vm.onTabClicked(1);
			await wrapper.vm.$nextTick();

			expect(wrapper.vm.currentGroup).toBe(1);

			// Test child index identification for second tab
			const secondChildFormInputs = wrapper.vm.formInputs[1];
			const secondAddressInput = secondChildFormInputs.find(input => input.name === "address");

			const secondChildIndex = wrapper.vm.formInputs.findIndex(formInput =>
				formInput.some(formInputItem => formInputItem === secondAddressInput)
			);
			expect(secondChildIndex).toBe(1);

			// Test that updateInputValue only works for current tab
			expect(typeof wrapper.vm.updateInputValue).toBe('function');

			// This should work (current tab is 1)
			wrapper.vm.updateInputValue('address', 'Test Address', 1);

			// This should not work (tab 0 is not current)
			wrapper.vm.updateInputValue('address', 'Should Not Work', 0);

			wrapper.destroy();
		});
	});
});
