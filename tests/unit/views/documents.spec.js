import Vuex from "vuex";
import { mount, createLocalVue, shallowMount } from "@vue/test-utils";
import { i18n } from "@/locales";
import Documents from "@/views/Documents.vue";
import router from "@/router";
import gdpr from "@/store/gdpr.js";
import brandModule from "@/store/brand.js";
import guestModule from "@/store/guest.js";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { defaultGuestList } from "../../../mocks/data/guests";
import loading from "@/store/loading.js";
import modal from "@/store/modal";
import repository from "@/repository/repositoryFactory.js";
import * as translatedDocuments from "@/services/documents/translateDocuments";
import flushPromises from "flush-promises";
const api = repository.get("checkin");

const localVue = createLocalVue();
localVue.use(Vuex);

describe("Documents page", () => {
  let store;
  let wrapper;

  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        brand: {
          ...brandModule,
          state: {
            ...brandModule.state,
            config: defaultConfig.data,
          },
        },
        guest: {
          ...guestModule,
          getters: {
            ...guestModule.getters,
            getSelectedGuest: () => {
              return defaultGuestList[0];
            },
          },
        },
        gdpr,
        loading,
        modal,
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it("Documents page is created", async () => {
    translatedDocuments.default = jest.fn(async () =>
      Promise.resolve(mockDocuments)
    );
    api.getBrandDocumentsForCurrentGuest = jest.fn(() =>
      Promise.resolve({
        data: [
          {
            id: 1,
            brand_id: 1,
            name: "TEST DOCUMENT",
            active: 1,
            extension: "pdf",
          },
        ],
      })
    );

    wrapper = mount(Documents, {
      store,
      i18n,
    });
    wrapper.vm.setDocumentVariablesValues = jest.fn(
      () => mockDocumentVariables
    );
    expect(wrapper.is(Documents)).toBe(true);
  });

	it("should render dynamic checkboxes with correct text for each document", async () => {
		const dynamicCheckboxes = wrapper.findAll("ul input[type='checkbox']");
		expect(dynamicCheckboxes.length).toBe(2);
		const expectedTexts = mockDocuments[0].checkbox_configurations.map(config => config.text);

		dynamicCheckboxes.wrappers.forEach((checkbox, index) => {
			const associatedLabel = checkbox.element.nextElementSibling;
			expect(associatedLabel.textContent.trim()).toBe(expectedTexts[index]); 
		});

  });

  it("Modal is visible and click on close button", async () => {
    expect(wrapper.find('[data-test="document-infoModal"]').exists()).toBe(
      true
    );
    expect(wrapper.find('[data-test="closeModal"]').exists()).toBe(true);
    wrapper.find('[data-test="closeModal"]').trigger("click");
  });

  it("First Document should render", async () => {
    wrapper.vm.showDocumentContent(wrapper.vm.documentList[0]);
    expect(wrapper.vm.documentToShow).toEqual(mockDocuments[0]);
    expect(wrapper.vm.documentList[0].read).toEqual(true);
    await wrapper.vm.$forceUpdate();
    expect(wrapper.find(".document-details-content").exists()).toBe(true);
  });
  it("if checkbox is clicked, change is triggered and data is mutated", async () => {
    const manualCheckbox = wrapper
      .findAll('[data-test="document-content"] input[type="checkbox"]')
      .at(0);
    manualCheckbox.setChecked();

    await wrapper.vm.$forceUpdate();
    expect(manualCheckbox.attributes("checked")).toBe("");

    // Click on read document, and go to next document
    await wrapper.find("[data-test='close-document-button']").trigger("click");
    await wrapper.vm.$forceUpdate();
    expect(wrapper.vm.documentToShow).not.toBe(null);
    expect(wrapper.vm.documentList[0].content).not.toEqual(
      '<p>Esto es un test</p>\r\n<input type="checkbox">&nbsp;Estoy de acuerdo<p></p>\r\n<input type="checkbox">&nbsp;No estoy de acuerdo<p></p>'
    );
  });

	it("updates checkbox state on click and retains state across navigation", async () => {
		wrapper.vm.showDocumentContent(wrapper.vm.documentList[0]);
		await wrapper.vm.$nextTick();
	
		const firstDynamicCheckbox = wrapper.find("ul input[type='checkbox']");
		firstDynamicCheckbox.setChecked(true);
		await wrapper.vm.$nextTick();
	
		// Check that the state is updated
		const checkboxType = mockDocuments[0].checkbox_configurations[0].type;
		expect(wrapper.vm.checkboxStates[checkboxType]).toBe(true);
	
		// Navigate away and back to verify state retention
		wrapper.vm.closeReadedDocument();
		wrapper.vm.showDocumentContent(wrapper.vm.documentList[0]);
		await wrapper.vm.$nextTick();
		expect(wrapper.vm.checkboxStates[checkboxType]).toBe(true);
	});

	it("If there are no checkbox configurations, no checkboxes should be shown", async () => {
    wrapper.vm.showDocumentContent(wrapper.vm.documentList[1]);
		await wrapper.vm.$nextTick();
		const checkboxList = wrapper.find("ul");
  	expect(checkboxList.exists()).toBe(false);
  	expect(wrapper.vm.documentToShow.checkbox_configurations.length).toBe(0);
  });

  it("Second Document should render, and click on button to go towards sign page.", async () => {
    wrapper.vm.showDocumentContent(wrapper.vm.documentList[1]);
    expect(wrapper.vm.documentToShow).toEqual(mockDocuments[1]);
    expect(wrapper.vm.documentList[0].read).toEqual(true);
    await wrapper.vm.$forceUpdate();
    expect(wrapper.find(".document-details-content").exists()).toBe(true);

    // Once all documents have been read, we go to sign documents.
    wrapper.find("[data-test='close-document-button']").trigger("click");
  });

  it("If the guest is an ADULT and children_sign_documents is false, the guest should still sign documents", async () => {
    wrapper = shallowMount(Documents, {
      store,
      localVue,
      i18n,
    });
    expect(wrapper.is(Documents)).toBe(true);
  });
});

describe("Guest is a child", () => {
  let store;
  let wrapper;

  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        brand: {
          ...brandModule,
          state: {
            ...brandModule.state,
            config: defaultConfig.data,
          },
        },
        guest: {
          ...guestModule,
          getters: {
            ...guestModule.getters,
            getSelectedGuest: () => {
              return defaultGuestList[4];
            },
          },
        },
        gdpr,
        loading,
        modal,
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it("If the guest is a CHILD and children_sign_documents is false, the guest should not sign documents", async ()=>{

  const spyPush = jest.spyOn(router, "push");

  wrapper = shallowMount(Documents, {
    router,
    store,
    localVue,
    i18n,
  });

  expect(spyPush).toHaveBeenCalledWith({
    name: "PhoneForm",
  });

  })

  it("If the guest is a CHILD and children_sign_documents is true, the guest should sign documents", async ()=>{

    store.state.brand.config.children_sign_documents = true;

    translatedDocuments.default = jest.fn(async () =>
      Promise.resolve(mockDocuments)
    );
    api.getBrandDocumentsForCurrentGuest = jest.fn(() =>
      Promise.resolve({
        data: [
          {
            id: 1,
            brand_id: 1,
            name: "TEST DOCUMENT",
            active: 1,
            extension: "pdf",
          },
        ],
      })
    );

    wrapper = mount(Documents, {
      store,
      i18n,
    });

    wrapper.vm.setDocumentVariablesValues = jest.fn(() => mockDocumentVariables);

    const spyPush = jest.spyOn(router, "push");

    expect(spyPush).not.toHaveBeenCalledWith();

    await flushPromises()

    const documentContent = wrapper.find('[data-test="document-content"]');
    expect(documentContent.exists()).toBe(true);

    wrapper.vm.showDocumentContent(wrapper.vm.documentList[0]);

    expect(wrapper.vm.documentToShow).not.toBeNull();
    expect(wrapper.vm.documentToShow).toEqual(mockDocuments[0]);

    })
})

const mockDocuments = [
  {
    id: 1,
    title: "TEST",
    content:
      '<p>Esto es un test</p>\r\n<input type="checkbox">&nbsp;Estoy de acuerdo<p></p>\r\n<input type="checkbox">&nbsp;No estoy de acuerdo<p></p>',
    extension: "pdf",
    read: false,
    checkbox_configurations: [
      {
      	type: "data_protect",
        text: "english data protect"
      },
			{
        type: "terms_conditions",
				text: "english terms conditions"
      },
    ],
  },
  {
    id: 2,
    title: "TEST2",
    content:
      '<p>Esto es el test 2</p>\r\n<input type="checkbox">&nbsp;Estoy de acuerdo<p></p>\r\n<input type="checkbox">&nbsp;No estoy de acuerdo<p></p>',
    extension: "pdf",
    read: false,
    checkbox_configurations: [],
  },
];
const mockDocumentVariables = {
  bookingAdultsNumber: "1",
  bookingBoardType: "AL",
  bookingCheckInDate: "2022-02-05",
  bookingCheckOutDate: "2022-02-07",
  bookingChildrenNumber: null,
  bookingCode: "145299-005",
  bookingDate: null,
  bookingHolderEmail: "NOT RETRIEVED",
  bookingHolderTelephone: "NOT RETRIEVED",
  bookingInvoiceBoardType: "NOT RETRIEVED",
  bookingInvoiceRoomType: "NOT RETRIEVED",
  bookingNightsNumber: "2",
  bookingRoomNumber: "",
  bookingRoomType: "DH",
  bookingType: "NOT RETRIEVED",
  bookingVoucherCode: "NOT RETRIEVED",
  guestDateOfBirth: "1996-02-05",
  guestDocumentDateOfIssue: "2018-01-24",
  guestDocumentNumber: "12345678A",
  guestDocumentType: "identity_card",
  guestFirstName: "TEST",
  guestGender: "male",
  guestLastName: "TESTING",
  guestMinimumAge: 18,
  guestNationality: "ESP",
  guestType: "NOT RETRIEVED",
};
