import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import flushPromises from "flush-promises";
import { i18n } from "@/locales";
import Search from "@/views/Search.vue";
import queryParams from "@/store/queryParams.js";
import loading from "@/store/loading.js";
import router from "@/router";
import modal from "@/store/modal";
import brand from "@/store/brand";
import guest from "@/store/guest";
import reservations from "@/store/reservations";
import app from "@/store/app";
import repository from "@/repository/repositoryFactory.js";
import {
	defaultReservation,
	notFoundReservation,
	groupReservationError,
	futureReservation,
	receptionReservation,
} from "../../../mocks/modules/integration/data";

const localVue = createLocalVue();

const defaultFormInputs = [
	[
		{
			active: "true",
			name: "reservation_code",
			type: "text",
			minLength: "3",
			maxLength: "20",
		},
		{
			active: "true",
			name: "last_name",
			type: "text",
			minLength: "2",
			maxLength: "50",
		},
		{
			active: "false",
			name: "check_in",
			type: "date",
		},
		{
			active: "false",
			name: "check_out",
			type: "date",
		},
		{
			active: "false",
			name: "email",
			type: "email",
		},
		{
			active: "false",
			name: "first_name",
			type: "text",
			minLength: "2",
			maxLength: "50",
		},
	],
];

localVue.use(Vuex);

describe("Search page", () => {
	let store;
	let wrapper;

	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "true",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "false",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				reservations,
				modal,
				queryParams,
				loading,
				guest,
			},
		});

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
	});
	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
	});
	// * TODO: improve and add more tests
	it("Search page is mounted", async () => {
		expect(wrapper.is(Search)).toBe(true);
	});

	it("text is rendered", async () => {
		expect(wrapper.find("title-component-stub").text()).toEqual(
			"We are going to look for your booking",
		);
		expect(wrapper.find(".search-text").text()).toEqual(
			"Fill in all the required fields to be able to locate your booking in our system. You will be able to find the necessary information in the data that was provided to you at the time of booking. Search by:",
		);
	});

	it("button is rendered and disabled", async () => {
		expect(wrapper.find('[data-test="searchButton"]').exists()).toBeTruthy();
		expect(wrapper.find('[data-test="searchButton"]').props()["disabled"]).toBe(
			true,
		);
	});

	it("search form is rendered and input props is filled with reservation inputs", async () => {
		expect(wrapper.find("search-form-stub").exists()).toBeTruthy();
		expect(wrapper.find("search-form-stub").props()["inputs"]).toEqual(
			store.state.brand.config.identification.reservation_inputs[0],
		);
	});

	it("if search form is ok, button should be enabled", async () => {
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});
		const button = wrapper.find('[data-test="searchButton"]');
		await flushPromises();
		expect(button.props()["disabled"]).toBe(false);
	});

	it("if button is clicked, submit is called", async () => {
		wrapper.vm.submit = jest.fn();
		const button = wrapper.find("btn-stub");
		button.trigger("click");
		expect(wrapper.vm.submit).toBeCalled();
	});

	it("if button is clicked but fields haven't been filled in, api getReservation is not called", () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () => Promise.resolve());

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});

		const button = wrapper.find('[data-test="searchButton"]');
		button.trigger("click");

		expect(api.getReservation).not.toBeCalled();
	});

	it("api getReservation is called, and reservations are set", async () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(defaultReservation),
		);

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});

		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});

		const button = wrapper.find("btn-stub");
		await button.trigger("click");

		expect(await api.getReservation).toBeCalledTimes(1);
	});

	it("if timeLimit is set, a modal should pop up with the remaining days", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: defaultFormInputs,
							},
							activate_time_limit: true,
							time_limit_checkin: 1,
							max_attempts_reservation: 1,
						},
						timezone: "Europe/Paris",
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					state: {
						...reservations.state,
						data: null,
						inputsForm: [
							[
								{
									active: "true",
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									active: "true",
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									name: "check_in",
									type: "date",
								},
								{
									active: "false",
									name: "check_out",
									type: "date",
								},
								{
									active: "false",
									name: "email",
									type: "email",
								},
								{
									active: "false",
									name: "first_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
							],
						],
					},
					getters: reservations.getters,
					actions: reservations.actions,
					mutations: reservations.mutations,
					namespaced: true,
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(futureReservation),
		);
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});
		wrapper.vm.sendRequest = jest.fn(() => [
			{
				brand_id: "76",
				guests: [
					{
						pms_id: "HUES-744341",
						position: null,
						validated: false,
						pax_type: "AD",
						first_name: null,
						last_name: null,
						email: null,
						gender: null,
						birthday: null,
						nationality: null,
						document_id: null,
						address: null,
						city: null,
						province: null,
						postal_code: null,
						telephone: null,
						birth_country: null,
						residence_country: null,
						holder: true,
					},
					{
						pms_id: null,
						position: null,
						validated: false,
						pax_type: "CH",
						first_name: null,
						last_name: null,
						email: null,
						gender: null,
						birthday: null,
						nationality: null,
						document_id: null,
						address: null,
						city: null,
						province: null,
						postal_code: null,
						telephone: null,
						birth_country: null,
						residence_country: null,
						holder: false,
					},
				],
				check_in: "2040-01-01",
				check_out: "2040-01-07",
				res_date: null,
				res_room_number: "",
				res_room_type: "DH",
				res_board: "AL",
				booking_state: "reserved",
				stay_state: "scheduled",
				check_state: "none",
				check_state_date: "1900-01-01",
				res_adults: "1",
				res_children: "1",
				res_juniors: null,
				res_babies: null,
				res_seniors: null,
				res_localizer: "TESTFUTURE",
				res_id: "**********",
				res_nights: "6",
				res_agency: null,
				res_company: null,
				res_intermediary: null,
				res_channel: "EXPEDIA.COM",
				res_contract: null,
				res_comments: "",
				res_amount: 0,
				res_extras: "[]",
				res_currency: "EUR",
			},
		]);

		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});

		const button = wrapper.find("btn-stub");
		await button.trigger("click");
		await flushPromises();
		const substring = "Online check-in will start in";
		expect(await wrapper.vm.$data.isErrorMessage).toBe(false);
		expect(await wrapper.vm.$data.modalMessage.includes(substring)).toBe(true);
	});

	it("if closetimeLimit is set, a modal should pop up with the remaining days", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: defaultFormInputs,
							},
							activate_time_limit: true,
							close_time_limit_checkin: 1,
							max_attempts_reservation: 1,
						},
						timezone: "Europe/Paris",
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					state: {
						...reservations.state,
						data: null,
						inputsForm: [
							[
								{
									active: "true",
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									active: "true",
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									name: "check_in",
									type: "date",
								},
								{
									active: "false",
									name: "check_out",
									type: "date",
								},
								{
									active: "false",
									name: "email",
									type: "email",
								},
								{
									active: "false",
									name: "first_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
							],
						],
					},
					getters: reservations.getters,
					actions: reservations.actions,
					mutations: reservations.mutations,
					namespaced: true,
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(futureReservation),
		);
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});
		wrapper.vm.sendRequest = jest.fn(() => [
			{
				brand_id: "76",
				guests: [
					{
						pms_id: "HUES-744341",
						position: null,
						validated: false,
						pax_type: "AD",
						first_name: null,
						last_name: null,
						email: null,
						gender: null,
						birthday: null,
						nationality: null,
						document_id: null,
						address: null,
						city: null,
						province: null,
						postal_code: null,
						telephone: null,
						birth_country: null,
						residence_country: null,
						holder: true,
					},
					{
						pms_id: null,
						position: null,
						validated: false,
						pax_type: "CH",
						first_name: null,
						last_name: null,
						email: null,
						gender: null,
						birthday: null,
						nationality: null,
						document_id: null,
						address: null,
						city: null,
						province: null,
						postal_code: null,
						telephone: null,
						birth_country: null,
						residence_country: null,
						holder: false,
					},
				],
				check_in: "1900-01-01",
				check_out: "1900-01-07",
				res_date: null,
				res_room_number: "",
				res_room_type: "DH",
				res_board: "AL",
				booking_state: "reserved",
				stay_state: "scheduled",
				check_state: "none",
				check_state_date: "1900-01-01",
				res_adults: "1",
				res_children: "1",
				res_juniors: null,
				res_babies: null,
				res_seniors: null,
				res_localizer: "TESTFUTURE",
				res_id: "**********",
				res_nights: "6",
				res_agency: null,
				res_company: null,
				res_intermediary: null,
				res_channel: "EXPEDIA.COM",
				res_contract: null,
				res_comments: "",
				res_amount: 0,
				res_extras: "[]",
				res_currency: "EUR",
			},
		]);

		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});

		const button = wrapper.find("btn-stub");
		await button.trigger("click");
		await flushPromises();
		const substring = "Online checkin closed";
		expect(await wrapper.vm.$data.isErrorMessage).toBe(false);
		expect(await wrapper.vm.$data.modalMessage.includes(substring)).toBe(true);
	});

	it("if timeLimit is set but not activated, a modal should not pop up", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: defaultFormInputs,
							},
							activate_time_limit: false,
							time_limit_checkin: 1,
							max_attempts_reservation: 1,
						},
						timezone: "Europe/Paris",
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					state: {
						...reservations.state,
						data: null,
						inputsForm: [
							[
								{
									active: "true",
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									active: "true",
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									name: "check_in",
									type: "date",
								},
								{
									active: "false",
									name: "check_out",
									type: "date",
								},
								{
									active: "false",
									name: "email",
									type: "email",
								},
								{
									active: "false",
									name: "first_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
							],
						],
					},
					getters: reservations.getters,
					actions: reservations.actions,
					mutations: reservations.mutations,
					namespaced: true,
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(futureReservation),
		);
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});
		wrapper.vm.sendRequest = jest.fn(() => [
			{
				brand_id: "76",
				guests: [
					{
						pms_id: "HUES-744341",
						position: null,
						validated: false,
						pax_type: "AD",
						first_name: null,
						last_name: null,
						email: null,
						gender: null,
						birthday: null,
						nationality: null,
						document_id: null,
						address: null,
						city: null,
						province: null,
						postal_code: null,
						telephone: null,
						birth_country: null,
						residence_country: null,
						holder: true,
					},
					{
						pms_id: null,
						position: null,
						validated: false,
						pax_type: "CH",
						first_name: null,
						last_name: null,
						email: null,
						gender: null,
						birthday: null,
						nationality: null,
						document_id: null,
						address: null,
						city: null,
						province: null,
						postal_code: null,
						telephone: null,
						birth_country: null,
						residence_country: null,
						holder: false,
					},
				],
				check_in: "2040-01-01",
				check_out: "2040-01-07",
				res_date: null,
				res_room_number: "",
				res_room_type: "DH",
				res_board: "AL",
				booking_state: "reserved",
				stay_state: "scheduled",
				check_state: "none",
				check_state_date: "1900-01-01",
				res_adults: "1",
				res_children: "1",
				res_juniors: null,
				res_babies: null,
				res_seniors: null,
				res_localizer: "TESTFUTURE",
				res_id: "**********",
				res_nights: "6",
				res_agency: null,
				res_company: null,
				res_intermediary: null,
				res_channel: "EXPEDIA.COM",
				res_contract: null,
				res_comments: "",
				res_amount: 0,
				res_extras: "[]",
				res_currency: "EUR",
			},
		]);

		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});

		const button = wrapper.find("btn-stub");
		await button.trigger("click");
		await flushPromises();
		expect(await wrapper.vm.$data.isErrorMessage).toBe(false);
		expect(await wrapper.vm.$data.modalMessage).toBe(null);
	});

	it("if reservation is not available, a modal should pop up", async () => {
		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "true",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "false",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],
								time_limit_checkin: 1,
								max_attempts_reservation: 1,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					state: {
						...reservations.state,
						data: null,
						inputsForm: [
							[
								{
									active: "true",
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									active: "true",
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									name: "check_in",
									type: "date",
								},
								{
									active: "false",
									name: "check_out",
									type: "date",
								},
								{
									active: "false",
									name: "email",
									type: "email",
								},
								{
									active: "false",
									name: "first_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
							],
						],
					},
					getters: reservations.getters,
					actions: reservations.actions,
					mutations: reservations.mutations,
					namespaced: true,
				},
				modal,
				app,
				queryParams,
				loading,
				guest,
			},
		});
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () => {
			return Promise.resolve(notFoundReservation);
		});
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});

		wrapper.vm.sendRequest = jest.fn(async () => Promise.resolve([]));

		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});

		const button = wrapper.find("btn-stub");
		await button.trigger("click");
		await flushPromises();

		expect(await wrapper.vm.$data.isErrorMessage).toBe(true);
		expect(await wrapper.vm.$data.modalMessage).toEqual(
			"We have been unable to find a reservation with the details provided. Please, try again.",
		);
	});

	it("if reservation endpoint fails, a modal should pop up", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: defaultFormInputs,
								time_limit_checkin: 1,
								max_attempts_reservation: 1,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					state: {
						...reservations.state,
						data: null,
						inputsForm: [
							[
								{
									active: "true",
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									active: "true",
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									name: "check_in",
									type: "date",
								},
								{
									active: "false",
									name: "check_out",
									type: "date",
								},
								{
									active: "false",
									name: "email",
									type: "email",
								},
								{
									active: "false",
									name: "first_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
							],
						],
					},
					getters: reservations.getters,
					actions: reservations.actions,
					mutations: reservations.mutations,
					namespaced: true,
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () => Promise.reject());
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});

		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});

		const button = wrapper.find("btn-stub");
		await button.trigger("click");

		await flushPromises();

		expect(await wrapper.vm.$data.isErrorMessage).toBe(true);
		expect(await wrapper.vm.$data.modalMessage).toEqual(
			"Unfortunately, the online check-in service is currently unavailable. <br> Please try again at a later date or contact our staff for more information and assistance.",
		);
	});

	it("should handle a groupReservation error correctly and show the appropriate modal message", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: defaultFormInputs,
								time_limit_checkin: 1,
								max_attempts_reservation: 1,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					state: {
						...reservations.state,
						data: null,
						inputsForm: [
							[
								{
									active: "true",
									name: "reservation_code",
									type: "text",
									minLength: "3",
									maxLength: "20",
								},
								{
									active: "true",
									name: "last_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
								{
									active: "false",
									name: "check_in",
									type: "date",
								},
								{
									active: "false",
									name: "check_out",
									type: "date",
								},
								{
									active: "false",
									name: "email",
									type: "email",
								},
								{
									active: "false",
									name: "first_name",
									type: "text",
									minLength: "2",
									maxLength: "50",
								},
							],
						],
					},
					getters: reservations.getters,
					actions: reservations.actions,
					mutations: reservations.mutations,
					namespaced: true,
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});
	
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () => {
			return Promise.reject(groupReservationError);
		});
	
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
	
		wrapper.vm.inputGroupSelected[0].forEach((input) => {
			if (input.active === "true") {
				input.value = "random value";
			}
		});
	
		const button = wrapper.find("btn-stub");
		await button.trigger("click");
	
		await flushPromises();
	
		expect(wrapper.vm.$data.isErrorMessage).toBe(true);
		expect(wrapper.vm.$data.modalMessage).toEqual("Unfortunately the online check-in process is not available due to the fact that your reservation is a group booking. Please go to the front desk to check in.");
	});
});

describe("Search with query params", () => {
	let store;
	let wrapper;
	let manageQueryFunction;
	let submitFunction;
	let checkResponseFunction;
	beforeEach(() => {
		manageQueryFunction = jest.spyOn(Search.methods, "manageQueryParams");
		submitFunction = jest.spyOn(Search.methods, "submit");
		checkResponseFunction = jest.spyOn(Search.methods, "checkResponse");
	});
	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
		manageQueryFunction.mockRestore();
		submitFunction.mockRestore();
		checkResponseFunction.mockRestore();
	});
	it("If token on query params and no encryption key, search should submit with token data", async () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(defaultReservation.data),
		);
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "true",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "false",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],

								max_attempts_reservation: 3,
							},
							token_key: "",
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					...reservations,
					state: {
						inputsForm: [defaultFormInputs],
					},
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				modal,
				queryParams: {
					...queryParams,
					state: {
						data: {
							token:
								"U2FsdGVkX1%2FOsNZ0MvJzPQ0l8vzT3TwuCSLo%2BEfgyG7XOqcoYF%2BhZjoRkbc7%2Fd8F",
						},
					},
				},
				loading,
				guest,
			},
		});

		wrapper = shallowMount(Search, {
			store,
			router,
			i18n,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});

		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());

		await flushPromises();

		expect(await manageQueryFunction).toBeCalledWith(defaultFormInputs);

		expect(await submitFunction).not.toBeCalledWith();
		expect(api.getReservation).not.toHaveBeenCalled();
		expect(checkResponseFunction).not.toHaveBeenCalled();
		expect(wrapper.vm.$router.push).not.toBeCalledWith();
	});
	it("If token on query params with encryption key, search should submit with token data", async () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(defaultReservation.data),
		);
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "true",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "false",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],

								max_attempts_reservation: 3,
							},
							token_key: "testkey",
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					...reservations,
					state: {
						inputsForm: [defaultFormInputs],
					},
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				modal,
				queryParams: {
					...queryParams,
					state: {
						data: {
							token:
								"U2FsdGVkX19V8k%2FQr%2BAh8QmM9EoDxScadMG5de8d3eP3qb3DQERBr0%2FTR%2Bc0AeQr",
						},
					},
				},
				loading,
				guest,
			},
		});

		wrapper = shallowMount(Search, {
			store,
			router,
			i18n,
			computed: {
				submitAvailable() {
					return true;
				},
			},
		});

		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());

		await flushPromises();

		expect(await manageQueryFunction).toBeCalledWith(defaultFormInputs);

		expect(await submitFunction).toBeCalledWith(
			[
				[
					{
						name: "reservation_code",
						value: "test",
					},
				],
			],
			{ isQueryParam: true },
		);
		expect(api.getReservation).toHaveBeenCalled();

		expect(checkResponseFunction).toHaveBeenCalled();
		expect(wrapper.vm.$router.push).toBeCalledWith({
			name: "Reservations",
		});
	});

	it("If token is malformed on query params, search should submit with token data", async () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(defaultReservation),
		);

		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "true",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "false",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],

								max_attempts_reservation: 3,
							},
							token_key: "testkey",
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				reservations: {
					...reservations,
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				modal,
				queryParams: {
					...queryParams,
					state: {
						data: {
							token: "TEST",
						},
					},
				},
				loading,
				guest,
			},
		});

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await flushPromises();

		expect(manageQueryFunction).toBeCalledWith(defaultFormInputs);

		expect(submitFunction).not.toBeCalled();

		expect(storeDispatch).toBeCalledWith("loading/LOADING", false);
		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);
	});

	it("If there's query params, but none matches with input names, submit should not be called", async () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(defaultReservation),
		);

		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "false",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "true",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],

								max_attempts_reservation: 3,
							},
						},
					},
				},
				reservations: {
					...reservations,
				},
				modal,
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},

				queryParams: {
					...queryParams,
					state: {
						data: {
							test: "test",
						},
					},
				},
				loading,
				guest,
			},
		});
		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});

		await flushPromises();

		expect(manageQueryFunction).toBeCalledWith([
			[
				{
					active: "true",
					name: "reservation_code",
					type: "text",
					minLength: "3",
					maxLength: "20",
				},
				{
					active: "false",
					name: "last_name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					active: "true",
					name: "check_in",
					type: "date",
				},
				{
					active: "false",
					name: "check_out",
					type: "date",
				},
				{
					active: "false",
					name: "email",
					type: "email",
				},
				{
					active: "false",
					name: "first_name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			],
		]);

		expect(submitFunction).not.toBeCalled();
	});

	it("If there's query params, and those match with input names, submit should be called", async () => {
		const api = repository.get("integration");
		api.getReservation = jest.fn(async () =>
			Promise.resolve(defaultReservation.data),
		);

		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
										},
										{
											active: "false",
											name: "last_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
										{
											active: "true",
											name: "check_in",
											type: "date",
										},
										{
											active: "false",
											name: "check_out",
											type: "date",
										},
										{
											active: "false",
											name: "email",
											type: "email",
										},
										{
											active: "false",
											name: "first_name",
											type: "text",
											minLength: "2",
											maxLength: "50",
										},
									],
								],

								max_attempts_reservation: 3,
							},
						},
					},
				},
				reservations: {
					...reservations,
					state: {
						reservations: [],
					},
				},
				modal,
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},

				queryParams: {
					...queryParams,
					state: {
						data: {
							reservation_code: "TEST",
							check_in: "1990-01-01",
						},
					},
				},
				loading,
				guest,
			},
		});

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});

		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());

		await flushPromises();

		expect(manageQueryFunction).toBeCalledWith([
			[
				{
					active: "true",
					name: "reservation_code",
					type: "text",
					minLength: "3",
					maxLength: "20",
				},
				{
					active: "false",
					name: "last_name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
				{
					active: "true",
					name: "check_in",
					type: "date",
				},
				{
					active: "false",
					name: "check_out",
					type: "date",
				},
				{
					active: "false",
					name: "email",
					type: "email",
				},
				{
					active: "false",
					name: "first_name",
					type: "text",
					minLength: "2",
					maxLength: "50",
				},
			],
		]);

		expect(submitFunction).toBeCalledWith(
			[
				[
					{
						name: "reservation_code",
						value: "TEST",
					},
					{
						name: "check_in",
						value: "1990-01-01",
					},
				],
			],
			{ isQueryParam: true },
		);

		expect(api.getReservation).toBeCalled();

		expect(checkResponseFunction).toBeCalled();

		expect(wrapper.vm.$router.push).toBeCalledWith({
			name: "Reservations",
		});
	});
});

describe("Search behaves accordingly to status of reservation", () => {
	let store;
	let wrapper;

	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
											value: "RESERVA",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				reservations,
				modal,
				queryParams,
				loading,
				guest,
			},
		});
	});

	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
		jest.restoreAllMocks();
	});

	it("if an eligible reservation is returned, user is taken to reservations page", async () => {
		const api = repository.get("integration");

		api.getReservation = jest.fn().mockResolvedValue(defaultReservation.data);

		const inputGroup =
			store.state.brand.config.identification.reservation_inputs;

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());
		wrapper.vm.submit(inputGroup, { isManual: true });

		await flushPromises();
		expect(wrapper.vm.$router.push).toBeCalledWith({
			name: "Reservations",
		});
	});

	it("if an object with action of redirect is returned, user is taken to confirmation page", async () => {
		const reservationWithCheckStateCheckIn = [
			{ ...defaultReservation.data[0], check_state: "check_in" },
		];

		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
											value: "RESERVA",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				reservations: {
					...reservations,
					state: {
						...reservations.state,
						reservationSelected: null,
						data: null,
					},
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});

		const api = repository.get("integration");

		api.getReservation = jest.fn().mockResolvedValue({
			action: "redirect",
			reservation: reservationWithCheckStateCheckIn,
			checkState: reservationWithCheckStateCheckIn[0].check_state,
		});

		const inputGroup =
			store.state.brand.config.identification.reservation_inputs;

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());
		wrapper.vm.submit(inputGroup, { isManual: true });

		await flushPromises();

		expect(wrapper.vm.$router.push).toBeCalledWith({
			name: "Confirmation",
		});
	});

	it("if an object with action of show modal is returned, user is shown a modal", async () => {
		const reservationWithCheckStateCheckOut = [
			{ ...defaultReservation.data[0], check_state: "check_out" },
		];

		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
											value: "RESERVA",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				reservations: {
					...reservations,
					state: {
						...reservations.state,
						reservationSelected: null,
						data: null,
					},
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});

		const api = repository.get("integration");

		api.getReservation = jest.fn().mockResolvedValue({
			action: "show modal",
			reservation: reservationWithCheckStateCheckOut,
			checkState: reservationWithCheckStateCheckOut[0].check_state,
		});

		const inputGroup =
			store.state.brand.config.identification.reservation_inputs;

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		wrapper.vm.submit(inputGroup, { isManual: true });

		await flushPromises();

		expect(wrapper.vm.$data.modalMessage).toContain(
			reservationWithCheckStateCheckOut[0].check_in,
		);
		expect(wrapper.vm.$data.modalMessage).toContain(
			reservationWithCheckStateCheckOut[0].check_out,
		);
	});

	it("in reception mode, if an eligible reservation is returned, user is taken to status page and language is correctly updated with holder's if available", async () => {
		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
											value: "RESERVA",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: true,
					},
				},
				reservations: {
					...reservations,
					state: {
						...reservations.state,
						reservationSelected: null,
						data: null,
					},
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});

		const api = repository.get("integration");
		api.getReservation = jest.fn().mockResolvedValue(receptionReservation.data);

		const inputGroup = [
			[
				{
					active: "true",
					error: false,
					maxLength: "20",
					minLength: "2",
					name: "room_number",
					type: "text",
					value: "0504",
				},
			],
		];

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());
		wrapper.vm.submit(inputGroup, { isManual: true });

		await flushPromises();

		expect(store.state.app.appLanguage).toBe("es");
		expect(wrapper.vm.$router.push).toBeCalledWith({
			name: "Status",
		});
	});

	it("in reception mode, if an object with action of show modal is returned, user is shown a modal", async () => {
		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
											value: "RESERVA",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: true,
					},
				},
				reservations: {
					...reservations,
					state: {
						...reservations.state,
						reservationSelected: null,
						data: null,
					},
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});

		const reservationWithCheckStateCheckOut = [
			{ ...defaultReservation.data[0], check_state: "check_out" },
		];

		const api = repository.get("integration");

		api.getReservation = jest.fn().mockResolvedValue({
			action: "show modal",
			reservation: reservationWithCheckStateCheckOut,
			checkState: reservationWithCheckStateCheckOut[0].check_state,
		});

		const inputGroup = [
			[
				{
					active: "true",
					error: false,
					maxLength: "20",
					minLength: "2",
					name: "room_number",
					type: "text",
					value: "0504",
				},
			],
		];

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		wrapper.vm.submit(inputGroup, { isManual: true });

		await flushPromises();

		expect(wrapper.vm.$data.modalMessage).toContain(
			reservationWithCheckStateCheckOut[0].check_in,
		);
		expect(wrapper.vm.$data.modalMessage).toContain(
			reservationWithCheckStateCheckOut[0].check_out,
		);
	});

	it("in reception mode, if an object with redirect is returned, user is taken to confirmation page", async () => {
		const reservationWithCheckStatePreCheckedIn = [
			{ ...defaultReservation.data[0], check_state: "pre_checked_in" },
		];

		store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						config: {
							identification: {
								reservation_filters: [
									[
										{
											position: 1,
											name: "check_in",
											type: "date",
										},
									],
									[
										{
											position: 2,
											name: "check_out",
											type: "date",
										},
									],
									[
										{
											position: 4,
											name: "reservation_code",
											type: "text",
										},
									],
									[
										{
											position: 3,
											name: "first_name",
											type: "text",
										},
									],
								],
								reservation_inputs: [
									[
										{
											active: "true",
											name: "reservation_code",
											type: "text",
											minLength: "3",
											maxLength: "20",
											value: "RESERVA",
										},
									],
								],
								time_limit_checkin: 1,
								timezone: "Europe/Paris",
								max_attempts_reservation: 10000,
							},
						},
					},
					getters: brand.getters,
					actions: brand.actions,
					namespaced: true,
				},
				app: {
					...app,
					state: {
						isReceptionMode: true,
					},
				},
				reservations: {
					...reservations,
					state: {
						...reservations.state,
						reservationSelected: null,
						data: null,
					},
				},
				modal,
				queryParams,
				loading,
				guest,
			},
		});

		const api = repository.get("integration");

		api.getReservation = jest.fn().mockResolvedValue({
			action: "redirect",
			reservation: reservationWithCheckStatePreCheckedIn,
			checkState: reservationWithCheckStatePreCheckedIn[0].check_state,
		});

		const inputGroup = [
			[
				{
					active: "true",
					error: false,
					maxLength: "20",
					minLength: "2",
					name: "room_number",
					type: "text",
					value: "0504",
				},
			],
		];

		wrapper = shallowMount(Search, {
			store,
			i18n,
			router,
		});
		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());
		wrapper.vm.submit(inputGroup, { isManual: true });

		await flushPromises();

		expect(wrapper.vm.$router.push).toBeCalledWith({
			name: "Confirmation",
		});
	});
});
