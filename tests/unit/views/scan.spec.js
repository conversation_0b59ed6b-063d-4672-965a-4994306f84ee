import Vuex from "vuex";
import { mount, shallowMount, createLocalVue } from "@vue/test-utils";
import flushPromises from "flush-promises";
import Scan from "@/views/Scan";
import Modal from "@/components/shared/modal.component";
import brand from "@/store/brand";
import scan from "@/store/scan";
import loading from "@/store/loading";
import guest from "@/store/guest";
import modal from "@/store/modal";
import queryParams from "@/store/queryParams";
import {
	childPassport,
	emptyScan,
	identityCardScan,
	errorScan,
	expiredPassportScan,
	passportScan,
	passportScanWithoutSignature,
} from "../../../mocks/modules/ocr/data";
import { i18n } from "@/locales";
import repository from "@/repository/repositoryFactory";
import redirectMixin from "@/mixins/redirect";
import reservationsModule from "@/store/reservations";
import { defaultReservation } from "../../../mocks/modules/integration/data";

const Ocr = repository.get("ocr");
const localVue = createLocalVue();
localVue.use(Vuex);

describe("Scan", () => {
	let store;
	let wrapper;
	let mockRouter;
	let redirect;
	beforeEach(() => {
		redirect = jest.spyOn(redirectMixin.methods, "redirect");
		mockRouter = { push: jest.fn(() => Promise.resolve()) };
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							optional_scan: true,
							custom_scan_text: false,
							partial_checkin: true,
							allow_expired_documents: true,
							not_allow_passports_from_country_brand: false
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "AD",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
						face: null,
						signatureUploadRequired: false,
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
						reservationSelected: {
							res_localizer: "testing-001",
						},
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		wrapper = shallowMount(Scan, { store, localVue, i18n });
		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.validateData = jest.fn(() => null);
		wrapper.vm.checkError = jest.fn(() => null);
	});

	afterEach(() => {
		jest.clearAllMocks();
		jest.resetAllMocks();
	});

	it("Should open upload image when closing modal", () => {
		// Click on upload image
		wrapper.find('[data-test="fileButton"]').trigger("click");
		expect(wrapper.find(".tips").isVisible()).toBe(true);
		// Click on "Understood" opens upload file
		wrapper.vm.uploadImageEvent = jest.fn();
		wrapper.find('[data-test="closeModal"').trigger("click");
		expect(wrapper.vm.uploadImageEvent).toBeCalledTimes(1);
	});

	it("If klippa does not retrieves document type, request document type only", async () => {
		expect(wrapper.find(".scan").exists()).toBe(true);

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').isVisible(),
		).toBe(true);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If passport is selected in modal, do not request nationality nor issuing country", async () => {
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "passport",
		});

		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If residence permit is selected in modal and issuing country was retrieved from Klippa, do not request issuing country", async () => {
		emptyScan.issuing_country = "ESP";
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "residence_permit",
		});

		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If residence permit is selected in modal and NO issuing country was retrieved from Klippa, request issuing country only", async () => {
		emptyScan.issuing_country = null;
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "residence_permit",
		});

		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').isVisible(),
		).toBe(true);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If ID is selected in modal and nationality was retrieved from Klippa, do not request nationality", async () => {
		emptyScan.issuing_country = null;
		emptyScan.nationality = "ESP";
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "identity_card",
		});

		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If driving license is selected in modal and nationality was retrieved from Klippa, do not request nationality", async () => {
		emptyScan.issuing_country = null;
		emptyScan.nationality = "ESP";
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "driving_license",
		});

		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If ID is selected in modal and NO nationality was retrieved from Klippa, request nationality only", async () => {
		emptyScan.issuing_country = null;
		emptyScan.nationality = null;
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "identity_card",
		});

		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').isVisible(),
		).toBe(true);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If driving license is selected in modal and NO nationality was retrieved from Klippa, request nationality only", async () => {
		emptyScan.issuing_country = null;
		emptyScan.nationality = null;
		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		await wrapper.setData({
			documentTypeValue: "driving_license",
		});

		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').isVisible(),
		).toBe(true);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If klippa retrieves residence permit as document type and issuing country, do not request extra information", async () => {
		emptyScan.document_type = "issuing_country";
		emptyScan.issuing_country = "ESP";

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If klippa retrieves residence permit as document type, but no issuing country, request issuing country only", async () => {
		emptyScan.document_type = "residence_permit";
		emptyScan.nationality = "ESP";
		emptyScan.issuing_country = null;

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').isVisible(),
		).toBe(true);
	});

	it("If klippa retrieves identity card as document type and nationality, do not request extra information", async () => {
		emptyScan.document_type = "identity_card";
		emptyScan.nationality = "ESP";

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If klippa retrieves driving license as document type and nationality, do not request extra information", async () => {
		emptyScan.document_type = "driving_license";
		emptyScan.nationality = "ESP";

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').exists(),
		).toBe(false);
	});

	it("If klippa retrieves identity card as document type, but no nationality, request nationality only", async () => {
		emptyScan.document_type = "identity_card";
		emptyScan.nationality = null;

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').isVisible(),
		).toBe(true);
	});

	it("If klippa retrieves driving license as document type, but no nationality, request nationality only", async () => {
		emptyScan.document_type = "driving_license";
		emptyScan.nationality = null;

		wrapper.vm.sendImage = jest.fn(() => emptyScan);

		await wrapper.vm.startImageProcessing();

		expect(
			wrapper.find('[data-test="document-type-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="issuing-country-sensible-input"]').exists(),
		).toBe(false);
		expect(
			wrapper.find('[data-test="nationality-sensible-input"]').isVisible(),
		).toBe(true);
	});

	it("Clicking on upload front document should change documentSide value", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		wrapper.find('[data-test="uploadDocument"]').trigger("click");

		expect(wrapper.vm.documentSide).toBe("front");
	});

	it("If document side is not retrieved, set it depending on uploaded button clicked", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		const signatureRequired = { identity_document_signature_required: false };

		identityCardScan.side = null;

		wrapper.vm.toBase64 = jest.fn(() => null);

		wrapper.vm.sendImage = jest.fn(async () =>
			Promise.resolve(identityCardScan.data),
		);
		wrapper.vm.checkGuestRepeated = jest.fn(() => false);

		wrapper.find('[data-test="uploadDocument"]').trigger("click");
		expect(wrapper.vm.documentSide).toBe("front");

		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });
		const scanStore = wrapper.vm.$store.state.scan;
		// Check if required documents are present
		await flushPromises();

		expect(
			scan.getters.getRequiredDocuments(scanStore)(signatureRequired),
		).toBe(false);
		expect(scanStore.identityDocuments).toMatchObject({
			identity_card_front: {
				title: "identity_card_front",
			},
		});
	});
	//Id
	it("Don't show Schengen zone error if identity card is inside Schengen Zone, when both nationality and document_type were not retrieved from Klippa", async () => {
		wrapper = await mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: null,
			},
			nationalityValue: "FRA",
			documentTypeValue: "identity_card",
		});
		wrapper.vm.validateData = jest.fn(() => null);
		wrapper.vm.checkError = jest.fn(() => null);

		const modal = wrapper.find('[data-test="modalError"]');

		await wrapper.vm.completeSensibleData();

		expect(modal.isVisible()).toBe(false);
		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if identity card is inside Schengen Zone, when nationality is retrieved from Klippa and document_type not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: "ITA",
			},
			documentTypeValue: "identity_card",
		});
		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if identity card is inside Schengen Zone, when document_type is retrieved from Klippa and nationality not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: "identity_card",
				nationality: null,
			},
			nationalityValue: "DEU",
		});

		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if identity card is outside Schengen Zone, but is the same than hotel's country", async () => {
		wrapper = await mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: null,
			},
			documentTypeValue: "identity_card",
			nationalityValue: "ARG",
		});
		store.state.brand.country = "AR";
		wrapper.vm.validateData = jest.fn(() => null);
		wrapper.vm.checkError = jest.fn(() => null);

		const modal = wrapper.find('[data-test="modalError"]');

		await wrapper.vm.completeSensibleData();

		expect(modal.isVisible()).toBe(false);
		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("should show Schengen zone error if identity card is from another country than the hotel and they are not both from Schegen Zone", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: "ARG",
			},
			documentTypeValue: "identity_card",
		});
		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).toHaveBeenCalledWith("SchengenZoneError");
	});
	//Driving license
	it("Don't show Schengen zone error if driving license's country is the same that the hotel's country, when both nationality and document_type were not retrieved from Klippa", async () => {
		wrapper = await mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: null,
			},
			nationalityValue: "ESP",
			documentTypeValue: "driving_license",
		});
		store.state.brand.config.allow_driving_license = true;
		wrapper.vm.validateData = jest.fn(() => null);
		wrapper.vm.checkError = jest.fn(() => null);

		const modal = wrapper.find('[data-test="modalError"]');

		await wrapper.vm.completeSensibleData();

		expect(modal.isVisible()).toBe(false);
		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if driving license's country is the same that the hotel's country, when nationality is retrieved from Klippa and document_type not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: "ESP",
			},
			documentTypeValue: "driving_license",
		});
		store.state.brand.config.allow_driving_license = true;
		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if driving license's country is the same that the hotel's country, when document_type is retrieved from Klippa and nationality not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: "driving_license",
				nationality: null,
			},
			nationalityValue: "ESP",
		});
		store.state.brand.config.allow_driving_license = true;
		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("should show Schengen zone error if document selected is a driving license of a different country than the hotel", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: null,
			},
			documentTypeValue: "driving_license",
			nationalityValue: "FRA",
		});
		store.state.brand.config.allow_driving_license = true;
		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).toHaveBeenCalledWith("SchengenZoneError");
	});

	it("should show driving license error if document selected is a driving license and this kind of document is not allow in the hotel configuration", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: "driving_license",
				nationality: null,
			},
			nationalityValue: "ESP",
		});
		store.state.brand.config.allow_driving_license = false;
		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).toHaveBeenCalledWith("drivingLicenseError");
	});

	//Passport
	it("Don't show Schengen zone error if passport is outside Schengen Zone, when both nationality and document_type were not retrieved from Klippa", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: null,
			},
			nationalityValue: "ARG",
			documentTypeValue: "passport",
		});

		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if passport is outside Schengen Zone, when nationality is retrieved from Klippa and document_type not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				nationality: "ARG",
			},
			documentTypeValue: "passport",
		});

		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if passport is outside Schengen Zone, when document type is retrieved from Klippa and nationality not", async () => {
		wrapper = await mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.setData({
			imageOcr: {
				document_type: "passport",
				nationality: null,
			},
			nationalityValue: "ARG",
		});
		wrapper.vm.validateData = jest.fn(() => null);
		wrapper.vm.checkError = jest.fn(() => null);

		const modal = wrapper.find('[data-test="modalError"]');

		await wrapper.vm.completeSensibleData();

		expect(modal.isVisible()).toBe(false);
		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	//Residence permit
	it("Don't show Schengen zone error if residence permit's issuing country is the same that the hotel's country, when both issuing country and document_type were not retrieved from Klippa", async () => {
		wrapper = await mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.setData({
			imageOcr: {
				document_type: null,
				issuing_country: null,
			},
			issuingCountryValue: "ESP",
			documentTypeValue: "residence_permit",
		});

		wrapper.vm.validateData = jest.fn(() => null);
		wrapper.vm.checkError = jest.fn(() => null);

		const modal = wrapper.find('[data-test="modalError"]');

		await wrapper.vm.completeSensibleData();

		expect(modal.isVisible()).toBe(false);
		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if residence permit's issuing country is the same that the hotel's country, when issuing country is retrieved from Klippa and document_type not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				issuing_country: "ESP",
			},
			documentTypeValue: "residence_permit",
		});

		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("Don't show Schengen zone error if residence permit's issuing country is the same that the hotel's country, when document_type is retrieved from Klippa and issuing country not", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: "residence_permit",
				issuing_country: null,
			},
			issuingCountryValue: "ESP",
		});

		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).not.toHaveBeenCalled();
		expect(wrapper.vm.validateData).toHaveBeenCalled();
	});

	it("should show Schengen zone error if document selected is a residence permit of a different country than the hotel", async () => {
		wrapper.setData({
			imageOcr: {
				document_type: null,
				issuing_country: null,
			},
			documentTypeValue: "residence_permit",
			issuingCountryValue: "FRA",
		});

		await wrapper.vm.completeSensibleData();

		expect(wrapper.vm.checkError).toHaveBeenCalledWith("SchengenZoneError");
	});

	it("Show modal error if ocr returns an error", async () => {
		Ocr.getImageInfo = jest.fn(() => errorScan("IM AN ERROR!"));
		await wrapper.vm.startImageProcessing();
		expect(wrapper.vm.checkError).toHaveBeenCalled();
	});

	it("If signature is required and signature is not detected in scanned passport, display a new slot for signature", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		store.state.brand.config.identity_document_signature_required = true;

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(async () =>
			Promise.resolve(passportScanWithoutSignature.data),
		);

		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		await flushPromises();

		const fileButtons = wrapper.findAll('[data-test="fileButton"]');
		expect(fileButtons.length).toBe(2);
	});

	it("If document front part is scanned, check document is present on store and button is disabled", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		const signatureRequired = { identity_document_signature_required: false };

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(async () =>
			Promise.resolve(identityCardScan.data),
		);
		wrapper.vm.checkGuestRepeated = jest.fn(() => false);

		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });
		const scanStore = wrapper.vm.$store.state.scan;
		// Check if required documents are present
		await flushPromises();

		expect(
			scan.getters.getRequiredDocuments(scanStore)(signatureRequired),
		).toBe(false);
		expect(scanStore.identityDocuments).toMatchObject({
			identity_card_front: {
				title: "identity_card_front",
			},
		});
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			true,
		);
	});

	it("If document is uploaded, and user clicks on image, modal should pop", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(async () =>
			Promise.resolve(identityCardScan.data),
		);
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		wrapper.vm.checkGuestRepeated = jest.fn(() => false);

		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		wrapper.find("[data-test='showDocumentModal']").trigger("click");
		await flushPromises();

		expect(storeDispatch.mock.calls).toContainEqual(
			["modal/SET_NAME", "documentModal"],
			["modal/VISIBLE", true],
		);
	});

	it("Allow expired document if option is enabled", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		const signatureRequired = { identity_document_signature_required: false };

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => expiredPassportScan.data);
		wrapper.vm.checkGuestRepeated = jest.fn(() => false);

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		expect(storeDispatch).not.toBeCalledWith("modal/VISIBLE");
		const scanStore = wrapper.vm.$store.state.scan;
		// Check if required documents are present
		expect(scanStore.identityDocuments).toMatchObject({
			passport: {
				title: "passport",
			},
		});
		expect(
			scan.getters.getRequiredDocuments(scanStore)(signatureRequired),
		).toBe(true);

		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);
	});

	it("Dont allow expired document if option is disabled", async () => {
		store.state.brand.config.allow_expired_documents = false;
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});
		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.checkGuestRepeated = jest.fn(() => false);

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		expect(wrapper.vm.$store.state.scan.identityDocuments).toMatchObject({});

		expect(storeDispatch.mock.calls).toContainEqual(
			["modal/SET_NAME", "errorModal"],
			["modal/VISIBLE", true],
		);
	});

	it("Don't show error modal if adult tries to scan child document", async () => {
		store.state.brand.config.allow_expired_documents = false;
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => childPassport.data);

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		expect(wrapper.vm.$store.state.scan.identityDocuments).toMatchObject({});

		expect(storeDispatch.mock.calls).not.toContainEqual(
			["modal/SET_NAME", "errorModal"],
			["modal/VISIBLE", true],
		);
	});

	it("If adult scans correct document, it validates", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		const signatureRequired = { identity_document_signature_required: false };

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => passportScan.data);

		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		expect(storeDispatch).not.toBeCalledWith("modal/VISIBLE");
		const scanStore = wrapper.vm.$store.state.scan;
		// Check if required documents are present
		expect(
			scan.getters.getRequiredDocuments(scanStore)(signatureRequired),
		).toBe(true);

		expect(scanStore.identityDocuments).toMatchObject({
			passport: {
				title: "passport",
			},
		});
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);
	});

	it("If all required documents are met, and user clicks on 'Continue' button, user should be sent to Validate data", async () => {
		store.state.brand.config.send_identity_documents_to_PMS = false;
		wrapper = mount(Scan, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
		});

		const signatureRequired = { identity_document_signature_required: false };

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => passportScan.data);

		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const continueButton = wrapper.find('[data-test="continue-button"]');
		expect(storeDispatch).not.toBeCalledWith("modal/VISIBLE");
		const scanStore = wrapper.vm.$store.state.scan;
		// Check if required documents are present
		expect(
			scan.getters.getRequiredDocuments(scanStore)(signatureRequired),
		).toBe(true);

		expect(scanStore.identityDocuments).toMatchObject({
			passport: {
				title: "passport",
			},
		});

		expect(continueButton.element.disabled).toBe(false);
		await continueButton.trigger("click");
		await flushPromises();
		expect(redirect).toBeCalledWith({ name: "ValidateData" });
	});

	it("Should call the addFaceImage mutation with the given image", async () => {
		const image = "image-data";
		const mockCommit = jest.fn();
		const context = { commit: mockCommit };

		scan.actions.ADD_FACE_IMAGE(context, image);

		expect(mockCommit).toHaveBeenCalledWith("addFaceImage", image);
	});

	it("Should update the state's face property when committing the addFaceImage mutation", async () => {
		const image = "image-data";

		scan.mutations.addFaceImage(scan.state, image);

		expect(scan.state.face).toBe(image);
	});

	it("Should call ADD_FACE_IMAGE mutation when scanData.face is available", async () => {
		const scanData = { face: "face-image-data" };
		const base64ImageSource = "base64-image-data";
		const imageType = "image/jpeg";

		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		const mockAddFaceImage = jest.fn();
		wrapper.vm.ADD_FACE_IMAGE = mockAddFaceImage;

		await wrapper.vm.manageIdentityDocument(scanData, {
			base64ImageSource,
			imageType,
		});
		expect(mockAddFaceImage).toHaveBeenCalledWith("face-image-data");
	});

	it("Should not call ADD_FACE_IMAGE mutation when scanData.face is not available", async () => {
		const scanData = {};
		const base64ImageSource = "base64-image-data";
		const imageType = "image/jpeg";

		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		const mockAddFaceImage = jest.fn();
		wrapper.vm.ADD_FACE_IMAGE = mockAddFaceImage;

		await wrapper.vm.manageIdentityDocument(scanData, {
			base64ImageSource,
			imageType,
		});
		expect(mockAddFaceImage).not.toHaveBeenCalled();
	});
});

describe("Child scan", () => {
	let store;
	let wrapper;

	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							optional_scan: true,
							scan_children_like_adults: true,
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "CH",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
						reservationSelected: {
							res_localizer: "testing-001",
						},
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		wrapper = mount(Scan, { store, localVue, i18n });
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Show error modal if child tries to scan adult document", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});

		wrapper.vm.toBase64 = jest.fn(() => "A long base64");
		wrapper.vm.sendImage = jest.fn(() => passportScan.data);

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		expect(storeDispatch.mock.calls).toContainEqual(
			["modal/SET_NAME", "errorModal"],
			["modal/VISIBLE", true],
		);
	});

	it("If child scans correct document, it validates", async () => {
		wrapper = mount(Scan, {
			store,
			localVue,
			i18n,
		});
		jest.mock("@/store/scan");

		const signatureRequired = { identity_document_signature_required: false };

		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => childPassport.data);
		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await flushPromises();
		expect(storeDispatch).not.toBeCalledWith("modal/VISIBLE");
		const scanStore = wrapper.vm.$store.state.scan;
		// Check if required documents are present
		expect(
			scan.getters.getRequiredDocuments(scanStore)(signatureRequired),
		).toBe(true);

		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);
	});
});

describe("Scan page with advanced_scan config activated without media device permissions", () => {
	let store;
	let wrapper;
	let mockRouter;

	beforeEach(() => {
		mockRouter = { push: jest.fn(() => Promise.resolve()) };
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							optional_scan: true,
							custom_scan_text: false,
							partial_checkin: true,
							allow_expired_documents: true,
							advanced_scan: true,
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "AD",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
						reservationSelected: {
							res_localizer: "testing-001",
						},
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		wrapper = mount(Scan, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
		jest.resetAllMocks();
	});
	it("Click on scan document with advanced scan product activated, sould redirect to advancedScan page", async () => {
		wrapper.vm.$data.modalShown = true;
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.uploadImageEvent();

		expect(wrapper.vm.$router.push).not.toBeCalledWith(
			expect.objectContaining({
				name: "AdvancedScan",
			}),
		);
		expect(storeDispatch.mock.calls).toContainEqual(["modal/VISIBLE", false]);
	});
});

describe("Scan page with advanced_scan config activated and media device permissions", () => {
	let store;
	let wrapper;
	let mockRouter;
	beforeEach(() => {
		mockRouter = { push: jest.fn(() => Promise.resolve()) };
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							optional_scan: true,
							custom_scan_text: false,
							partial_checkin: true,
							allow_expired_documents: true,
							advanced_scan: true,
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "AD",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
						reservationSelected: {
							res_localizer: "testing-001",
						},
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		const mockMediaDevices = {
			getUserMedia: jest.fn(async () => {
				return new Promise((resolve) => {
					resolve();
				});
			}),
		};
		Object.defineProperty(window.navigator, "mediaDevices", {
			writable: true,
			value: mockMediaDevices,
		});

		wrapper = mount(Scan, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
		jest.resetAllMocks();
	});
	it("Click on scan document with advanced scan product activated, sould redirect to advancedScan page", async () => {
		wrapper.vm.$data.modalShown = true;
		await wrapper.vm.uploadImageEvent();

		expect(wrapper.vm.$router.push).toBeCalledWith(
			expect.objectContaining({
				name: "AdvancedScan",
			}),
		);
	});
});
