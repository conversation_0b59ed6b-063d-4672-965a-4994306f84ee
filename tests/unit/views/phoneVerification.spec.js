import Vuex from "vuex";
import Vuelidate from "vuelidate";
import VueRouter from "vue-router";
import PhoneVerification from "@/views/PhoneVerification";
import { mount, createLocalVue } from "@vue/test-utils";
import router from "@/router";
import { i18n } from "@/locales";
import brand from "@/store/brand";
import reservations from "@/store/reservations";
import phone from "@/store/phone";
import guest from "@/store/guest";
import modal from "@/store/modal";
import repository from "@/repository/repositoryFactory";
import loading from "@/store/loading";
const localVue = createLocalVue();
localVue.use(Vuex);
localVue.use(Vuelidate);
localVue.use(VueRouter);
const api = repository.get("checkin");
describe("Phone verification page", () => {
  let store;
  let wrapper;
  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: 1,
            mainColor: "123",
            country: "ES",
            config: {
              max_attempts_telephone: 3,
              max_attempts_validate_telephone_code: 3,
              telephone_notifications: true,
              telephone: true
            }
          }
        },
        guest,
        phone,
        reservations,
        modal,
        loading
      }
    });

    api.validatePhone = jest.fn(async () =>
      Promise.resolve({
        data: {}
      })
    );
    api.sendPhoneCode = jest.fn(async () =>
      Promise.resolve({
        data: {}
      })
    );

    wrapper = mount(PhoneVerification, {
      localVue,
      router,
      store,
      i18n
    });
  });
  it("Should be rendered", async () => {
    wrapper = mount(PhoneVerification, {
      localVue,
      router,
      store,
      i18n
    });
    expect(wrapper.find(".phone-verification").exists()).toBe(true);
  });

  it("button should be disabled, until code is written on input", async () => {
    wrapper = mount(PhoneVerification, {
      localVue,
      router,
      store,
      i18n
    });
    expect(wrapper.find('[data-test="validate-phone"]').element.disabled).toBe(
      true
    );
    wrapper.find('[data-test="verification_code"]').setValue("2151");
    await localVue.nextTick();
    expect(wrapper.find('[data-test="validate-phone"]').element.disabled).toBe(
      false
    );
  });

  it("Clicking on resend code button should call to api", async () => {
    wrapper = mount(PhoneVerification, {
      localVue,
      router,
      store,
      i18n
    });
    expect(wrapper.find('[data-test="resend-code"]').exists()).toBe(true);
    wrapper.find('[data-test="resend-code"]').trigger("click");

    await localVue.nextTick();

    expect(await api.validatePhone).toBeCalledTimes(1);
  });

  it("Clicking on verify phone button should call to api and if response is Ok, should send you to comments", async () => {
    wrapper = mount(PhoneVerification, {
      localVue,
      router,
      store,
      i18n
    });
    wrapper.vm.$router.push = jest.fn(async () => Promise.resolve());
    jest.spyOn(wrapper.vm, "redirect");
    wrapper.find('[data-test="verification_code"]').setValue("2151");
    await localVue.nextTick();
    wrapper.find('[data-test="validate-phone"]').trigger("click");
    jest.spyOn(wrapper.vm, "redirect");
    await localVue.nextTick();
    expect(await api.sendPhoneCode).toBeCalledTimes(1);
    // ! Todo: Make this work
    // await localVue.nextTick();
    // expect(await wrapper.vm.redirect).toBeCalledWith({ name: "Comments" });
  });
});
