import { shallowMount } from "@vue/test-utils";
import ErrorView from "@/views/Error.vue";

describe("Error view", () => {
  it("should load title", () => {
    const wrapper = shallowMount(ErrorView, {
      mocks: {
        $store: {
          state: {
            brand: {
              brandId: 12,
              mainColor: "123"
            }
          },
          dispatch: jest.fn()
        },
        $route: {
          params: {
            id: 123
          }
        },
        $t: () => ""
      }
    });
    const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
    expect(storeDispatch).toHaveBeenCalledWith("loading/LOADING", true);
    expect(wrapper.find(".error").exists()).toBe(true);
  });
});
