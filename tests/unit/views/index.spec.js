import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import flushPromises from "flush-promises";
import Index from "@/views/Index";
import repository from "@/repository/repositoryFactory";
import brand from "@/store/brand";
import loading from "@/store/loading";
import app from "@/store/app";
import redirectMixin from "@/mixins/redirect";

import {
  defaultBrand,
  defaultConfig,
  chainBrand
} from "../../../mocks/modules/brand/data";

const localVue = createLocalVue();
const api = repository.get("brand");

localVue.use(Vuex);

describe("Index", () => {
  let store;
  let wrapper;
  let mockRouter;
  let redirect;
  beforeEach(async () => {
    redirect = jest.spyOn(redirectMixin.methods, "redirect");
    mockRouter = { push: jest.fn(() => Promise.resolve()) };

    api.getBrand = jest.fn(async () => Promise.resolve(defaultBrand));

    api.getConfig = jest.fn(async () => Promise.resolve(defaultConfig));

    store = new Vuex.Store({
      modules: {
        brand,
        app,
        loading
      }
    });
    wrapper = shallowMount(Index, {
      mocks: {
        $router: mockRouter
      },
      store,
      localVue
    });
    await flushPromises();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Fetches both brandInfo and brandConfig", async () => {
    expect(api.getBrand).toHaveBeenCalledTimes(1);
    expect(api.getConfig).toHaveBeenCalledTimes(1);
  });

  it("If its autocheckin, redirect to Privacy Page", async () => {
    expect(redirect).toBeCalledWith({ name: "Privacy" });
  });

  it("If is reception and reception_signature is active, redirect to Search Page", async () => {
    store = new Vuex.Store({
      modules: {
        brand,
        app,
        loading
      }
    });
    wrapper = shallowMount(Index, {
      mocks: {
        $router: mockRouter
      },
      store,
      localVue
    });
    wrapper.vm.$store.state.app.isReceptionMode = true;
    wrapper.vm.$store.state.brand.config.reception_signature = true;
    await flushPromises();
    expect(redirect).toBeCalledWith({ name: "Search" });
  });

  it("If is reception and reception_signature is NOT active, redirect to Error Page", async () => {
    store = new Vuex.Store({
      modules: {
        brand,
        app,
        loading
      }
    });
    wrapper = shallowMount(Index, {
      mocks: {
        $router: mockRouter
      },
      store,
      localVue
    });
    wrapper.vm.$store.state.app.isReceptionMode = true;
    wrapper.vm.$store.state.brand.config.reception_signature = false;
    await flushPromises();
    expect(redirect).toBeCalledWith({ name: "Error" });
  });

  it("If error while fetching, redirect to Error Page", async () => {
    api.getBrand = jest.fn(async () => Promise.reject());

    store = new Vuex.Store({
      modules: {
        brand,
        app,
        loading
      }
    });
    wrapper = shallowMount(Index, {
      mocks: {
        $router: mockRouter
      },
      store,
      localVue
    });
    await flushPromises();
    expect(api.getBrand).toHaveBeenCalledTimes(1);
    expect(redirect).toBeCalledWith({ name: "Error" });
  });

  it("If brand is a chain, redirect to BrandSelector Page", async () => {
    api.getBrand = jest.fn(async () => Promise.resolve(chainBrand));

    store = new Vuex.Store({
      modules: {
        brand,
        app,
        loading
      }
    });
    wrapper = shallowMount(Index, {
      mocks: {
        $router: mockRouter
      },
      store,
      localVue
    });
    await flushPromises();
    expect(api.getBrand).toHaveBeenCalledTimes(1);
    expect(redirect).toBeCalledWith({ name: "BrandSelector" });
  });
});
