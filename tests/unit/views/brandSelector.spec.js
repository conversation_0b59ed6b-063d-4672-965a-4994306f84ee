import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import BrandSelector from "@/views/BrandSelector";
import loading from "@/store/loading";
import router from "@/router/index.js";
import brand from "@/store/brand";
import queryParams from "@/store/queryParams";
import app from "@/store/app";
import { i18n } from "@/locales";
import flushPromises from "flush-promises";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("BrandSelector", () => {
	let actions;
	let store;
	let wrapper;

	beforeEach(() => {
		actions = {
			SET_BRAND_ID: jest.fn(),
		};

		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						children: [
							{
								id: 1,
								name: "Brand 1",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 300,
								name: "Brand 300",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
						],
					},
					actions,
				},
				app: {
					...app,
					state: {
						isReceptionMode: false,
					},
				},
				loading,
			},
		});

		wrapper = shallowMount(BrandSelector, { store, localVue, i18n, router });
	});

	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
	});

	it("Render page correctly", async () => {
		expect(wrapper.find(".brandSelector").exists()).toBe(true);
	});

	it("getBrandOptions function returns correct data", async () => {
		const brandOptions = await wrapper.vm.getBrandOptions();

		expect(brandOptions).toMatchObject([
			{ value: "1", name: "Brand 1" },
			{ value: "300", name: "Brand 300" },
		]);
	});

	it("Changes brand id on store when start checkin button clicked", async () => {
		wrapper.setData({
			brandId: "999",
		});

		await wrapper.find('[data-test="brandSelectorButton"]').trigger("click");

		wrapper.vm.$nextTick(() => {
			expect(actions.SET_BRAND_ID).toHaveBeenCalled();
		});
	});

	it("If there are brands with the autocheckin deactivated, do not show it in the selector and sort the others alphabetically", async () => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						children: [
							{
								id: 1,
								name: "Brand C",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 0,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 5451,
								name: "Brand B",
								products: [
									{
										id: 95612,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 300,
								name: "Brand A",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
						],
					},
					actions,
				},
				loading,
			},
		});

		wrapper = shallowMount(BrandSelector, { store, localVue, i18n, router });

		const brandOptions = await wrapper.vm.getBrandOptions();

		expect(brandOptions).toMatchObject([
			{ value: "300", name: "Brand A" },
			{ value: "5451", name: "Brand B" },
		]);
	});

	it("If there is brand_id param, should redirect automatically to brand page", async () => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						children: [
							{
								id: 1,
								name: "Brand C",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 0,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 5451,
								name: "Brand B",
								products: [
									{
										id: 95612,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 300,
								name: "Brand A",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
						],
					},
					actions,
				},
				queryParams: {
					...queryParams,
					state: {
						data: {
							brand_id: 300,
						},
					},
				},
				loading,
			},
		});

		const startCheckinFunction = jest.spyOn(
			BrandSelector.methods,
			"startCheckin",
		);

		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());
		await flushPromises();
		wrapper = shallowMount(BrandSelector, { store, localVue, i18n, router });

		expect(startCheckinFunction).toBeCalled();
	});

	it("If there is brand_id param from not active autocheckin, should stay in brand selector page", async () => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						children: [
							{
								id: 1,
								name: "Brand C",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 0,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 5451,
								name: "Brand B",
								products: [
									{
										id: 95612,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
							{
								id: 300,
								name: "Brand A",
								products: [
									{
										id: 209,
										product_id: 21,
										active: 1,
										name: "autocheckin",
										active_by_name: {
											autocheckin: 1,
										},
									},
								],
							},
						],
					},
					actions,
				},
				queryParams: {
					...queryParams,
					state: {
						data: {
							brand_id: 1,
						},
					},
				},
				loading,
			},
		});

		const startCheckinFunction = jest.spyOn(
			BrandSelector.methods,
			"startCheckin",
		);

		wrapper.vm.$router.push = jest.fn(() => Promise.resolve());
		await flushPromises();
		wrapper = shallowMount(BrandSelector, { store, localVue, i18n, router });

		expect(startCheckinFunction).not.toBeCalled();
	});
});
