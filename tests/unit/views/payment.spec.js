import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import { i18n } from "@/locales";
import Payment from "@/views/Payment.vue";
import paymentModule from "@/store/payment.js";
import loading from "@/store/loading.js";
import modal from "@/store/modal.js";
import brandModule from "@/store/brand.js";
import reservationModule from "@/store/reservations.js";
import repository from "@/repository/repositoryFactory.js";
import {
  charges,
  sanetizedCharges
} from "../../../mocks/modules/integration/data.js";
import { integrations } from "../../../mocks/modules/payment/data.js";
const integrationAPI = repository.get("integration");
const paymentAPI = repository.get("payment");

const localVue = createLocalVue();
localVue.use(Vuex);
// const router = new VueRouter();

describe("Payment page", () => {
  let store;
  let payment;
  let brand;
  let reservations;
  beforeEach(() => {
    brand = {
      ...brandModule,
      state: {
        brandId: "1",
        mainColor: "123",
        country: "ES"
      }
    };

    payment = {
      ...paymentModule
    };

    reservations = {
      ...reservationModule,
      state: {
        data: [
          {
            // res_localizer: "TESTLOCALIZER7",
            res_adults: "1",
            res_children: "1",
            res_id: "145316-001",
            res_nights: "4",
            res_room_type: "DH"
          }
        ],

        reservationSelected: {
          // res_localizer: "TESTLOCALIZER7",
          res_adults: "1",
          res_children: "1",
          res_id: "145316-001",
          res_nights: "4",
          res_room_type: "DH"
        }
      }
    };

    store = new Vuex.Store({
      modules: {
        brand,
        loading,
        payment,
        reservations,
        modal
      }
    });
    paymentAPI.getBrandIntegrations = jest.fn(async () =>
      Promise.resolve(integrations.data)
    );
    integrationAPI.getLocalizerCharges = jest.fn(async () =>
      Promise.resolve(charges.data)
    );
  });

  it("Payment page is mounted", async () => {
    // router.push = jest.fn();
    // localVue.use(VueRouter);

    await shallowMount(Payment, {
      localVue,
      store,
      // router,
      i18n
    });

    expect(await paymentAPI.getBrandIntegrations).toHaveBeenCalledWith("1");
  });

  //* TODO: make this test work

  // it("Sends to comments if no invoices or integrations", async () => {
  //   paymentAPI.getBrandIntegrations = jest.fn(async () => Promise.resolve([]));
  //   integrationAPI.getLocalizerCharges = jest.fn(async () =>
  //     Promise.resolve(noCharges.data)
  //   );
  //   const mockRouter = {
  //     // push: jest.fn(async ()=> return {catch: jest.fn()}),
  //     push: jest.fn(async () => Promise.resolve([]).catch())
  //   };
  //   const mockRoute = { name: "Comments" };

  //   let wrapper = await shallowMount(Payment, {
  //     mocks: {
  //       $router: mockRouter,
  //       $route: mockRoute
  //     },
  //     localVue,
  //     store,
  //     i18n
  //   });
  //   // console.log("existe", wrapper.vm.redirect);
  //   wrapper.vm.redirect = jest.fn();

  //   expect(await paymentAPI.getBrandIntegrations).toHaveBeenCalledWith("1");

  //   expect(wrapper.vm.redirect).toBeCalled();
  // });

  it("Test charges are set on page", async () => {
    const wrapper = await shallowMount(Payment, {
      localVue,
      store,
      i18n
    });

    charges.data.reservations.forEach((charge, index) => {
      expect(
        wrapper.find(`[data-test="reservation-selector-${index}"]`).exists()
      ).toBe(true);
    });
    expect(wrapper.find('[data-test="localizer-selector"]').exists()).toBe(
      true
    );
    expect(wrapper.find('[data-test="no-payment"]').exists()).toBe(true);
  });

  it("Test charges payment object is santeized as expected", async () => {
    const wrapper = await shallowMount(Payment, {
      localVue,
      store,
      i18n
    });

    expect(wrapper.vm.charges).toEqual(sanetizedCharges);
  });

  it("Test localizerAmount sets pricing correctly", async () => {
    const wrapper = await shallowMount(Payment, {
      localVue,
      store,
      i18n
    });
    console.log(wrapper.html());
    const localizerAmount = wrapper.vm.localizerAmount;

    expect(localizerAmount).toBe(450);
    expect(wrapper.find('[data-test="total-amount"]').text()).toEqual(
      "450 EUR"
    );
  });

  it("Depending on payment option selected, data variables should change", async () => {
    const wrapper = await shallowMount(Payment, {
      localVue,
      store,
      i18n
    });
    const button = wrapper.find('[data-test="payment-button"]');
    expect(button.props()["disabled"]).toBe(true);
    // Simulate clicking on no paying button
    let spy = jest.spyOn(wrapper.vm, "noPayment");
    wrapper.vm.noPayment();

    await wrapper.vm.$nextTick();
    expect(spy).toHaveBeenCalled();

    expect(wrapper.vm.noPaymentSelected).toBe(true);
    expect(wrapper.vm.charges.localizer.selected).toBe(false);
    // every charge should be false
    expect(
      wrapper.vm.charges.reservations.every(charge => !charge.selected)
    ).toBe(true);
    expect(button.props()["disabled"]).toBe(false);

    // Simulate clicking on localizerSelected button
    spy = jest.spyOn(wrapper.vm, "localizerSelected");
    wrapper.vm.localizerSelected();

    await wrapper.vm.$nextTick();
    expect(spy).toHaveBeenCalled();

    expect(wrapper.vm.noPaymentSelected).toBe(false);
    expect(wrapper.vm.charges.localizer.selected).toBe(true);
    // every charge should be false
    expect(
      wrapper.vm.charges.reservations.every(charge => !charge.selected)
    ).toBe(true);
    expect(button.props()["disabled"]).toBe(false);

    // Simulate clicking on selectReservation button
    spy = jest.spyOn(wrapper.vm, "selectReservation");
    wrapper.vm.selectReservation(wrapper.vm.charges.reservations[0]);

    await wrapper.vm.$nextTick();
    expect(spy).toHaveBeenCalled();

    expect(wrapper.vm.noPaymentSelected).toBe(false);
    expect(wrapper.vm.charges.localizer.selected).toBe(false);
    // once charge should be selected
    expect(
      wrapper.vm.charges.reservations.some(charge => charge.selected)
    ).toBe(true);
    expect(wrapper.vm.charges.reservations[0].selected).toBe(true);
    expect(button.props()["disabled"]).toBe(false);
  });

  it("Test handlePaymentsResponse method", async () => {
    // Invoices and integrations
    paymentAPI.getBrandIntegrations = jest.fn(async () =>
      Promise.resolve(integrations.data)
    );
    integrationAPI.getLocalizerCharges = jest.fn(async () =>
      Promise.resolve(charges.data)
    );
    let wrapper = await shallowMount(Payment, {
      localVue,
      store,
      i18n
    });

    const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
    expect(storeDispatch).not.toBeCalledWith();

    // No integrations but invoices

    paymentAPI.getBrandIntegrations = jest.fn(async () => Promise.resolve([]));
    integrationAPI.getLocalizerCharges = jest.fn(async () =>
      Promise.resolve(charges.data)
    );
    wrapper = await shallowMount(Payment, {
      localVue,
      store,
      i18n
    });

    expect(storeDispatch).not.toBeCalledWith(
      "modal/SET_NAME",
      "paymentInformation"
    );
    expect(storeDispatch).not.toBeCalledWith("modal/SET_TYPE", "info");
    expect(storeDispatch).not.toBeCalledWith("modal/VISIBLE", true);

    // No invoices but integrations

    //* TODO: ADD TEST
  });
});
