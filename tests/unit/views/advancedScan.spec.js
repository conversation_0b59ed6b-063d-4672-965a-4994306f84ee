import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import AdvancedScan from "@/views/AdvancedScan";
import brand from "@/store/brand";
import scan from "@/store/scan";
import loading from "@/store/loading";
import guest from "@/store/guest";
import modal from "@/store/modal";
import queryParams from "@/store/queryParams";
import {
	emptyScan,
	identityCardScan,
	errorScan,
	expiredPassportScan,
	childPassport,
} from "../../../mocks/modules/ocr/data";
import { i18n } from "@/locales";
import repository from "@/repository/repositoryFactory";
import reservationsModule from "@/store/reservations";
import redirectMixin from "@/mixins/redirect";
import { defaultReservation } from "../../../mocks/modules/integration/data";
import { setupJestCanvasMock } from "jest-canvas-mock";
import advancedScanTranslations from "@/locales/en/AdvancedScan.view";
const Ocr = repository.get("ocr");

const localVue = createLocalVue();
localVue.use(Vuex);

describe("Advanced Scan with userMedia permissions", () => {
	let store;
	let wrapper;
	let mockRouter;
	let mockMediaDevices;
	let redirect;

	beforeEach(() => {
		setupJestCanvasMock();
		redirect = jest.spyOn(redirectMixin.methods, "redirect");
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							advanced_scan: true,
							allow_expired_documents: false,
							not_allow_passports_from_country_brand: false
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "AD",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		mockMediaDevices = {
			getUserMedia: jest.fn(async () => {
				return new Promise((resolve) => {
					resolve();
				});
			}),
		};
		Object.defineProperty(window.navigator, "mediaDevices", {
			writable: true,
			value: mockMediaDevices,
		});

		wrapper = mount(AdvancedScan, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
		jest.resetAllMocks();
	});

	it("Get user media called on mount Advanced Scan Page", async () => {
		await localVue.nextTick();

		expect(mockMediaDevices.getUserMedia).toBeCalled();
		expect(wrapper.vm.documentSide).toBe("front");
	});

	it("Make photo should call takeSnapshotandProcess function", async () => {
		const takeSnapshotFunction = jest.spyOn(wrapper.vm, "takeSnapshotandProcess");
		const startImageProcessing = jest.spyOn(wrapper.vm, "startImageProcessing");

		const makePhotoButton = wrapper.find('[data-test="makePhotoButton"]');
	
		expect(makePhotoButton.element.disabled).toBe(false);

		makePhotoButton.trigger("click");

		expect(takeSnapshotFunction).toBeCalled();

		await localVue.nextTick();

		expect(startImageProcessing).toBeCalled();
		expect(wrapper.vm.photo).not.toBe(null);
	});

	it("Delete photo should restart process", async () => {
		const deletePhotoButton = wrapper.find('[data-test="deletePhotoButton"]');
		const makePhotoButton = wrapper.find('[data-test="makePhotoButton"]');

		deletePhotoButton.trigger("click");

		expect(makePhotoButton.element.disabled).toBe(false);
		expect(wrapper.vm.photo).toBe(null);
	});

	it("If no sensible data retrieved redirect to sensibleData page", async () => {
		wrapper.vm.sendImage = jest.fn(() => emptyScan.data);
		wrapper.vm.$data.photo =
			"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==";

		await wrapper.vm.startImageProcessing(wrapper.vm.$data.photo);

		expect(wrapper.vm.$router.push).toBeCalledWith(
			expect.objectContaining({
				name: "SensibleData",
			}),
		);
	});

	it("If complete scan, check if data stored and redirect to scan page", async () => {
		wrapper.vm.sendImage = jest.fn(() => identityCardScan.data);
		wrapper.vm.$data.photo =
			"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==";

		await wrapper.vm.startImageProcessing(wrapper.vm.$data.photo);
		const scanStore = wrapper.vm.$store.state.scan;

		expect(redirect).toBeCalledWith({
			name: "Scan",
			params: { imageProcessed: true },
		});
		expect(scanStore.identityDocuments).toMatchObject({
			identity_card_front: {
				title: "identity_card_front",
			},
		});
	});

	it("Show modal error if ocr returns an error", async () => {
		wrapper.vm.checkError = jest.fn(() => null);
		Ocr.getImageInfo = jest.fn(() => errorScan("Error!"));
		wrapper.vm.$data.photo =
			"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==";

		await wrapper.vm.startImageProcessing(wrapper.vm.$data.photo);
		expect(wrapper.vm.checkError).toHaveBeenCalled();
	});

	it("Dont allow expired document if option is disabled", async () => {
		wrapper.vm.checkError = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => expiredPassportScan.data);
		wrapper.vm.checkGuestRepeated = jest.fn(() => false);
		wrapper.vm.$data.photo =
			"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==";

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.startImageProcessing(wrapper.vm.$data.photo);

		expect(wrapper.vm.$store.state.scan.identityDocuments).toMatchObject({});
		expect(storeDispatch.mock.calls).not.toContainEqual(
			["modal/SET_NAME", "errorModal"],
			["modal/VISIBLE", true],
		);
	});

	it("Don't show error modal if adult tries to scan child document", async () => {
		wrapper.vm.toBase64 = jest.fn(() => null);
		wrapper.vm.sendImage = jest.fn(() => childPassport.data);

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.vm.startImageProcessing({ type: "image/jpeg" });

		expect(wrapper.vm.$store.state.scan.identityDocuments).toMatchObject({});

		expect(storeDispatch.mock.calls).not.toContainEqual(
			["modal/SET_NAME", "errorModal"],
			["modal/VISIBLE", true],
		);
	});
});

describe("Advanced Scan without userMedia permissions", () => {
	let store;
	let wrapper;
	let mockRouter;
	let mockMediaDevices;

	beforeEach(() => {
		setupJestCanvasMock();
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							advanced_scan: true,
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "AD",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		mockMediaDevices = {
			getUserMedia: jest.fn(async () => {
				return new Promise((resolve, reject) => {
					reject({
						code: 0,
						message: "Permission denied",
						name: "NotAllowedError",
					});
				});
			}),
		};
		Object.defineProperty(window.navigator, "mediaDevices", {
			writable: true,
			value: mockMediaDevices,
		});

		wrapper = mount(AdvancedScan, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
		jest.resetAllMocks();
	});

	it("Get user media called on mount Advanced Scan Page", async () => {
		expect(mockMediaDevices.getUserMedia).toBeCalled();
		expect(wrapper.vm.documentSide).toBe("front");
	});

	it("If camera permission denied should print an error and a button to upload a file", async () => {
		const makePhotoButton = wrapper.find('[data-test="makePhotoButton"]');

		expect(wrapper.find('[data-test="userMediaError"]').text()).toEqual(
			advancedScanTranslations.cameraDeined,
		);
		expect(wrapper.find('[data-test="uploadPhotoButton"]').exists()).toBe(true);
		expect(makePhotoButton.element.disabled).toBe(true);
	});

	it("If upload photo button clicked, should request for file", async () => {
		wrapper.vm.uploadFile = jest.fn();
		wrapper.find('[data-test="uploadPhotoButton"]').trigger("click");

		expect(wrapper.vm.uploadFile).toBeCalledTimes(1);
	});
});

describe("Advanced Scan with userMedia error", () => {
	let store;
	let wrapper;
	let mockRouter;
	let mockMediaDevices;

	beforeEach(() => {
		setupJestCanvasMock();
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							advanced_scan: true,
						},
					},
				},
				guest: {
					...guest,
					getters: {
						getSelectedGuest: () => {
							return {
								pax_type: "AD",
							};
						},
					},
					namespaced: true,
				},
				scan: {
					...scan,
					state: {
						currentAttemptsDocumentScan: 0,
						identityDocuments: {},
					},
				},
				reservations: {
					...reservationsModule,
					state: {
						data: defaultReservation.data,
					},
				},
				modal,
				loading,
				queryParams,
			},
		});

		mockMediaDevices = {
			getUserMedia: jest.fn(async () => {
				return new Promise((resolve, reject) => {
					reject("Unexpected");
				});
			}),
		};
		Object.defineProperty(window.navigator, "mediaDevices", {
			writable: true,
			value: mockMediaDevices,
		});

		wrapper = mount(AdvancedScan, {
			mocks: {
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
		jest.resetAllMocks();
	});

	it("Get user media called on mount Advanced Scan Page", async () => {
		expect(mockMediaDevices.getUserMedia).toBeCalled();
		expect(wrapper.vm.documentSide).toBe("front");
	});

	it("If unexpected camera error should print a generic error and a button to upload a file", async () => {
		const makePhotoButton = wrapper.find('[data-test="makePhotoButton"]');

		expect(wrapper.find('[data-test="userMediaError"]').text()).toEqual(
			advancedScanTranslations.genericUserMediaError,
		);
		expect(wrapper.find('[data-test="uploadPhotoButton"]').exists()).toBe(true);
		expect(makePhotoButton.element.disabled).toBe(true);
	});
});
