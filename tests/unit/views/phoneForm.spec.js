import Vuex from "vuex";
import Vuelidate from "vuelidate";
import VueRouter from "vue-router";
import PhoneForm from "@/views/PhoneForm";
import { mount, createLocalVue } from "@vue/test-utils";
import router from "@/router";
import { i18n } from "@/locales";
import brand from "@/store/brand";
import reservations from "@/store/reservations";
import phone from "@/store/phone";
import guestModule from "@/store/guest";
import { guestData } from "../../../mocks/data/guestData";
import modal from "@/store/modal";
import repository from "@/repository/repositoryFactory";
import loading from "@/store/loading";
import flushPromises from "flush-promises";
const localVue = createLocalVue();
localVue.use(Vuex);
localVue.use(Vuelidate);
localVue.use(VueRouter);
const api = repository.get("checkin");
describe("Phone Form page", () => {
	let store;
	let wrapper;
	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 1,
						mainColor: "123",
						country: "ES",
						config: {
							max_attempts_telephone: 3,
							max_attempts_validate_telephone_code: 3,
							telephone_notifications: true,
							telephone: true,
							show_send_newsletter_checkbox: true,
							show_save_phone_in_database_checkbox: true,
							custom_phone_text: false,
						},
					},
				},
				guest: {
					...guestModule,
					getters: {
						getSelectedGuest: () => {
							return guestData[1];
						},
					},
				},
				phone: {
					...phone,
					state: {
						attempt: 0,
						attemptCode: 0,
						validatedPhones: [],
					},
				},
				reservations: {
					...reservations,
					state: {
						reservationSelected: {
							res_id: 1,
							room_number: "",
							check_in: "2022-01-20",
							check_out: "2022-01-22",
						},
					},
				},
				modal,
				loading,
			},
		});

		api.validatePhone = jest.fn(async () =>
			Promise.resolve({
				data: {},
			}),
		);
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("Should be rendered", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});
		expect(wrapper.find(".phone-form").exists()).toBe(true);
	});

	it("button should be disabled if input has error", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});
		// Starts enabled
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);
		wrapper.find('input[name="phone_number"]').setValue("666666666");
		await localVue.nextTick();
		await localVue.nextTick();
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);
	});

	it("displays checkboxes when config values are true", () => {
		wrapper = mount(PhoneForm, {
			localVue,
			store,
			i18n,
		});

		const sendNewsletterCheckbox = wrapper.find("#commercialSMS");
		const savePhoneCheckbox = wrapper.find("#keep-phone-number");

		expect(sendNewsletterCheckbox.exists()).toBe(true);
		expect(savePhoneCheckbox.exists()).toBe(true);
	});

	it("does not display checkboxes when config values are false", () => {
		store.state.brand.config.show_send_newsletter_checkbox = false;
		store.state.brand.config.show_save_phone_in_database_checkbox = false;

		wrapper = mount(PhoneForm, {
			localVue,
			store,
			i18n,
		});

		const sendNewsletterCheckbox = wrapper.find("#commercialSMS");
		const savePhoneCheckbox = wrapper.find("#keep-phone-number");

		expect(sendNewsletterCheckbox.exists()).toBe(false);
		expect(savePhoneCheckbox.exists()).toBe(false);
	});

	it("Clicking on checkboxes should update data values", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});
		expect(wrapper.vm.keep_phone).toBe(false);
		wrapper.find("#keep-phone-number").trigger("click");
		await localVue.nextTick();

		expect(wrapper.vm.keep_phone).toBe(true);
	});

	it("Filling form and clicking on continue should call to api", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});

		await flushPromises();

		jest.spyOn(router, "push");
		wrapper.getValidatePhoneBodyRequest = jest.fn({});
		wrapper.find('input[name="phone_number"]').setValue(999999999);
		await localVue.nextTick();
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);
		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();

		expect(await api.validatePhone).toBeCalled();
	});

	it("If api fails, an attempt should be added and modal should pop", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});

		await flushPromises();

		api.validatePhone = jest.fn(async () =>
			Promise.reject({
				data: {},
			}),
		);
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		jest.spyOn(wrapper.vm, "showModalError");
		wrapper.getValidatePhoneBodyRequest = jest.fn({});
		wrapper.find('input[name="phone_number"]').setValue("666666666");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);

		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();
		expect(await api.validatePhone).toBeCalled();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await wrapper.vm.showModalError).toBeCalled();
		expect(await wrapper.vm.$store.state.phone.attempt).toBe(1);
		await localVue.nextTick();
		expect(await storeDispatch).toBeCalledWith("modal/SET_TYPE", "error");
		expect(await storeDispatch).toBeCalledWith(
			"modal/SET_TITLE",
			"error.header",
		);
		expect(await storeDispatch).toBeCalledWith("modal/VISIBLE", true);
		expect(await wrapper.vm.modalMessage).toBe(
			"We have been unable to verify the telephone number. Please, try again.",
		);
	});

	it("If attempts > config attempts modal message should change", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});

		await flushPromises();

		api.validatePhone = jest.fn(async () =>
			Promise.reject({
				data: {},
			}),
		);
		jest.spyOn(wrapper.vm, "showModalError");
		wrapper.getValidatePhoneBodyRequest = jest.fn({});
		wrapper.find('input[name="phone_number"]').setValue("666666666");
		await localVue.nextTick();
		expect(wrapper.find('[data-test="continue-button"]').element.disabled).toBe(
			false,
		);

		// Click nº 1
		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();
		expect(await api.validatePhone).toBeCalled();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await wrapper.vm.showModalError).toBeCalled();
		expect(await wrapper.vm.$store.state.phone.attempt).toBe(1);
		await localVue.nextTick();

		// Click nº 2
		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();
		expect(await api.validatePhone).toBeCalled();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await wrapper.vm.showModalError).toBeCalled();
		expect(await wrapper.vm.$store.state.phone.attempt).toBe(2);
		await localVue.nextTick();

		// Click nº 3
		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();
		expect(await api.validatePhone).toBeCalled();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await wrapper.vm.showModalError).toBeCalled();
		expect(await wrapper.vm.$store.state.phone.attempt).toBe(3);
		await localVue.nextTick();

		// Click nº 4
		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();
		expect(await api.validatePhone).toBeCalled();
		await localVue.nextTick();
		await localVue.nextTick();
		expect(await wrapper.vm.showModalError).toBeCalled();
		expect(await wrapper.vm.$store.state.phone.attempt).toBe(4);
		expect(await wrapper.vm.modalMessage).toBe(
			"You have exceeded the maximum number of attempts. We will redirect you to the following screen to continue with the process.",
		);
	});

	it("should set custom phone text when custom_phone_text config value is true", async () => {
		store.state.brand.config.custom_phone_text = true;

		api.getCustomizedText = jest.fn(async () =>
			Promise.resolve({
				data: [
					{},
					{},
					{},
					{
						translations: [
							{
								text: "Custom phone text",
							},
						],
					},
				],
			}),
		);

		const getCustomPhoneTextSpy = jest.spyOn(
			PhoneForm.methods,
			"getCustomPhoneText",
		);

		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});

		expect(getCustomPhoneTextSpy).toHaveBeenCalled();
		await flushPromises();
		expect(wrapper.vm.alertSMS).toBe("Custom phone text");
	});

	it("should redirect to Comments if the phone number is already validated", async () => {
		store.state.phone.validatedPhones = ["+34123456789"];

		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});

		await flushPromises();

		const redirectSpy = jest.spyOn(wrapper.vm, "redirect");

		wrapper.find('input[name="phone_number"]').setValue("123456789");
		wrapper.find('[data-test="continue-button"]').trigger("click");

		expect(await api.validatePhone).not.toBeCalled();
		expect(redirectSpy).toHaveBeenCalledWith({ name: "Comments" });
	});

	it("should redirect to Phone Verification if the phone number has not been validated", async () => {
		store.state.phone.validatedPhones = ["+34123456789"];

		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});

		await flushPromises();

		const redirectSpy = jest.spyOn(wrapper.vm, "redirect");

		wrapper.find('input[name="phone_number"]').setValue("999999999");
		wrapper.find('[data-test="continue-button"]').trigger("click");
		await localVue.nextTick();

		expect(await api.validatePhone).toBeCalled();
		await flushPromises();
		expect(redirectSpy).toHaveBeenCalledWith({ name: "PhoneVerification" });
	});

	it("Should automatically set phone_number value that was inputed on Validate Data", async () => {
		wrapper = mount(PhoneForm, {
			localVue,
			router,
			store,
			i18n,
		});
		await localVue.nextTick();
		expect(wrapper.vm.phoneNumber).toEqual(guestData[1].telephone.value);
		expect(wrapper.find('input[name="phone_number"]').element.value).toBe(
			guestData[1].telephone.value,
		);
	});
});
