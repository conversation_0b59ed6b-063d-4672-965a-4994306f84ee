import Vuex from "vuex";
import { createLocalVue } from "@vue/test-utils";
import reservationsModule from "@/store/reservations";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("Reservations Store", () => {
  let store;
  let reservations;

  beforeEach(async () => {
    reservations = {
      ...reservationsModule,
      state: {
        multipleRooms: true,
        numberGuestSession: 0,
        reservationSelected: {
          guests: [
            { validated: true },
            { validated: true },
            { validated: true },
            { validated: true },
            { validated: true }
          ],
          res_adults: 2,
          res_children: 1,
          res_juniors: 1,
          res_babies: 1
        },
        data: [
          {
            guests: [
              { validated: true },
              { validated: true },
              { validated: true },
              { validated: true },
              { validated: true }
            ],
            res_adults: 2,
            res_children: 1,
            res_juniors: 1,
            res_babies: 1
          },
          {
            guests: [
              { validated: true },
              { validated: true },
              { validated: true },
              { validated: true }
            ],
            res_adults: 1,
            res_children: 1,
            res_juniors: 1,
            res_babies: 1
          }
        ]
      }
    };
    store = new Vuex.Store({
      modules: {
        reservations
      }
    });
  });

  it("Getter getReservationCompleted with multipleRooms and all guests validated", async () => {
    const result = store.getters["reservations/getReservationCompleted"]();
    expect(result).toBe(true);
  });

  it("Getter getReservationCompleted with multipleRooms and some guests not validated", async () => {
    reservations.state.data[0].guests[0].validated = false;

    const result = store.getters["reservations/getReservationCompleted"]();
    expect(result).toBe(false);
  });

  it("Getter getReservationCompleted without multipleRooms and all guests validated", async () => {
    reservations.state.multipleRooms = false;

    const result = store.getters["reservations/getReservationCompleted"]();
    expect(result).toBe(true);
  });

  it("Getter getReservationCompleted without multipleRooms and some guests not validated", async () => {
    reservations.state.multipleRooms = false;
    reservations.state.reservationSelected.guests[0].validated = false;

    const result = store.getters["reservations/getReservationCompleted"]();
    expect(result).toBe(false);
  });

  it("Getter getReservationCompleted with multipleRooms, forceReservationSelected and all guests validated", async () => {
    reservations.state.data[0].guests[0].validated = false;

    const result = store.getters["reservations/getReservationCompleted"](true);
    // Expect true even if we set a guest in data as not validated, because the getter should
    // be using reservationSelected due to the forceReservationSelected parameter being true
    expect(result).toBe(true);
  });

  it("Getter getReservationCompleted with multipleRooms, forceReservationSelected and some guests not validated", async () => {
    reservations.state.reservationSelected.guests[0].validated = false;
    const result = store.getters["reservations/getReservationCompleted"](true);
    expect(result).toBe(false);
  });

  it("Getter getReservationCompleted without multipleRooms, with forceReservationSelected and all guests validated", async () => {
    reservations.state.multipleRooms = false;
    reservations.state.data[0].guests[0].validated = false;

    const result = store.getters["reservations/getReservationCompleted"](true);
    // Expect true even if we set a guest in data as not validated, because the getter should
    // be using reservationSelected due to the forceReservationSelected parameter being true
    expect(result).toBe(true);
  });

  it("Getter getReservationCompleted without multipleRooms, with forceReservationSelected and some guests not validated", async () => {
    reservations.state.multipleRooms = false;
    reservations.state.reservationSelected.guests[0].validated = false;

    const result = store.getters["reservations/getReservationCompleted"](true);
    expect(result).toBe(false);
  });
});
