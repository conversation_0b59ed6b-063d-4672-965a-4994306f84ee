import calculateCharges from "@/services/payment/calculateCharges";
import { charges, noCharges } from "../../../../mocks/modules/integration/data";

describe("getLocalizerAmount method", () => {
  it("If there is no invoices, getLocalizerAmount should return 0", () => {
    const response = calculateCharges.getLocalizerAmount(noCharges.data);
    expect(response).toBe(0);
  });

  it("If there is invoices, getLocalizerAmount should return the sum of all", () => {
    const response = calculateCharges.getLocalizerAmount(charges.data);
    expect(response).toBe(450);
  });

  it("If there is invoices, getLocalizerAmount should return an amout with 2 decimals", () => {
    const chargesWithDecimals = {
      data: {
        localizer: "MOCK_LOCALIZER",
        reservations: [
          {
            invoices: [
              {
                total: 250.12345
              },
              {
                total: 10.555
              }
            ]
          },
          {
            invoices: [
              {
                total: 43.005
              }
            ]
          }
        ]
      }
    };

    const response = calculateCharges.getLocalizerAmount(
      chargesWithDecimals.data
    );
    expect(response).toBe(303.68);
  });
});

describe("getReservationsInvoices method", () => {
  it("If there is reservations, getReservationsInvoices should return a normalized object", () => {
    const response = calculateCharges.getReservationsInvoices(charges.data);
    const sanetizedInvoices = [
      {
        id: "145316-001",
        invoices: [
          {
            amount: 150,
            currency: "EUR",
            guest_id: "HUES-744531",
            id: "PROF-000000",
            name: "Daniel Alzina Daniel",
            products: [
              {
                currency: "EUR",
                description: "ALOJAMIENTO",
                end_date: "2022-04-07T00:00:00+02:00",
                product_code: "0100",
                quantity: 1,
                start_date: "2022-04-07T00:00:00+02:00",
                taxes: 7.27,
                total: 150
              }
            ],
            received: 0,
            remain: 150,
            taxes: 7.27,
            total: 150,
            transactions: []
          }
        ],
        selected: false
      },
      {
        id: "**********",
        invoices: [
          {
            amount: 300,
            currency: "EUR",
            guest_id: "HUES-744531",
            id: "PROF-000001",
            name: "Pepe viyuela",
            products: [
              {
                currency: "EUR",
                description: "ALOJAMIENTO",
                end_date: "2022-04-07T00:00:00+02:00",
                product_code: "0100",
                quantity: 1,
                start_date: "2022-04-07T00:00:00+02:00",
                taxes: 7.27,
                total: 300
              }
            ],
            received: 0,
            remain: 300,
            taxes: 7.27,
            total: 300,
            transactions: []
          }
        ],
        selected: false
      }
    ];
    expect(response).toEqual(sanetizedInvoices);
  });

  it("If there is no reservations, should return []", () => {
    const response = calculateCharges.getReservationsInvoices({
      localizer: "MOCK_LOCALIZER",
      reservations: []
    });
    expect(response).toEqual([]);
  });

  it("If there is no invoices, should return an object with empty invoices", () => {
    const response = calculateCharges.getReservationsInvoices({
      localizer: "MOCK_LOCALIZER",
      reservations: [
        {
          res_id: "**********",
          invoices: []
        }
      ]
    });
    expect(response).toEqual([
      {
        id: "**********",
        selected: false,
        invoices: []
      }
    ]);
  });
});

describe("getInvoicesCurrency method", () => {
  it("If first reservation has currency, it should be returned", () => {
    const response = calculateCharges.getInvoicesCurrency(charges.data);
    expect(response).toBe("EUR");
  });
  it("If theres no reservations, false should be returned", () => {
    const response = calculateCharges.getInvoicesCurrency(noCharges.data);
    expect(response).toBe(false);
  });

  it("If first reservation has currency but other reservations have differente currency, false should be returned", () => {
    let response = calculateCharges.getInvoicesCurrency({
      reservations: [
        { res_id: "145316-001", invoices: [{ currency: "EUR" }] },
        { res_id: "145316-001", invoices: [{ currency: "USD" }] }
      ]
    });
    expect(response).toBe(false);

    response = calculateCharges.getInvoicesCurrency({
      reservations: [
        {
          res_id: "145316-001",
          invoices: [
            { currency: "EUR" },
            { currency: "EUR" },
            { currency: "USD" }
          ]
        }
      ]
    });
    expect(response).toBe(false);

    response = calculateCharges.getInvoicesCurrency({
      reservations: [
        {
          res_id: "145316-001",
          invoices: [
            { currency: "EUR" },
            { currency: "USD" },
            { currency: "EUR" }
          ]
        }
      ]
    });
    expect(response).toBe(false);
  });

  it("If first reservation has currency and other reservations have the same currency, first currency should be returned", () => {
    let response = calculateCharges.getInvoicesCurrency({
      reservations: [
        { res_id: "145316-001", invoices: [{ currency: "EUR" }] },
        { res_id: "145316-001", invoices: [{ currency: "EUR" }] }
      ]
    });
    expect(response).toBe("EUR");

    response = calculateCharges.getInvoicesCurrency({
      reservations: [
        {
          res_id: "145316-001",
          invoices: [
            { currency: "EUR" },
            { currency: "EUR" },
            { currency: "EUR" }
          ]
        }
      ]
    });
    expect(response).toBe("EUR");
  });
});
