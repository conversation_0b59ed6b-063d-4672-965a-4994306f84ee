import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import dateInput from "@/components/search/inputs/dateInput.component.vue";
import Vuelidate from "vuelidate";
import { i18n } from "@/locales";
import brand from "@/store/brand";
import { AMERICAN_FORMAT, DEFAULT_FORMAT } from "@/mixins/dateFormat.js";

const localVue = createLocalVue();
localVue.use(Vuex);
localVue.use(Vuelidate);

describe("allow timeLimit config", () => {
	let store;
	let wrapper;
	const defaultCurrentDate = new Date("06-08-2023T00:00:00.00Z");
	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: {
					...brand,
					state: {
						brandId: 76,
						mainColor: "123",
						country: "ES",
						config: {},
					},
				},
			},
		});

		jest.spyOn(global, "Date").mockImplementation(() => defaultCurrentDate);
	});

	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
	});
	it("should render and find date-picker form", async () => {
		wrapper = mount(dateInput, {
			localVue,
			i18n,
			store,
			attachTo: document.body,
			propsData: {
				name: "test",
				inputName: "check_in",
				type: "check_in",
				allowTimeLimit: true,
				checkOnStart: false,
				closeTimeLimitCheckin: -3,
				timeLimitCheckin: 2,
				disableFutureDates: false,
				value: "2023-06-28",
			},
			data() {
				return {
					inputValue: "",
					hasError: false,
					errorType: "",
					lang: "en",
				};
			},
			mocks: {
				$root: {
					$i18n: {
						locale: "en",
					},
				},
			},
		});
	});

	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
	});

	it("should render and find date-picker form", async () => {
		const datePicker = wrapper.findComponent({ name: "date-picker" });
		const errorMessage = wrapper.find(".error-message");

		expect(wrapper.exists()).toBe(true);
		expect(errorMessage.exists()).toBe(false);
		expect(datePicker.exists()).toBeTruthy();
		expect(datePicker.isVisible()).toBe(true);
		expect(wrapper.vm.countryFormat()).toBe(DEFAULT_FORMAT);
	});

	it("adds hyphens to inputValue when inputChanged receives unformatted date", async () => {
    await wrapper.setData({ inputValue: "25022023" });
		wrapper.vm.inputChanged("25022023");
		await wrapper.vm.$nextTick();
		expect(wrapper.vm.inputValue).toBe("25-02-2023");
	});

	it("should show errorMessage only if data hasError is true", async () => {
		wrapper = mount(dateInput, {
			localVue,
			i18n,
			store,
			attachTo: document.body,
			propsData: {
				name: "Fecha de check-in",
				inputName: "check_in",
				type: "check_in",
				allowTimeLimit: true,
				checkOnStart: false,
				closeTimeLimitCheckin: -3,
				timeLimitCheckin: 2,
				disableFutureDates: false,
				value: "",
			},
			data() {
				return {
					inputValue: "",
					hasError: true,
					errorType: "",
					lang: "en",
				};
			},
			mocks: {
				$root: {
					$i18n: {
						locale: "en",
					},
				},
			},
		});
		expect(wrapper.exists()).toBe(true);
		let errorMessage;
		errorMessage = wrapper.find(".error-message");
		await expect(errorMessage.exists()).toBeTruthy();
		await wrapper.setData({ hasError: false });

		errorMessage = await wrapper.find(".error-message");
		await expect(errorMessage.exists()).toBe(false);
	});
});