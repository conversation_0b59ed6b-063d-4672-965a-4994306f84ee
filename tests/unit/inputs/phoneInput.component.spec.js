import { mount, createLocalVue } from "@vue/test-utils";
import Vuex from "vuex";
import phoneInput from "@/components/search/inputs/phoneInput.component.vue";
import Vuelidate from "vuelidate";
import { i18n } from "@/locales";
const localVue = createLocalVue();
localVue.use(Vuelidate);
localVue.use(Vuex);

describe("Phone input", () => {
	it("should render", () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					namespaced: true,
					state: {
						country: "",
					},
				},
			},
		});
		const wrapper = mount(phoneInput, {
			localVue,
			store,
			propsData: {
				name: "test",
				inputName: "test",
				type: "phone",
				value: "",
				countryCode: "",
				optional: false,
			},
		});
		expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
	});

	it("If input has countryCode and value is not a valid phone number, border should be red", async () => {
		const wrapper = mount(phoneInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "phone",
				countryCode: "ES",
				value: "",
				optional: false,
			},
		});
		const input = wrapper.find('input[name="test"]');

		expect(input.exists()).toBe(true);

		input.setValue("1");
		await wrapper.vm.$nextTick();

		expect(wrapper.vm.isPhoneValidationAvailable).toBe(true);

		expect(wrapper.find(".border-red-400").exists()).toBe(true);
	});

	it("If input does not have an error, border should be green and no error message should be shown", async () => {
		const wrapper = mount(phoneInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "phone",
				value: "",
				countryCode: "FR",
				optional: false,
			},
		});

		const input = wrapper.find('input[name="test"]');
		expect(input.exists()).toBe(true);
		await input.setValue("+34666666666");
		await wrapper.vm.$nextTick();
		await wrapper.vm.$nextTick(); // Twice because there's 2 changes (dial selector and input)
		expect(wrapper.vm.isPhoneValidationAvailable).toEqual(true);
		expect(wrapper.find(".border-green-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").exists()).toBe(false);
	});

	it("If input is optional, and value is cleared, gray border should be shown and no error message", async () => {
		const wrapper = mount(phoneInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "phone",
				value: "123456789",
				countryCode: "ES",
				optional: true,
			},
		});

		const input = wrapper.find('input[name="test"]');

		expect(input.exists()).toBe(true);

		input.setValue("");
		await wrapper.vm.$nextTick();
		expect(wrapper.vm.isPhoneValidationAvailable).toEqual(false);
		expect(wrapper.find(".border-gray-200").exists()).toBe(true);
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".border-red-400").exists()).toBe(false);
		expect(wrapper.find(".error-message").exists()).toBe(false);
	});
});
