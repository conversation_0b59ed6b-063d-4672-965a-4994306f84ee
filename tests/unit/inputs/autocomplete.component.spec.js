import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import autocomplete from "@/components/search/inputs/autocompleteInput.component.vue";
import Vuelidate from "vuelidate";
import { i18n } from "@/locales";
const localVue = createLocalVue();
localVue.use(Vuelidate);
localVue.use(Vuex);

describe("Autocomplete input", () => {
  it("should render", () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "",
        optional: false,
      },
    });
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
  });

  it("If value is placed on input, list should be shown", async () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "",
        options: [
          {
            name: "testing 1",
            value: "testing 1",
          },
          {
            name: "testing 2",
            value: "testing 2",
          },
        ],
      },
    });

    wrapper.find('[data-test="test"]').setValue("testing");
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);

    expect(wrapper.find('[data-test="test-list"]').isVisible()).toBe(true);
    expect(wrapper.findAll('[data-test="test-option"]')).toHaveLength(2);
  });

  it("If value is selected, list should be hidden, and value inputValue should change", async () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "",
        options: [
          {
            name: "testing 1",
            value: "testing 1",
          },
          {
            name: "testing 2",
            value: "testing 2",
          },
        ],
      },
    });

    wrapper.find('[data-test="test"]').setValue("testing");
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);

    // Check component before change
    expect(wrapper.find('[data-test="test-list"]').isVisible()).toBe(true);
    expect(wrapper.findAll('[data-test="test-option"]')).toHaveLength(2);
    expect(wrapper.vm.isOpen).toBe(true);
    expect(wrapper.vm.inputValue).toEqual("testing");
    expect(wrapper.vm.results).toEqual([
      {
        name: "testing 1",
        value: "testing 1",
      },
      {
        name: "testing 2",
        value: "testing 2",
      },
    ]);
    // Click on first option
    wrapper
      .findAll('[data-test="test-option"]')
      .at(0)
      .trigger("click");
    await wrapper.vm.$nextTick();
    // Check component after change
    expect(wrapper.vm.isOpen).toBe(false);
    expect(wrapper.vm.inputValue).toEqual("testing 1");
    expect(wrapper.vm.selectedResult).toEqual({
      name: "testing 1",
      value: "testing 1",
    });
    expect(wrapper.find('[data-test="test-list"]').isVisible()).toBe(false);
    expect(wrapper.find(".border-green-400").exists()).toBe(true);
  });

  it("If value is not an option, value should be empty", () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "RUS",
        optional: false,
        options: [
          {
            name: "España",
            value: "ESP",
          },
          {
            name: "Francia",
            value: "FRA",
          },
          {
            name: "Alemania",
            value: "DEU",
          },
        ],
      },
    });
    expect(wrapper.vm.inputValue).toEqual("");
  });

  it("If value is an option, input value should be that option", () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "ESP",
        optional: false,
        options: [
          {
            name: "España",
            value: "ESP",
          },
          {
            name: "Francia",
            value: "FRA",
          },
          {
            name: "Alemania",
            value: "DEU",
          },
        ],
      },
    });
    expect(wrapper.vm.inputValue).toEqual("España");
  });

  it("If value is not an option but input allows free text, input value equals to given value", () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "Etiopia",
        optional: false,
        allowsFreeText: true,
        options: [
          {
            name: "España",
            value: "ESP",
          },
          {
            name: "Francia",
            value: "FRA",
          },
          {
            name: "Alemania",
            value: "DEU",
          },
        ],
      },
    });
    expect(wrapper.vm.inputValue).toEqual("Etiopia");
  });

  it("If input is cleared, list should be empty", async () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "testing 1",
        options: [
          {
            name: "testing 1",
            value: "testing 1",
          },
          {
            name: "testing 2",
            value: "testing 2",
          },
        ],
      },
    });

    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
    wrapper.find('[data-test="test"]').setValue("");
    await wrapper.vm.$nextTick();

    // Check component before change
    expect(wrapper.find('[data-test="test-list"]').isVisible()).toBe(false);
    expect(wrapper.findAll('[data-test="test-option"]')).toHaveLength(0);
    expect(wrapper.vm.isOpen).toBe(false);
    expect(wrapper.vm.inputValue).toEqual("");
    expect(wrapper.vm.results).toEqual([]);

    expect(wrapper.find(".border-red-400").exists()).toBe(true);
  });

  it("If input is cleared but it is optional, list should be empty and border should be gray", async () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "testing 1",
        optional: true,
        options: [
          {
            name: "testing 1",
            value: "testing 1",
          },
          {
            name: "testing 2",
            value: "testing 2",
          },
        ],
      },
    });

    wrapper.find('[data-test="test"]').setValue("");
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);

    // Check component before change
    expect(wrapper.find('[data-test="test-list"]').isVisible()).toBe(false);
    expect(wrapper.findAll('[data-test="test-option"]')).toHaveLength(0);
    expect(wrapper.vm.isOpen).toBe(false);
    expect(wrapper.vm.inputValue).toEqual("");
    expect(wrapper.vm.results).toEqual([]);

    expect(wrapper.find(".border-gray-200").exists()).toBe(true);
    expect(wrapper.find(".border-red-400").exists()).toBe(false);
    expect(wrapper.find(".border-green-400").exists()).toBe(false);
  });

  it("Triggering handleClickOutside should close menu", async () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "",
        options: [
          {
            name: "testing 1",
            value: "testing 1",
          },
          {
            name: "testing 2",
            value: "testing 2",
          },
        ],
      },
    });
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
    wrapper.find('[data-test="test"]').setValue("testing");
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isOpen).toBe(true);
    wrapper.vm.handleClickOutside({ targer: "" });

    expect(wrapper.vm.isOpen).toBe(false);
    expect(wrapper.vm.inputValue).toBe("");
  });

  it("If information modal button is clicked, a modal should pop up", async () => {
    const store = new Vuex.Store({
      modules: {
        brand: {
          state: {
            mainColor: "123",
          },
        },
      },
    });

    const wrapper = mount(autocomplete, {
      store,
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "defaultInput",
        value: "",
        state: "test",
        maxLength: "10",
        optional: false,
        infoModal: {
          title: "Obtener información de la reserva.",
          message:
            "Podrá encontrar la información necesaria para realizar el check-in en los datos suministrados por el establecimiento, agencia o Web de reservas en el momento de hacer la reserva. El código o                      localizador de reserva es generalmente un código alfanumérico proporcionado en el momento de haber hecho la reserva.",
          button: "Volver",
        },
      },
    });

    const button = wrapper.find('[data-test="info-button-help"]');
    expect(button.exists()).toBe(true);
    await button.trigger("click");
    expect(wrapper.emitted("showModalInfo")).toBeTruthy();
  });

  it("Triggering handleClickInside should open menu", async () => {
    const wrapper = mount(autocomplete, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "autocompleteInput",
        value: "",
        options: [
          {
            name: "testing 1",
            value: "testing 1",
          },
          {
            name: "testing 2",
            value: "testing 2",
          },
        ],
      },
    });
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
    expect(wrapper.vm.isOpen).toBe(false);
    wrapper.find('[data-test="test"]').trigger("click");
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isOpen).toBe(true);
  });
});
