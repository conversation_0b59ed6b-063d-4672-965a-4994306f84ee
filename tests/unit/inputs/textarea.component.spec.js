import { mount, flushPromises } from "@vue/test-utils";
import textarea from "@/components/comments/textarea.component.vue";

describe("Textarea Input", () => {
  it("Check correctly render component", () => {
    const wrapper = mount(textarea, {
      propsData: {
        name: "comment",
        text: "",
        type: "coment",
        placeholder: ""
      }
    });

    expect(wrapper.find("textarea").exists()).toBe(true);
    expect(wrapper.find("#comments").exists()).toBe(true);
  });

  it("Check if trigger emit event", () => {
    const wrapper = mount(textarea, {
      propsData: {
        name: "comment",
        text: "",
        type: "coment",
        placeholder: ""
      }
    });

    wrapper.find("textarea").trigger("input");
    expect(wrapper.emitted()).toHaveProperty("inputChanged");
    expect(wrapper.emitted().inputChanged.length).toBe(1);
  });

  it("If maxChar prop does not exists, span with maxLength should not be rendered", async () => {
    const wrapper = mount(textarea, {
      propsData: {
        name: "comment",
        text: "",
        type: "coment",
        placeholder: ""
      }
    });

    expect(wrapper.find('[data-test="maxLengthProgress"]').exists()).toBe(
      false
    );
  });

  it("If maxChar prop exists, span with maxLength should be rendered", async () => {
    const wrapper = mount(textarea, {
      propsData: {
        name: "comment",
        text: "",
        type: "coment",
        placeholder: "",
        maxChars: 2
      }
    });

    expect(wrapper.find('[data-test="maxLengthProgress"]').exists()).toBe(true);
  });

  it("If max char is reached, event should not be emmited", async () => {
    const wrapper = mount(textarea, {
      propsData: {
        name: "comment",
        text: "",
        type: "coment",
        placeholder: "",
        maxLength: 2
      }
    });
    const input = wrapper.find("textarea");

    input.element.value = "He";
    input.trigger("input");
    expect(wrapper.emitted().inputChanged.length).toBe(1);

    input.element.value = "Hello";
    // Not emmited again, because we reached the maxLength previously
    expect(wrapper.emitted().inputChanged.length).toBe(1);
    expect(wrapper.vm.length).toEqual(2);
    expect(wrapper.vm.inputValue).toBe("He");
  });

  it("Input maxLength prints correctly", async () => {
    const wrapper = mount(textarea, {
      propsData: {
        name: "comment",
        text: "",
        type: "coment",
        placeholder: "",
        maxChars: 200
      }
    });
    wrapper.find("textarea").element.value = "TESTING";
    wrapper.find("textarea").trigger("input");
    expect(wrapper.vm.length).toEqual(7);
    await flushPromises; // In order to re-render the component
    expect(wrapper.find('[data-test="maxLengthProgress"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="maxLengthProgress"]').text()).toEqual(
      "7/200"
    );
  });
});
