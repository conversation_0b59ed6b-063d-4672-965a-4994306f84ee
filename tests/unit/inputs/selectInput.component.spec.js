import Vuex from "vuex";
import Vuelidate from "vuelidate";
import { mount, createLocalVue } from "@vue/test-utils";
import selectInput from "@/components/search/inputs/selectInput.component.vue";
import { i18n } from "@/locales";
import brand from "@/store/brand";

const localVue = createLocalVue();
localVue.use(Vuex);
localVue.use(Vuelidate);

describe('select input component', () => {
  let store;

  beforeEach(() => {
		store = new Vuex.Store({
			modules: {
        brand: {
          ...brand,
					state: {
						country: "ES",
					},
        }
			},
		});
	});

	afterEach(() => {
		jest.resetAllMocks();
		jest.clearAllMocks();
	});

  it("displays overridden name when overrides exist", async() => {
    store.state.brand.country = "MX"

    const wrapper = mount(selectInput, { store,
      localVue,
      i18n,
      propsData: {
        options: [
          { value: "identity_card", name: "<PERSON><PERSON>" },
          { value: "passport", name: "<PERSON><PERSON><PERSON><PERSON>" },
        ],
        value: "identity_card",
        inputName: "document_type"
      },
     
    });

    wrapper.vm.$root.$i18n.locale = "es";
    await wrapper.vm.$nextTick();

    const identityCardOption = wrapper.find('option[value="identity_card"]');
    expect(identityCardOption.text()).toBe("INE");
  });

  it("doesn't display overridden name when override doesn't exist", async() => {

    const wrapper = mount(selectInput, { store,
      localVue,
      i18n,
      propsData: {
        options: [
          { value: "identity_card", name: "DNI" },
          { value: "passport", name: "Pasaporte" },
        ],
        value: "identity_card",
        inputName: "document_type"
      },
     
    });

    wrapper.vm.$root.$i18n.locale = "es";
    await wrapper.vm.$nextTick();

    const identityCardOption = wrapper.find('option[value="identity_card"]');
    expect(identityCardOption.text()).toBe("DNI");
    expect(identityCardOption.text()).not.toBe("INE");

  });
})
