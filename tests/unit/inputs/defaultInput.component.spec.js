import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import defaultInput from "@/components/search/inputs/defaultInput.component.vue";
import Vuelidate from "vuelidate";
import { i18n } from "@/locales";
const localVue = createLocalVue();
localVue.use(Vuelidate);
localVue.use(Vuex);

describe("Default input", () => {
	it("should render", () => {
		const wrapper = mount(defaultInput, {
			localVue,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});
		expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
	});

	it("If input has an error, border should be red and error message should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "testing",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-red-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").text()).toBe(
			"The value cannot be empty",
		);
	});

	it("If input does not have an error, border should be green and no error message should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("testing!!!");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-green-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").exists()).toBe(false);
	});

	it("If input is optional, and value is cleared, gray border should be shown and no error message", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "test",
				optional: true,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-gray-200").exists()).toBe(true);
		expect(wrapper.find(".border-red-400").exists()).toBe(false);
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".error-message").exists()).toBe(false);
	});

	it("If input max length is exceeded, error should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				maxLength: "10",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("1234567898745");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".border-red-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").text()).toBe(
			"Value must contain a maximum of 10 characters",
		);
	});

	it("If information modal button is clicked, a modal should pop up", async () => {
		const store = new Vuex.Store({
			modules: {
				brand: {
					state: {
						mainColor: "123",
					},
				},
			},
		});

		const wrapper = mount(defaultInput, {
			store,
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				state: "test",
				maxLength: "10",
				optional: false,
				infoModal: {
					title: "Obtener información de la reserva.",
					message:
						"Podrá encontrar la información necesaria para realizar el check-in en los datos suministrados por el establecimiento, agencia o Web de reservas en el momento de hacer la reserva. El código o                      localizador de reserva es generalmente un código alfanumérico proporcionado en el momento de haber hecho la reserva.",
					button: "Volver",
				},
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const button = wrapper.find('[data-test="info-button-help"]');
		expect(button.exists()).toBe(true);
		await button.trigger("click");
		expect(wrapper.emitted("showModalInfo")).toBeTruthy();
	});

	it("If input min length is not reached, error should be shown", async () => {
		const wrapper = mount(defaultInput, {
			localVue,
			i18n,
			propsData: {
				name: "test",
				inputName: "test",
				type: "defaultInput",
				value: "",
				minLength: "8",
				optional: false,
				autocompleteData: {
					name: "test",
					autocomplete: "test",
				},
			},
		});

		const input = wrapper.find('[data-test="test"]');
		expect(input.exists()).toBe(true);

		input.setValue("123456");
		await wrapper.vm.$nextTick();
		expect(wrapper.find(".border-green-400").exists()).toBe(false);
		expect(wrapper.find(".border-red-400").exists()).toBe(true);
		expect(wrapper.find(".error-message").text()).toBe(
			"Value must contain a minimum of 8 characters",
		);
	});
});
