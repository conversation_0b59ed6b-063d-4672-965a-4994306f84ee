import Vuex from "vuex";
import { mount, createLocalVue } from "@vue/test-utils";
import emailInput from "@/components/search/inputs/emailInput.component.vue";
import Vuelidate from "vuelidate";
import { i18n } from "@/locales";
const localVue = createLocalVue();
localVue.use(Vuelidate);
localVue.use(Vuex);

describe("Email input", () => {
  it("should render", () => {
    const wrapper = mount(emailInput, {
      localVue,
      propsData: {
        name: "test",
        inputName: "test",
        type: "email",
        value: "",
        optional: false,
      },
    });
    expect(wrapper.find('[data-test="test"]').exists()).toBe(true);
  });

  it("If input has an error, border should be red and error message should be shown", async () => {
    const wrapper = mount(emailInput, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "email",
        value: "testing",
        optional: false,
      },
    });

    const input = wrapper.find('[data-test="test"]');

    expect(input.exists()).toBe(true);

    input.setValue("not an email");
    await wrapper.vm.$nextTick();
    expect(wrapper.find(".border-red-400").exists()).toBe(true);
    expect(wrapper.find(".error-message").text()).toBe("Invalid email");
  });

  it("If input does not have an error, border should be green and no error message should be shown", async () => {
    const wrapper = mount(emailInput, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "email",
        value: "",
        optional: false,
      },
    });

    const input = wrapper.find('[data-test="test"]');

    expect(input.exists()).toBe(true);

    input.setValue("<EMAIL>");
    await wrapper.vm.$nextTick();
    expect(wrapper.find(".border-green-400").exists()).toBe(true);
    expect(wrapper.find(".error-message").exists()).toBe(false);
  });

  it("If information modal button is clicked, a modal should pop up", async () => {
    const store = new Vuex.Store({
      modules: {
        brand: {
          state: {
            mainColor: "123",
          },
        },
      },
    });

    const wrapper = mount(emailInput, {
      store,
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "defaultInput",
        value: "",
        state: "test",
        maxLength: "10",
        optional: false,
        infoModal: {
          title: "Obtener información de la reserva.",
          message:
            "Podrá encontrar la información necesaria para realizar el check-in en los datos suministrados por el establecimiento, agencia o Web de reservas en el momento de hacer la reserva. El código o                      localizador de reserva es generalmente un código alfanumérico proporcionado en el momento de haber hecho la reserva.",
          button: "Volver",
        },
      },
    });

    const button = wrapper.find('[data-test="info-button-help"]');
    expect(button.exists()).toBe(true);
    await button.trigger("click");
    expect(wrapper.emitted("showModalInfo")).toBeTruthy();
  });

  it("If input is optional, and value is cleared, gray border should be shown and no error message", async () => {
    const wrapper = mount(emailInput, {
      localVue,
      i18n,
      propsData: {
        name: "test",
        inputName: "test",
        type: "email",
        value: "test",
        optional: true,
      },
    });

    const input = wrapper.find('[data-test="test"]');

    expect(input.exists()).toBe(true);

    input.setValue("");
    await wrapper.vm.$nextTick();
    expect(wrapper.find(".border-gray-200").exists()).toBe(true);
    expect(wrapper.find(".border-green-400").exists()).toBe(false);
    expect(wrapper.find(".border-red-400").exists()).toBe(false);
    expect(wrapper.find(".error-message").exists()).toBe(false);
  });
});
