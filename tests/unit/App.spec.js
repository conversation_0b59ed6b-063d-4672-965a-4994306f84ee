import Vuex from "vuex";
import { shallowMount, createLocalVue } from "@vue/test-utils";
import App from "@/App.vue";
import { TrackerProvider, Tracker } from "@/tracker";
import { Analytics } from "aws-amplify";
import brand from "@/store/brand";
import app from "@/store/app";
import guest from "@/store/guest";
import reservations from "@/store/reservations";
import trace from "@/store/trace";
import router from "@/router/index.js";
import { i18n } from "@/locales";
import flushPromises from "flush-promises";

const localVue = createLocalVue();
localVue.use(Vuex);

// Mock the tracker and analytics
jest.mock("@/tracker");
jest.mock("aws-amplify", () => ({
  Analytics: {
    addPluggable: jest.fn(),
    autoTrack: jest.fn(),
  },
}));

// Mock the repository
jest.mock("@/repository/repositoryFactory", () => ({
  get: jest.fn(() => ({
    getReservation: jest.fn().mockResolvedValue([]),
  })),
}));

// Mock mixins
jest.mock("@/mixins/checkinAvailability", () => ({
  methods: {
    isCheckinAvailable: jest.fn().mockReturnValue({ available: true }),
  },
}));
jest.mock("@/mixins/clearStoreAndRedirect", () => ({
  methods: {
    clearStoreAndRedirect: jest.fn(),
  },
}));

describe("App.vue - Tracker Initialization", () => {
  let store;
  let wrapper;
  let mockTrackerInstance;
  let consoleInfoSpy;
  let consoleWarnSpy;
  let consoleErrorSpy;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create mock tracker instance
    mockTrackerInstance = {
      init: jest.fn().mockResolvedValue(),
      setBrandId: jest.fn(),
      setGuestId: jest.fn(),
      setResId: jest.fn(),
    };

    TrackerProvider.mockImplementation(() => mockTrackerInstance);

    // Mock console methods
    consoleInfoSpy = jest.spyOn(console, "info").mockImplementation(() => {});
    consoleWarnSpy = jest.spyOn(console, "warn").mockImplementation(() => {});
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});

    // Mock environment variable
    process.env.VUE_APP_ANALYTICS_STREAM_NAME = "test-stream";
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
    consoleInfoSpy.mockRestore();
    consoleWarnSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    jest.clearAllTimers();
  });

  const createStore = (overrides = {}) => {
    return new Vuex.Store({
      modules: {
        brand: {
          ...brand,
          state: {
            brandId: null,
            config: {},
            timeZone: "Europe/Madrid",
            ...overrides.brand?.state,
          },
        },
        app: {
          ...app,
          state: {
            isReceptionMode: false,
            isDemoMode: false,
            ...overrides.app?.state,
          },
        },
        guest: {
          ...guest,
          state: {
            list: [],
            ...overrides.guest?.state,
          },
        },
        reservations: {
          ...reservations,
          state: {
            reservationSelected: null,
            dataRequested: null,
            data: null,
            ...overrides.reservations?.state,
          },
        },
        trace: {
          ...trace,
          state: {
            parentBrand: null,
            ...overrides.trace?.state,
          },
        },
      },
    });
  };

  const createWrapper = (storeOverrides = {}, options = {}) => {
    store = createStore(storeOverrides);
    return shallowMount(App, {
      store,
      localVue,
      i18n,
      router,
      methods: {
        checkReservationIsStillEligible: jest.fn(),
      },
      ...options,
    });
  };

  describe("Normal behavior - Tracker initialization on mount", () => {
    it("should initialize tracker when brand_id is available on mount", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();

      expect(TrackerProvider).toHaveBeenCalledWith({
        brand_id: "123",
        guest_id: null,
        res_id: null,
        streamName: "test-stream",
        source: "autocheckin",
      });
      expect(mockTrackerInstance.init).toHaveBeenCalled();
      expect(Analytics.addPluggable).toHaveBeenCalledWith(mockTrackerInstance);
      expect(Analytics.autoTrack).toHaveBeenCalledWith("pageView", expect.any(Object));
      expect(consoleInfoSpy).toHaveBeenCalledWith("Init tracker on mounted");
      expect(consoleInfoSpy).toHaveBeenCalledWith("Initializing tracker with brand_id:", "123");
    });

    it("should not initialize tracker when brand_id is not available on mount", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: null } },
      });

      await flushPromises();

      expect(TrackerProvider).not.toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        "No brand id found in the store. Tracker will be initialized when brand_id becomes available"
      );
    });

    it("should not initialize tracker in demo mode", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
        app: { state: { isDemoMode: true } },
      });

      await flushPromises();

      expect(TrackerProvider).not.toHaveBeenCalled();
    });

    it("should initialize tracker with reception mode source", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
        app: { state: { isReceptionMode: true } },
      });

      await flushPromises();

      expect(TrackerProvider).toHaveBeenCalledWith(
        expect.objectContaining({
          source: "reception",
        })
      );
    });
  });

  describe("Brand ID set later - Watcher behavior", () => {
    it("should initialize tracker when brand_id becomes available", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: null } },
      });

      await flushPromises();
      expect(TrackerProvider).not.toHaveBeenCalled();

      // Simulate brand_id being set
      await wrapper.vm.$store.commit("brand/setBrandID", "456");
      await flushPromises();

      expect(TrackerProvider).toHaveBeenCalledWith({
        brand_id: "456",
        guest_id: null,
        res_id: null,
        streamName: "test-stream",
        source: "autocheckin",
      });
      expect(mockTrackerInstance.init).toHaveBeenCalled();
      expect(consoleInfoSpy).toHaveBeenCalledWith("Brand ID became available, initializing tracker");
    });

    it("should not initialize tracker if demo mode is enabled when brand_id becomes available", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: null } },
        app: { state: { isDemoMode: true } },
      });

      await flushPromises();

      // Simulate brand_id being set
      await wrapper.vm.$store.commit("brand/setBrandID", "456");
      await flushPromises();

      expect(TrackerProvider).not.toHaveBeenCalled();
    });
  });

  describe("Brand ID changes - Brand selector scenario", () => {
    it("should update existing tracker when brand_id changes", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();
      expect(TrackerProvider).toHaveBeenCalledTimes(1);

      // Simulate brand_id change (brand selector)
      await wrapper.vm.$store.commit("brand/setBrandID", "789");
      await flushPromises();

      expect(mockTrackerInstance.setBrandId).toHaveBeenCalledWith("789");
      // Should not create a new tracker instance
      expect(TrackerProvider).toHaveBeenCalledTimes(1);
    });

    it("should not do anything when brand_id changes to the same value", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();
      jest.clearAllMocks();

      // Simulate brand_id change to the same value
      await wrapper.vm.$store.commit("brand/setBrandID", "123");
      await flushPromises();

      expect(mockTrackerInstance.setBrandId).not.toHaveBeenCalled();
      expect(TrackerProvider).toHaveBeenCalledTimes(0);
    });
  });

  describe("Guest and reservation data tracking", () => {
    it("should update tracker when guest data changes", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();
      jest.clearAllMocks();

      // Simulate guest change by directly calling the watcher
      const newGuestData = {
        uuid: "guest-456",
        pms_id: "pms-789",
      };
      const oldGuestData = {
        uuid: "guest-123",
        pms_id: "pms-456",
      };

      // Manually trigger the watcher
      wrapper.vm.$options.watch.selectedGuestData.call(wrapper.vm, newGuestData, oldGuestData);

      expect(mockTrackerInstance.setGuestId).toHaveBeenCalledWith("pms-789");
    });

    it("should update tracker when reservation changes", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();
      jest.clearAllMocks();

      // Simulate reservation change
      const newReservation = {
        res_id: "res-456",
      };
      const oldReservation = {
        res_id: "res-123",
      };

      // Manually trigger the watcher
      wrapper.vm.$options.watch.reservationSelected.call(wrapper.vm, newReservation, oldReservation);

      expect(mockTrackerInstance.setResId).toHaveBeenCalledWith("res-456");
    });
  });

  describe("Robust edge cases and error handling", () => {
    it("should prevent multiple tracker initializations", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();
      expect(TrackerProvider).toHaveBeenCalledTimes(1);
      expect(wrapper.vm.tracker).toBeTruthy();

      // Try to initialize again
      await wrapper.vm.initializeTracker();
      await flushPromises();

      // Should still only be called once
      expect(TrackerProvider).toHaveBeenCalledTimes(1);
    });

    it("should handle tracker initialization errors gracefully", async () => {
      const mockError = new Error("Tracker initialization failed");
      TrackerProvider.mockImplementation(() => {
        throw mockError;
      });

      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();

      expect(consoleErrorSpy).toHaveBeenCalledWith("Failed to initialize tracker:", mockError);
      expect(wrapper.vm.tracker).toBeNull();
    });

    it("should handle Analytics.addPluggable errors gracefully", async () => {
      const mockError = new Error("Analytics addPluggable failed");
      Analytics.addPluggable.mockRejectedValue(mockError);

      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();

      expect(consoleErrorSpy).toHaveBeenCalledWith("Failed to initialize tracker:", mockError);
    });

    it("should handle tracker.init errors gracefully", async () => {
      const mockError = new Error("Tracker init failed");
      mockTrackerInstance.init.mockRejectedValue(mockError);

      wrapper = createWrapper({
        brand: { state: { brandId: "123" } },
      });

      await flushPromises();

      expect(consoleErrorSpy).toHaveBeenCalledWith("Failed to initialize tracker:", mockError);
    });

    it("should not update tracker methods if tracker is not initialized", async () => {
      wrapper = createWrapper({
        brand: { state: { brandId: null } },
      });

      await flushPromises();

      // Simulate guest change without tracker
      const newGuestData = { pms_id: "pms-789" };
      const oldGuestData = { pms_id: "pms-456" };

      wrapper.vm.$options.watch.selectedGuestData.call(wrapper.vm, newGuestData, oldGuestData);

      expect(mockTrackerInstance.setGuestId).not.toHaveBeenCalled();
    });
  });
});
