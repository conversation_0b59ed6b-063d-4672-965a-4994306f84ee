import { mount, createLocalVue } from "@vue/test-utils";
import DemoHeader from "@/components/shared/demoHeader.component.vue";
import Vuex from "vuex";
import { i18n } from "@/locales";
import brandStore from "@/store/brand";
import appStore from "@/store/app";
import traceStore from "@/store/trace";
import clearAllStore from "@/utils/clearAllStore";

const localVue = createLocalVue();
localVue.use(Vuex);

jest.mock("@/utils/clearAllStore", () => jest.fn());
describe("DemoHeader", () => {
	let store;
	let wrapper;
	beforeEach(() => {
		store = new Vuex.Store({
			modules: {
				brand: { ...brandStore },
				app: { ...appStore },
				trace: { ...traceStore },
			},
		});

		afterEach(() => {
			jest.clearAllMocks();
			jest.restoreAllMocks();
		});
	});
	it("checks if app isDemoMode is true it shows demoHeader", () => {
		store.state.app.isDemoMode = true;
		wrapper = mount(DemoHeader, {
			localVue,
			store,
			i18n,
		});
		const header = wrapper.find('[data-test="demoHeader"]');
		expect(header.isVisible()).toBe(true);
	});

	it("checks if app isDemoMode is false it does not show demoHeader", () => {
		store.state.app.isDemoMode = false;
		wrapper = mount(DemoHeader, {
			localVue,
			store,
			i18n,
		});
		const header = wrapper.find('[data-test="demoHeader"]');
		expect(header.isVisible()).toBe(false);
	});

	it("checks if app isDemoMode is true and triggers toggleDemoMode function switches to false", async () => {
		store.state.app.isDemoMode = true;
		wrapper = mount(DemoHeader, {
			localVue,
			store,
			i18n,
		});

		const button = wrapper.find('[data-test="demoToggleButton"]');
		expect(wrapper.vm.isDemoMode).toBe(true);
		await button.trigger("click");
		expect(wrapper.vm.isDemoMode).toBe(false);
		expect(clearAllStore).toHaveBeenCalled();
	});
});
