import {
  getDifference,
  sanitizeString,
  getStringRecombinations
} from "@/utils/stringUtils.js";
const minViability = 0.4;
describe("Testing getDifference method", () => {
  it("same strings should return exactly 1", () => {
    expect(getDifference("test", "test")).toBe(1);
  });

  it(`similar strings should return${minViability}> x ≥ 1`, () => {
    const value = getDifference("Comunidad Valenciana", "Comunitat Valenciana");

    expect(value).toBeGreaterThan(minViability);
    expect(value).toBeLessThanOrEqual(1);
  });

  it("different strings should return 0", () => {
    expect(getDifference("test", "patata")).toBe(0);
  });

  it(`strings with ñ => ny should return ${minViability}> x ≥ 1 `, () => {
    const value = getDifference("cataluña", "catalunya");

    expect(value).toBeGreaterThan(minViability);
    expect(value).toBeLessThanOrEqual(1);
  });

  it("if one of params is not a string, should throw an error", () => {
    expect(getDifference(null, "test")).toEqual(
      new TypeError("One or both parameters are not strings")
    );
  });
});

describe("Testing sanitizeString method", () => {
  it("Accents should be removed", () => {
    expect(sanitizeString("Mónaco")).toEqual("Monaco");
  });
  it("umlaut shoud be removed", () => {
    expect(sanitizeString("Müller")).toEqual("Muller");
  });
  it("Circumflex shoud be removed", () => {
    expect(sanitizeString("île")).toEqual("ile");
  });
  it("diacritic shoud be removed", () => {
    expect(sanitizeString("Makélélé")).toEqual("Makelele");
  });
  it("ç shoud be removed", () => {
    expect(sanitizeString("Calçot")).toEqual("Calcot");
  });
});

describe("Testing getStringRecombinations method", () => {
  // Results are based on n! on n is the array length
  it("Should return an array with 2 results", async () => {
    const result = getStringRecombinations(["One", "Two"]);

    expect(result.length).toBe(2);
    expect(result).toEqual(["One Two", "Two One"]);
  });

  it("Should return an array with 6 results", async () => {
    const result = getStringRecombinations(["One", "Two", "Three"]);

    expect(result.length).toBe(6);

    expect(result).toEqual([
      "One Two Three",
      "One Three Two",
      "Two One Three",
      "Two Three One",
      "Three One Two",
      "Three Two One"
    ]);
  });
});
