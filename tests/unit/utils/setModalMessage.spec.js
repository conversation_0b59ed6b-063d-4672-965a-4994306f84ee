import { shallowMount, createLocalVue } from "@vue/test-utils";
import Vuex from "vuex";
import Search from "@/views/Search.vue";
import { i18n } from "@/locales";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("setModalMessage function in Search view", () => {
  let wrapper;
  let store;

  beforeEach(() => {
    store = new Vuex.Store();
    wrapper = shallowMount(Search, {
      localVue,
      store,
      i18n
    });
  });

  it("should set modalMessage correctly for reservationNotFound", async () => {
    const errorMessage = wrapper.vm.reservationErrorMessage.NOT_FOUND;

    await wrapper.vm.setModalMessage(errorMessage);
    expect(wrapper.vm.modalMessage).toBe("We have been unable to find a reservation with the details provided. Please, try again.");
  });

  it("should set modalMessage correctly for integrationError", async () => {
    const errorMessage = wrapper.vm.reservationErrorMessage.BAD_INTEGRATION;

    await wrapper.vm.setModalMessage(errorMessage);
    expect(wrapper.vm.modalMessage).toBe("Unfortunately, the online check-in service is currently unavailable. <br> Please try again at a later date or contact our staff for more information and assistance.");
  });

  it("should set modalMessage correctly for groupReservationError", async () => {
    const errorMessage = wrapper.vm.reservationErrorMessage.GROUP_RESERVATION;

    await wrapper.vm.setModalMessage(errorMessage);
    expect(wrapper.vm.modalMessage).toBe("Unfortunately the online check-in process is not available due to the fact that your reservation is a group booking. Please go to the front desk to check in.");
  });

  it("should set modalMessage correctly by default", async () => {
    await wrapper.vm.setModalMessage(null);
    
    expect(wrapper.vm.modalMessage).toBe("Unfortunately, the online check-in service is currently unavailable. <br> Please try again at a later date or contact our staff for more information and assistance.");
  });

});
