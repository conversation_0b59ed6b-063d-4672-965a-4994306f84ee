import { getMostSimilarGuest } from "@/utils/guestSimilarity";

const guestList = [
  {
    pms_id: "HUES-744885",
    position: null,
    validated: false,
    holder: false,
    pax_type: "AD",
    email: null,
    gender: "male",
    birthday: "1991-09-28",
    nationality: "34__",
    document_number: "12345678A",
    address: "Calle Falsa 123 Palma",
    city: "Palma",
    province: null,
    postal_code: "07009",
    telephone: "",
    birth_country: "34__",
    residence_country: "",
    processCompleted: false,
    name: "<PERSON><PERSON><PERSON>",
    surname: "<PERSON><PERSON>",
    full_name: "<PERSON><PERSON><PERSON> Perro"
  },
  {
    pms_id: "HUES-744884",
    position: null,
    validated: false,
    holder: true,
    pax_type: "AD",
    email: null,
    gender: "female",
    birthday: "1980-01-01",
    nationality: "34__",
    document_number: "99999999R",
    address: "Calle Falsa 123 Palma",
    city: "Palma",
    province: null,
    postal_code: "07009",
    telephone: "",
    birth_country: "34__",
    residence_country: "",
    processCompleted: false,
    name: "<PERSON>",
    surname: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    full_name: "<PERSON>"
  },
  {
    pms_id: null,
    position: null,
    validated: false,
    holder: false,
    pax_type: "AD",
    name: null,
    surname: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_number: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false
  }
];

describe("Guest similarity", () => {
  describe("getMostSimilarGuest method", () => {
    it("If theres no data, returns an empty Object", async () => {
      const result = getMostSimilarGuest({}, guestList);

      expect(result).toEqual({});
    });

    it("If document_number matches exactly, return that guest", async () => {
      const result = getMostSimilarGuest(
        {
          document_number: "99999999R",
          full_name: "Agallas Perro",
          name: "Agallas",
          surname: "Perro",
          second_surname: "",
          birthday_date: "1991-09-28"
        },
        guestList
      );

      expect(result).toMatchObject({
        document_number: "99999999R",
        name: "Carmen",
        surname: "Española",
        full_name: "Carmen Española"
      });
    });
    it("If there's no coincidences, return an empty Object", async () => {
      const guestList = [
        {
          pms_id: "HUES-744885",
          position: null,
          validated: false,
          holder: false,
          pax_type: "AD",
          name: null,
          surname: null,
          email: null,
          gender: null,
          birthday: null,
          nationality: null,
          document_number: null,
          address: null,
          city: null,
          province: null,
          postal_code: null,
          telephone: null,
          birth_country: null,
          residence_country: null,
          processCompleted: false
        },
        {
          pms_id: "HUES-744884",
          position: null,
          validated: false,
          holder: false,
          pax_type: "AD",
          name: null,
          surname: null,
          email: null,
          gender: null,
          birthday: null,
          nationality: null,
          document_number: null,
          address: null,
          city: null,
          province: null,
          postal_code: null,
          telephone: null,
          birth_country: null,
          residence_country: null,
          processCompleted: false
        },
        {
          pms_id: null,
          position: null,
          validated: false,
          holder: false,
          pax_type: "AD",
          name: null,
          surname: null,
          email: null,
          gender: null,
          birthday: null,
          nationality: null,
          document_number: null,
          address: null,
          city: null,
          province: null,
          postal_code: null,
          telephone: null,
          birth_country: null,
          residence_country: null,
          processCompleted: false
        }
      ];

      const result = getMostSimilarGuest(
        {
          document_number: "55555555A",
          full_name: "Eustaquio Habichuela",
          name: "Eustaquio",
          second_surname: "Habichuela",
          birthday_date: "1950-08-04"
        },
        guestList
      );

      expect(result).toEqual({});
    });

    it("If guestList is empty, return an empty Object", async () => {
      const guestList = [];

      const result = getMostSimilarGuest(
        {
          document_number: "55555555A",
          full_name: "Eustaquio Habichuela",
          name: "Eustaquio",
          second_surname: "Habichuela",
          birthday_date: "1950-08-04"
        },
        guestList
      );

      expect(result).toEqual({});
    });

    it("If one pair of  evaluated params are null, return 0 as coincidence", () => {
      const guestList = [
        {
          pms_id: "HUES-744885",
          position: null,
          validated: false,
          holder: false,
          pax_type: "AD",
          name: null,
          surname: null,
          email: null,
          gender: null,
          birthday: null,
          nationality: null,
          document_number: null,
          address: null,
          city: null,
          province: null,
          postal_code: null,
          telephone: null,
          birth_country: null,
          residence_country: null,
          processCompleted: false
        }
      ];

      const result = getMostSimilarGuest(
        {
          document_number: null,
          full_name: "Eustaquio Habichuela",
          name: "Eustaquio",
          second_surname: "Habichuela",
          birthday_date: "1950-08-04"
        },
        guestList
      );

      expect(result).toEqual({});
    });

    it("If scan data coincides with selected guest data, return an empty Object", () => {
      const result = getMostSimilarGuest(
        {
          document_number: "12345678F",
          full_name: "Carmen Española",
          second_surname: "",
          birthday_date: "1991-09-28"
        },
        guestList,
        {
          pms_id: "HUES-744884",
          position: null,
          validated: false,
          holder: true,
          pax_type: "AD",
          email: null,
          gender: "female",
          birthday: "1980-01-01",
          nationality: "34__",
          document_number: "99999999R",
          address: "Calle Falsa 123 Palma",
          city: "Palma",
          province: null,
          postal_code: "07009",
          telephone: "",
          birth_country: "34__",
          residence_country: "",
          processCompleted: false,
          name: "Carmen",
          surname: "Española",
          full_name: "Carmen Española"
        }
      );

      expect(result).toEqual({});
    });
  });
});
