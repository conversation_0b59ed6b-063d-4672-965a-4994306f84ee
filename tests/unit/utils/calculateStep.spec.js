import { calculateStep } from "../../../src/router/routes/helpers";

describe("calculateStep function", () => {
	describe("Logs errors properly", () => {
		test("Logs error when a wrong mode is passed as parameter", () => {
			const consoleErrorSpy = jest
				.spyOn(console, "error")
				.mockImplementation(() => {});

			calculateStep("totalSteps", "wrongMode", {
				signedDocuments: false,
				telephone: false,
				paymentsActive: false,
				comments: false,
			});

			expect(consoleErrorSpy).toHaveBeenCalledWith("Invalid mode 'wrongMode'");
			expect(
				calculateStep("totalSteps", "wrongMode", {
					signedDocuments: false,
					telephone: false,
					paymentsActive: false,
					comments: false,
				}),
			).toBe(null);

			consoleErrorSpy.mockRestore();
		});

		test("Logs error when a wrong stepName is passed as parameter in reception mode", () => {
			const consoleErrorSpy = jest
				.spyOn(console, "error")
				.mockImplementation(() => {});

			calculateStep("wrongStepName", "reception", {
				signedDocuments: true,
			});

			expect(consoleErrorSpy).toHaveBeenCalledWith(
				"Invalid stepName 'wrongStepName' for reception mode",
			);
			expect(
				calculateStep("wrongStepName", "reception", {
					signedDocuments: true,
				}),
			).toBe(null);

			consoleErrorSpy.mockRestore();
		});
	});

	describe("Autocheckin mode steps", () => {
		test("calculates total steps correctly", () => {
			expect(
				calculateStep("totalSteps", "autocheckin", {
					signedDocuments: false,
					telephone: false,
					paymentsActive: false,
					comments: false,
				}),
			).toBe(6);
			expect(
				calculateStep("totalSteps", "autocheckin", {
					signedDocuments: true,
					telephone: false,
					paymentsActive: false,
					comments: false,
				}),
			).toBe(7);
			expect(
				calculateStep("totalSteps", "autocheckin", {
					signedDocuments: false,
					telephone: true,
					paymentsActive: true,
					comments: false,
				}),
			).toBe(8);
			expect(
				calculateStep("totalSteps", "autocheckin", {
					signedDocuments: true,
					telephone: false,
					paymentsActive: true,
					comments: true,
				}),
			).toBe(9);
			expect(
				calculateStep("totalSteps", "autocheckin", {
					signedDocuments: true,
					telephone: true,
					paymentsActive: true,
					comments: true,
				}),
			).toBe(10);
		});

		test("calculates phone step correctly", () => {
			expect(
				calculateStep("phone", "autocheckin", {
					signedDocuments: false,
				}),
			).toBe(6);
			expect(
				calculateStep("phone", "autocheckin", {
					signedDocuments: true,
				}),
			).toBe(7);
		});

		test("calculates payment step correctly", () => {
			expect(
				calculateStep("payment", "autocheckin", {
					signedDocuments: false,
					telephone: false,
				}),
			).toBe(6);
			expect(
				calculateStep("payment", "autocheckin", {
					signedDocuments: true,
					telephone: false,
				}),
			).toBe(7);
			expect(
				calculateStep("payment", "autocheckin", {
					signedDocuments: true,
					telephone: true,
				}),
			).toBe(8);
		});

		test("calculates extraInformation step correctly", () => {
			expect(
				calculateStep("extraInformation", "autocheckin", {
					signedDocuments: false,
					telephone: false,
					paymentsActive: false,
				}),
			).toBe(6);
			expect(
				calculateStep("extraInformation", "autocheckin", {
					signedDocuments: false,
					telephone: true,
					paymentsActive: false,
				}),
			).toBe(7);
			expect(
				calculateStep("extraInformation", "autocheckin", {
					signedDocuments: true,
					telephone: false,
					paymentsActive: true,
				}),
			).toBe(8);
			expect(
				calculateStep("extraInformation", "autocheckin", {
					signedDocuments: true,
					telephone: true,
					paymentsActive: true,
				}),
			).toBe(9);
		});

		test("calculates confirmation step correctly", () => {
			expect(
				calculateStep("confirmation", "autocheckin", {
					signedDocuments: false,
					telephone: false,
					paymentsActive: false,
					comments: false,
				}),
			).toBe(6);
			expect(
				calculateStep("confirmation", "autocheckin", {
					signedDocuments: false,
					telephone: true,
					paymentsActive: false,
					comments: false,
				}),
			).toBe(7);
			expect(
				calculateStep("confirmation", "autocheckin", {
					signedDocuments: true,
					telephone: false,
					paymentsActive: true,
					comments: false,
				}),
			).toBe(8);
			expect(
				calculateStep("confirmation", "autocheckin", {
					signedDocuments: true,
					telephone: true,
					paymentsActive: true,
					comments: false,
				}),
			).toBe(9);
			expect(
				calculateStep("confirmation", "autocheckin", {
					signedDocuments: true,
					telephone: true,
					paymentsActive: true,
					comments: true,
				}),
			).toBe(10);
		});
	});

	describe("Reception mode steps", () => {
		test("calculates total steps correctly", () => {
			expect(
				calculateStep("totalSteps", "reception", {
					signedDocuments: false,
					scanOnReception: false,
				}),
			).toBe(5);
			expect(
				calculateStep("totalSteps", "reception", {
					signedDocuments: true,
					scanOnReception: false,
				}),
			).toBe(6);
			expect(
				calculateStep("totalSteps", "reception", {
					signedDocuments: true,
					scanOnReception: true,
				}),
			).toBe(7);
		});

		test("calculates identityVerification step correctly", () => {
			expect(
				calculateStep("identityVerification", "reception", {
					scanOnReception: false,
				}),
			).toBe(4);
			expect(
				calculateStep("identityVerification", "reception", {
					scanOnReception: true,
				}),
			).toBe(5);
		});

		test("calculates documents step correctly", () => {
			expect(
				calculateStep("documents", "reception", {
					scanOnReception: false,
				}),
			).toBe(5);
			expect(
				calculateStep("documents", "reception", {
					scanOnReception: true,
				}),
			).toBe(6);
		});

		test("calculates confirmation step correctly", () => {
			expect(
				calculateStep("confirmation", "reception", {
					scanOnReception: false,
					signedDocuments: false,
				}),
			).toBe(5);
			expect(
				calculateStep("confirmation", "reception", {
					scanOnReception: true,
					signedDocuments: false,
				}),
			).toBe(6);
			expect(
				calculateStep("confirmation", "reception", {
					scanOnReception: true,
					signedDocuments: true,
				}),
			).toBe(7);
		});
	});
});
