import { chainBrand, defaultBrand } from "../../../mocks/modules/brand/data";

describe("Start checkin on parent brand", () => {
  const timeout = 10000;

  before(() => {
    cy.overrideEndpointResponse(
      {
        method: "get",
        endpoint: "*/brands/:id/info",
        status: 200
      },
      chainBrand
    );
    cy.startServiceWorker();
    cy.startFlow(chainBrand.data.id, false);
  });

  after(() => {
    cy.restartServiceWorkerHandlers();
  });

  it("Prints select of  brand childs with autocheckin product activated when accessing to autocheckin with a parent brand", () => {
    cy.get('[data-test="brand_name"]', { timeout })
      .children("option")
      .then(options => {
        const actual = [...options].map(option => option.value);
        expect(actual).to.deep.eq(["0", "2", "3"]);
      });

    cy.get('[data-test="brandSelectorButton"]').should("be.disabled");
  });

  it("Redirects you to the child's autocheckin when one of them is selected.", () => {
    cy.get('[data-test="brand_name"]')
      .select("Hotel 2")
      .should("have.value", "2");

    cy.get('[data-test="brandSelectorButton"]', { timeout })
      .first()
      .click();

    // Override again child brand info to get child brand
    defaultBrand.data.id = 2;
    cy.overrideEndpointResponse(
      {
        method: "get",
        endpoint: "*/brands/:id/info",
        status: 200
      },
      defaultBrand
    );
    cy.url({ timeout }).should("include", "2");
    cy.expect('[data-test="gdprText"]', { timeout: 60000 }).not.to.be.empty;
  });
});
