import "cypress-file-upload";
import { defaultBrand, defaultConfig } from "../../../mocks/modules/brand/data";
import {
	errorScan,
	passportScan,
	identityCardScan,
} from "../../../mocks/modules/ocr/data";
import {
	foreignSuggestion,
	foreignSelection,
} from "../../../mocks/modules/aws/data";
import {
	defaultReservation,
	multipleRoomsReservation,
	similarReservation,
} from "../../../mocks/modules/integration/data";

describe("Full Process test", () => {
	const cypressEnv = Cypress.env("HL_ENV") || "testing";
	const envConfig = Cypress.env(cypressEnv);
	const timeout = 30000;
	const request = {
		method: "post",
		status: 200,
		endpoint: "*/brands/:id/scan",
	};

	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			defaultConfig,
			false,
		);

		cy.startServiceWorker();

		cy.startFlow(defaultBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("Fill reservation form", () => {
		cy.overrideEndpointResponse(
			{
				method: "get",
				status: 200,
				endpoint: "*/brands/:id/reservations",
			},
			similarReservation,
		);

		//Information modal
		cy.get('[data-test="info-button-help"]', { timeout }).click();

		getTranslations(
			"es",
			"searchFormComponent.reservation_code.modal.message",
		).then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});

		cy.get('[data-test="closeModal"]').filter(":visible").click();

		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout })
			.first()
			.click();
	});

	it("Backwards flow", () => {
		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "status");

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "reservations");

		cy.get('[data-test="rebookLink"]').click();
		cy.url({ timeout }).should("include", "search");
	});

	it("Search localizer with multipleRooms", () => {
		cy.overrideEndpointResponse(
			{
				method: "get",
				status: 200,
				endpoint: "*/brands/:id/reservations",
			},
			multipleRoomsReservation,
		);
		//Search reservations
		cy.fillSearchForm(
			Cypress.env("multipleRoomsReservationId"),
			Cypress.env("multipleRoomsReservationName"),
		);

		cy.url({ timeout }).should("include", "reservations");
	});

	it("Multiple rooms - Select reservation 1. Guests should be visible and it can go back", () => {
		//Get the first room
		cy.get('[data-test="continueReservation"]', { timeout }).first().click();

		cy.url({ timeout }).should("include", "status");

		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).should(
			"be.visible",
		);

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "reservations");
	});

	it("Multiple rooms - Select reservation 2. Guests should be visible and it can go back", () => {
		//Get the second room (index 1 in array of reservations)
		cy.get('[data-test="continueReservation"]', { timeout }).eq(1).click();

		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).should(
			"be.visible",
		);

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "reservations");

		cy.get('[data-test="rebookLink"]').click();
		cy.url({ timeout }).should("include", "search");
	});

	it("Fill reservation form again", () => {
		//Search reservations
		cy.overrideEndpointResponse(
			{
				method: "get",
				status: 200,
				endpoint: "*/brands/:id/reservations",
			},
			defaultReservation,
		);

		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation again", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(1).click();
	});

	it("Uploading an expired document should popup a modal and redirect to status page", () => {
		cy.overrideEndpointResponse(request, errorScan("Document Expired"));
		cy.get('[data-test="fileInput"]').attachFile("expiredPortugalPassport.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });

		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();

		cy.url({ timeout }).should("include", "status");

		cy.get('[data-test="adult"]:not(:disabled)', { timeout }).eq(1).click();
	});

	it("Uploading an image that is not a document should not let you continue.", () => {
		cy.overrideEndpointResponse(
			request,
			errorScan("RequireValuesNotFoundError"),
		);
		cy.uploadFile("fakePassport.png");
		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");

		getTranslations("es", "scan.error.noDataRequiredError").then(
			(translation) => {
				cy.get('[data-test="modalBody"]').should("contain", translation);
			},
		);

		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("Uploading a document with no side data should set it to front", () => {
		identityCardScan.data.side = null;
		cy.overrideEndpointResponse(request, identityCardScan);
		cy.get('[data-test="fileInput"]').attachFile("spanishIdCard.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });
		cy.get('[data-test="fileButton"]', { timeout }).should("be.visible");
		cy.get('[data-test="showDocumentModal"]', { timeout }).should("be.visible");
		cy.get('[data-test="scannedPart"]', { timeout }).should("be.visible");
		getTranslations("es", "documentUploader.front").then((translation) => {
			cy.get('[data-test="scannedPart"]').should("contain", translation);
		});

		cy.get('[data-test="deleteDocument"]', { timeout }).click();
	});
	
	it("User clicks on document image, modal should pop up with", () => {
		cy.overrideEndpointResponse(request, passportScan);
		cy.get('[data-test="fileInput"]').attachFile("goodPassport.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });

		cy.wait(1000);

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "scan");

		cy.wait(1000);

		cy.get('[data-test="showDocumentModal"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
	});

	it("Clicking on 'Delete' button should clear docuemnt", () => {
		cy.get('[data-test="deleteDocument"]', { timeout }).click();
	});

	it("Scan document again", () => {
		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "status");

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "reservations");

		cy.get('[data-test="rebookLink"]').click();
		cy.url({ timeout }).should("include", "search");

		const modifiedReservation = { ...defaultReservation };
		modifiedReservation.data[0].guests[0].validated = true;
		cy.overrideEndpointResponse(
			{
				method: "get",
				status: 200,
				endpoint: "*/brands/:id/reservations",
			},
			modifiedReservation,
		);

		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);
		cy.get('[data-test="continueReservation"]', { timeout }).click();
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(0).click();
		
		const modifiedPassport = { ...passportScan, data: { ...passportScan.data, document_number: "15CV28143" } };
		cy.overrideEndpointResponse(request, modifiedPassport)
		cy.get('[data-test="fileInput"]').attachFile("goodPassport.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });
	});

	it("If a document is scanned with the same number than the one of a guest with completed check-in, it shows error modal and redirects to status" , () => {
		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();

		cy.url({ timeout }).should("include", "status");
	});

	it("Prevents submission if the user types a repeated doc number, then allows submission after fixing it", () => {
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(0).click();
		cy.doManualProcess(timeout);		

		cy.get('[data-test="name"]').clear();
		cy.get('[data-test="name"]', { timeout }).type("Frank");
		cy.get('[data-test="surname"]').clear();
		cy.get('[data-test="surname"]', { timeout }).type("Green");
		cy.get('[data-test="nationality"]', { timeout }).clear();
		cy.get('[data-test="nationality"]', { timeout }).type("Argentina");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();
		cy.get('[data-test="nationality"]', { timeout }).clear();
		cy.get('[data-test="nationality"]', { timeout }).type("Francia");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();
		cy.get('[data-test="document_type"]').select("passport");
		cy.get('[data-test="document_number"]').clear();
		cy.get('[data-test="document_number"]', { timeout }).type("15CV28143")

		cy.get('[data-test="document_number"]')
    .parent()
    .should("have.class", "border-red-400");

		cy.get('[data-test="address"]').clear();
		cy.get('[data-test="address"]', {
			timeout,
		}).type("Carrer miquel forteza y piña 3", { delay: 50 });
		cy.wait(1000);
		cy.get('[data-test="address-option"]', { timeout }).first().click();
		cy.wait(1000);

		cy.get('[data-test="validate-data-button"]').click();
		cy.url({ timeout }).should("include", "validate-data");

		cy.get('[data-test="document_number"]').clear();
		cy.get('[data-test="document_number"]', { timeout }).type("15CV99999")
		cy.get('[data-test="document_number"]')
    .parent()
    .should("not.have.class", "border-red-400");

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "status");

	});

	it("If all documents are uploaded, click on 'Continue' and go next", () => {
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(0).click();
		const modifiedPassport = { ...passportScan, data: { ...passportScan.data, surname: "Grey", name: "John"} };
		
		cy.overrideEndpointResponse(request, modifiedPassport)
		cy.get('[data-test="fileInput"]').attachFile("goodPassport.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });

		cy.wait(1000);
		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="continue-button"]', { timeout }).click();
		cy.url({ timeout }).should("include", "validate-data");
	});

	it("Show validate form", () => {
		// modal pops up
		cy.get('[data-test="address"]').clear();
		// Type first letter
		cy.get('[data-test="address"]', { timeout }).type("a");

		cy.get('[data-test="name"]').clear();

		cy.get('[data-test="name"]', { timeout }).type("Test");

		cy.get('[data-test="surname"]').clear();

		cy.get('[data-test="surname"]', { timeout }).type("Testing");

		cy.get('[data-test="nationality"]', { timeout }).clear();

		cy.get('[data-test="nationality"]', { timeout }).type("Argentina");

		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		cy.get('[data-test="document_type"]').find('option').should('have.length', 2);

		cy.get('[data-test="nationality"]', { timeout }).clear();

		cy.get('[data-test="nationality"]', { timeout }).type("Francia");

		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		cy.get('[data-test="document_type"]').find('option').should('have.length', 3);

		cy.get('[data-test="document_type"]').select("passport");

		// Continue flow
		cy.get('[data-test="address"]').clear();
		cy.get('[data-test="address"]', {
			timeout,
		}).type("Carrer miquel forteza y piña 3", { delay: 50 });

		cy.wait(1000);

		cy.get('[data-test="address-option"]', { timeout }).first().click();
		cy.wait(1000);
		cy.get('[data-test="address"]').should(
			"have.value",
			"Carrer Miquel Forteza i Pinya",
		);
		cy.get('[data-test="street_number"]').should(
			"have.value",
			"3",
		);
		cy.get('[data-test="municipality"]').should("have.value", "Palma");
		cy.get('[data-test="postal_code"]', { timeout }).should(
			"have.value",
			"07007",
		);
		cy.get('[data-test="postal_code"]').clear();

		cy.get('[data-test="validate-data-button"]').click();
		cy.focused().should("have.attr", "data-test", "postal_code");
	});

	it("If address changes to a non Spanish address, ccaa and province should be disabled and input should be filled with new information", () => {
		cy.overrideEndpointResponse(
			{
				method: "post",
				status: 200,
				endpoint:
					"https://places.geo.eu-west-1.amazonaws.com/places/v0/indexes/*/search/suggestions",
			},
			foreignSuggestion,
		);

		cy.overrideEndpointResponse(
			{
				method: "post",
				status: 200,
				endpoint:
					"https://places.geo.eu-west-1.amazonaws.com/places/v0/indexes/*/search/text",
			},
			foreignSelection,
		);
		cy.get('[data-test="address"]', {
			timeout,
		}).clear();
		cy.get('[data-test="address"]', {
			timeout,
		}).type("Chemin Ample, 31800", { delay: 50 });

		cy.wait(1000);

		cy.get('[data-test="address-option"]', { timeout }).first().click();
		cy.wait(1000);
		cy.get('[data-test="address"]').should("have.value", "Chemin Ample");
		cy.get('[data-test="municipality"]').should("have.value", "Valentine");
		cy.get('[data-test="postal_code"]', { timeout })
			.invoke("val")
			.should("not.be.empty");

		cy.get('[data-test="residence_country"]', { timeout }).should(
			"have.value",
			"Francia",
		);
		cy.get('[data-test="CCAA"]', { timeout }).should("not.exist");
		cy.get('[data-test="province"]', { timeout }).should("not.exist");
	});

	it("If country changes from ESP to Japan, CCAA and province should be disabled and GEO is disabled for address", () => {
		cy.get('[data-test="residence_country"', { timeout }).clear();
		cy.get('[data-test="residence_country"', { timeout }).type("Japón");
		cy.get('[data-test="residence_country-option"]', { timeout })
			.first()
			.click();
		cy.get('[data-test="CCAA"]', { timeout }).should("not.exist");
		cy.get('[data-test="province"]', { timeout }).should("not.exist");
		cy.get('[data-test="address"]').clear();
		cy.get('[data-test="address"]', {
			timeout,
		}).type("1-chome 9", { delay: 50 });
		cy.wait(1000);
		cy.get('[data-test="address-option"]', { timeout }).should("not.exist");
		cy.get('[data-test="address"]').should("have.value", "1-chome 9");
	});

	it("If country changes to ESP, CCAA  should be enabled", () => {
		cy.get('[data-test="residence_country"', { timeout }).clear();
		cy.get('[data-test="residence_country"', { timeout }).type("España");
		cy.get('[data-test="residence_country-option"]', { timeout })
			.first()
			.click();
		cy.get('[data-test="CCAA"]', { timeout }).should("exist");
	});

	it("If ccaa changes, zip should be cleared and if ccaa has only one subregion, it should be selected", () => {
		cy.get('[data-test="CCAA"]', { timeout }).select("18");
		cy.get('[data-test="postal_code"]', { timeout }).should("have.value", "");
		cy.get('[data-test="CCAA"]', { timeout }).should("have.value", "18");
		cy.get('[data-test="province"]', { timeout }).should("have.value", "51");
		cy.get('[data-test="validate-data-button"]').click();
		cy.focused().should("have.attr", "data-test", "postal_code");
	});
	it("If ccaa changes, zip should be cleared and if ccaa has more than one subregion, none should be selected", () => {
		cy.get('[data-test="CCAA"]', { timeout }).select("16");
		cy.get('[data-test="postal_code"]', { timeout }).should("have.value", "");
		cy.get('[data-test="CCAA"]', { timeout }).should("have.value", "16");
		cy.get('[data-test="province"]', { timeout }).should("have.value", null);
		cy.get('[data-test="validate-data-button"]').click();
		cy.focused().should("have.attr", "data-test", "postal_code");
	});

	it("If name is cleared, focus should be on name input", () => {
		cy.get('[data-test="name"]').clear();
		cy.get('[data-test="validate-data-button"]').click();
		cy.focused().should("have.attr", "data-test", "name");
		cy.get('[data-test="name"]').type("DifferentName");
	});

	it("If zip changes, ccaa and province should change", () => {
		cy.get('[data-test="postal_code"]', { timeout }).clear();
		cy.get('[data-test="postal_code"]', { timeout }).type("48002");
		cy.get('[data-test="CCAA"]', { timeout }).should("have.value", "16");
		cy.get('[data-test="province"]', { timeout }).should("have.value", "48");
	});

	it("If postalCodeValidator does exist for country and postal_code value is not correct, it should return error when attempted to validate", () => {
		cy.get('[data-test="residence_country"', { timeout }).clear();
		cy.get('[data-test="residence_country"', { timeout }).type("España");
		cy.get('[data-test="residence_country-option"]', { timeout })
			.first()
			.click();

		cy.get('[data-test="postal_code"]', { timeout }).clear();
		cy.get('[data-test="postal_code"]', { timeout }).type("1111111");
		cy.get('[data-test="validate-data-button"]').click();

		cy.focused().should("have.attr", "data-test", "postal_code");
	});

	it("If postalCodeValidator does not exist for country, it should bypass the validation", () => {
		cy.get('[data-test="residence_country"', { timeout }).clear();
		cy.get('[data-test="residence_country"', { timeout }).type("Afganistán");
		cy.get('[data-test="residence_country-option"]', { timeout })
			.first()
			.click();

		cy.get('[data-test="postal_code"]', { timeout }).clear();
		cy.get('[data-test="postal_code"]', { timeout }).type("48002123");
		cy.get('[data-test="name"]').clear();
		cy.get('[data-test="validate-data-button"]').click();

		cy.focused().should("not.have.attr", "data-test", "postal_code");

		cy.get('[data-test="name"]', { timeout }).type("DifferentName");
	});

	it("Insert wrong dates and error messages are showing", () => {
		// Test messages errors for birthday input
		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		// Can introduce under 18 age on validate data
		cy.get('[data-test="birthday_date"]').type(
			Cypress.moment().subtract(17, "years").format("DD-MM-YYYY"), { delay: 100 }
		);

		cy.get('[data-test="birthday_date_error"]').should("not.be.visible");

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		// Check date format error
		cy.get('[data-test="birthday_date"]').type(
			"32-13-2025", { delay: 100 }
		);

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		// Check error when introduce too old date
		cy.get('[data-test="birthday_date"]').type(
			Cypress.moment().subtract(121, "years").format("DD-MM-YYYY"), { delay: 100 }
		);

		getTranslations("es", "inputs.date.tooOld").then((translation) => {
			cy.get('[data-test="birthday_date_error"]').should(
				"contain",
				translation,
			);
		});

		cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

		// Check introduce valid date
		cy.get('[data-test="birthday_date"]').type(
			Cypress.moment().subtract(20, "years").format("DD-MM-YYYY"), { delay: 100 }
		);

		cy.get('[data-test="birthday_date_error"]', { timeout }).should(
			"not.be.visible",
		);

		// Test messages errors for date of expiry input
		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_expiry"]').type(
			"32-13-2025", { delay: 100 }
		);

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_expiry"]').type(
			Cypress.moment().subtract(121, "years").format("DD-MM-YYYY"), { delay: 100 }
		);

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date_of_expiry_error"]').should(
				"contain",
				translation.replace("{name}", "fecha de caducidad"),
			);
		});

		cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_expiry"]').type(
			"12122030", { delay: 100 }
		);

		cy.get('[data-test="date_of_expiry_error"]', { timeout }).should(
			"not.be.visible",
		);

		// Test messages errors for date of issue input
		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");
		cy.get('[data-test="date_of_issue"]').type(
			"32132025", { delay: 100 }
		);

		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_issue"]').type(
			Cypress.moment().add(1, "day").format("DD-MM-YYYY"), { delay: 100 }
		);

		getTranslations("es", "inputs.date.greaterThanTodayError").then(
			(translation) => {
				cy.get('[data-test="date_of_issue_error"]').should(
					"contain",
					translation.replace("{name}", "fecha de expedición"),
				);
			},
		);

		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		cy.get('[data-test="date_of_issue"]').type("12122020", { delay: 100 }
		);

		cy.get('[data-test="date_of_issue_error"]', { timeout }).should(
			"not.be.visible",
		);
	});

	it("If cancel validate data modal stay in validate-data page", () => {
		cy.get('[data-test="validate-data-button"]').click();

		if (cy.get('[data-test="modalError"]', { timeout }).should("be.visible")) {
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"John → DifferentName",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Francia → Afganistán",
			);
			// Province and CCAA shouldn't appear in differences modal because they didnt have a value from scanned data
			cy.get('[data-test="modalError', { timeout }).should(
				"not.contain",
				"País Vasco",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"not.contain",
				"Bizkaia",
			);
			cy.get('[data-test="close-differences-modal"]', { timeout }).click();
		}
		//Redirect from optional route return error, ignore error
		Cypress.on("uncaught:exception", () => {
			return false;
		});

		cy.url({ timeout }).should("include", "validate");
	});

	it("If all ok, show again differences and go next page", () => {
		cy.get('[data-test="validate-data-button"]').click();

		if (cy.get('[data-test="modalError"]', { timeout }).should("be.visible")) {
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"John → DifferentName",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Francia → Afganistán",
			);
			// Province and CCAA shouldn't appear in differences modal because they didnt have a value from scanned data
			cy.get('[data-test="modalError', { timeout }).should(
				"not.contain",
				"País Vasco",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"not.contain",
				"Bizkaia",
			);
			cy.get('[data-test="accept-differences-modal"]', { timeout })
				.filter(":visible")
				.click();
		}
		//Redirect from optional route return error, ignore error
		Cypress.on("uncaught:exception", () => {
			return false;
		});

		cy.url({ timeout }).should("include", "documents");
	});

	it("FinishFlowAfterCompleteGuestData", () => {
		cy.readDocuments(envConfig.documents, timeout);
		cy.signsDocuments(timeout);
		cy.bypassToConfirmation(timeout);

		cy.get('[data-test="closeWindowMessage"]', { timeout }).should(
			"be.visible",
		);

		cy.get('[data-test="shareButton"]', { timeout })
			.should("be.visible")
			.click();

		cy.url({ timeout }).should("include", "share");

		cy.get('[data-test="backToConfirmation"]', { timeout })
			.should("be.visible")
			.click();

		cy.url({ timeout }).should("include", "confirmation");

		cy.get('[data-test="nextGuest"]', { timeout }).should("be.visible").click();

		cy.url({ timeout }).should("include", "reservations");
	});
});
