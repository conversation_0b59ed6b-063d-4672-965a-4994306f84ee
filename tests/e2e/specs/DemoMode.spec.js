import "cypress-file-upload";
import { defaultBrand } from "../../../mocks/modules/brand/data";

describe("Validate user form", () => {
	before(() => {
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});

	it("should not show demoMode header", () => {
		cy.get('[data-test="demoHeader"]').should("not.be.visible");
	});

	it("should navigate to the brand setting ?demo=true and activate demoMode", () => {
		cy.visit("http://localhost:8080/304?demo=true");
		cy.wait(3000);
		cy.get('[data-test="demoHeader"]').should("be.visible");
		cy.reload();
		cy.wait(3000);
		cy.logMessage("now should reload page and keep demo mode active");
		cy.get('[data-test="demoHeader"]').should("be.visible");
	});

	it("should click on exit button and demoMode should be disabled", () => {
		cy.get('[data-test="demoToggleButton"]').click();
		cy.wait(3000);
		cy.get('[data-test="demoHeader"]').should("not.be.visible");
	});
});
