import "cypress-file-upload";
import { defaultBrand, defaultConfig } from "../../../mocks/modules/brand/data";
import {
	similarReservation,
} from "../../../mocks/modules/integration/data";

describe("Full Process US Date format test", () => {
	const timeout = 2000;

	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		// Clone the default brand and modify the place_country
		const modifiedBrand = { ...defaultBrand, data: { ...defaultBrand.data, place_country: "BR", timeZone:"America/Los_Angeles" } };
		cy.server();
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint:  "*/brands/:id/info",
				method: "get",
			},
			modifiedBrand, 
			false,
		);

		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			defaultConfig, 
			false,
		);

		cy.startServiceWorker();
		// Start the flow with the default brand ID
		cy.startFlow(modifiedBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("Fill reservation form", () => {
		cy.overrideEndpointResponse(
			{
				method: "get",
				status: 200,
				endpoint: "*/brands/:id/reservations",
			},
			similarReservation,
		);

		//Information modal
		cy.get('[data-test="info-button-help"]', { timeout }).click();

		getTranslations(
			"es",
			"searchFormComponent.reservation_code.modal.message",
		).then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});

		cy.get('[data-test="closeModal"]').filter(":visible").click();

		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout })
			.first()
			.click();
	});

	it("Select reservation again", () => {
		// Click the manual process button
    cy.get('[data-test="manualProcess-button"]').click();
		// Wait for the modal to appear and click the button to proceed with manual data entry
    cy.get('[data-test="goToCompleteDataManually"]').should('be.visible').click();
	});

	it("Validates other fields", () => {
		cy.get('[data-test="gender"]').select("male");
		cy.get('[data-test="document_type"]').select("passport");
		

		cy.get('[data-test="postal_code"]').clear().type("1431");
		cy.get('[data-test="nationality"]', { timeout }).type("A");
		cy.wait(timeout);
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		cy.get('[data-test="address"]', { timeout }).type("Amiens Street, IRL", {
			delay: 50,
		});
		cy.wait(1000);
		cy.get('[data-test="address-option"]', { timeout }).first().click();
	});

	it("Validate biryhday date", () => {
			// Check too old date
			cy.get('[data-test="birthday_date"]').type(
				Cypress.moment().subtract(121, "years").format("MM-DD-YYYY"),
			);
			getTranslations("es", "inputs.date.tooOld").then((translation) => {
				cy.get('[data-test="birthday_date_error"]').should(
					"contain",
					translation,
				);
			});

			// check empty input
			cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

			getTranslations("es", "inputs.emptyValueError").then((translation) => {
				cy.get('[data-test="empty-value-error"]').should("contain", translation);
			});

			// check valid date
			cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");

			cy.get('[data-test="birthday_date"]').type(
				Cypress.moment().subtract(17, "years").format("MM-DD-YYYY"), { delay: 100 }
			);
			cy.get('[data-test="birthday_date"]').type("{selectall}{backspace}");
			cy.get('[data-test="birthday_date"]').type("01011990");
			cy.get('[data-test="birthday_date_error"]').should("not.be.visible");


		});
		
		it("Validates passport issue date", () => {
			// test use date picker
			cy.get('[data-test="date_of_issue"]').click();
			cy.get('.mx-calendar-content').should('be.visible');
			cy.get('i.mx-icon-left')
				.should('be.visible')
				.click();
			cy.get('td[data-row-col="1,0"]')
				.click()
				.invoke('attr', 'title')
				.then((title) => {
					// The title is in "YYYY-MM-DD" format.
					// Convert it to "MM-DD-YYYY" if needed.
					const [year, month, day] = title.split('-');
					const formattedDate = `${month}-${day}-${year}`;

					// Now you can assert that the input contains the formatted date.
					cy.get('#date_of_issue')
						.click() // in case you need to focus it to update the value
						.invoke('val')
						.should('equal', formattedDate);
				});
	
			// Check future date
			cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");
			cy.get('[data-test="date_of_issue"]').type(
				Cypress.moment().add(1, "day").format("MM-DD-YYYY"), { delay: 100 }
			);

			getTranslations("es", "inputs.date.greaterThanTodayError").then(
				(translation) => {
					cy.get('[data-test="date_of_issue_error"]').should(
						"contain",
						translation.replace("{name}", "fecha de expedición"),
					);
				},
			);

		// Check invalid date (Value above 31)
		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");
		cy.get('[data-test="date_of_issue"]').type("32/13/2025");
		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		// Check invalid date (Values in DD-MM-YYYY format)
		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");
		cy.get('[data-test="date_of_issue"]').type("25/11/2025");
		getTranslations("es", "inputs.dateFormatError").then((translation) => {
			cy.get('[data-test="date-format-error"]').should("contain", translation);
		});

		// check empty input
		cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");

		getTranslations("es", "inputs.emptyValueError").then((translation) => {
			cy.get('[data-test="empty-value-error"]').should("contain", translation);
		});

		// Check valid date
			cy.get('[data-test="date_of_issue"]').type("{selectall}{backspace}");
			cy.get('[data-test="date_of_issue"]').type("12252020", { delay: 100 });
			cy.get('[data-test="date_of_issue_error"]').should("not.be.visible");
	});

	it("Validates passport expire date", () => {
			// test use date picker
			cy.get('[data-test="date_of_expiry"]').click();
			cy.get('.mx-calendar-content').should('be.visible');
			cy.get('i.mx-icon-right').should('be.visible').click();
			cy.get('td[data-row-col="1,0"]')
				.click()
				.invoke('attr', 'title')
				.then((title) => {
					// Reformat the date from "YYYY-MM-DD" to "MM-DD-YYYY"
					const [year, month, day] = title.split('-');
					const formattedDate = `${month}-${day}-${year}`;
					cy.get('#date_of_expiry')
						.click()
						.invoke('val')
						.should('equal', formattedDate);
				});


			// Check Invalid Date Format (values out of range)
			cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");
			cy.get('[data-test="date_of_expiry"]').type("32/13/2025");
			getTranslations("es", "inputs.dateFormatError").then((translation) => {
				cy.get('[data-test="date-format-error"]').should("contain", translation);
			});

			// Check Invalid Date Format (using DD-MM-YYYY instead of MM-DD-YYYY)
			cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");
			cy.get('[data-test="date_of_expiry"]').type("25/11/2025");
			getTranslations("es", "inputs.dateFormatError").then((translation) => {
				cy.get('[data-test="date-format-error"]').should("contain", translation);
			});

			// Check Empty Input Error
			cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");
			getTranslations("es", "inputs.emptyValueError").then((translation) => {
				cy.get('[data-test="empty-value-error"]').should("contain", translation);
			});

			// Check Valid Date (a properly formatted and valid date)
			cy.get('[data-test="date_of_expiry"]').type("{selectall}{backspace}");
			cy.get('[data-test="date_of_expiry"]').type("12252030", { delay: 100 });
			cy.get('[data-test="date_of_expiry_error"]').should("not.be.visible");
		});

		it("Confirms and continues with checkin process", () => {
			cy.get('[data-test="validate-data-button"]').click();
			cy.get('[data-test="closeModal"]').should("be.visible")
			cy.get('[data-test="closeModal"]').click({ multiple: true, force: true });
			cy.get('[data-test="close-document-button"]').click();
			cy.wait(1000);
			cy.get('[data-test="close-document-button"]').click();
	
			cy.logMessage(
				"should sign documents, validate email and go to pick next guest page",
			);
			cy.url({ timeout }).should("include", "signature");
			cy.get("canvas")
				.click()
				.trigger("mousedown", { which: 1, x: 50, y: 50 })
				.trigger("mousemove", { which: 1, x: 50, y: 60 })
				.trigger("mouseup");
			cy.get('[data-test="continue-button"]').click({ force: true });
	
			cy.logMessage("should continue without setting an email");
			cy.url({ timeout }).should("include", "send-documents");
			cy.get('[data-test="continue-button"]').click({ force: true });
			cy.wait(1000);
			cy.get('[data-test="continue-button"]').click({ force: true });
			cy.wait(1000);
			cy.get('[data-test="continue-button"]').click({ force: true });
		});
});
