import "cypress-file-upload";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import {
	notFoundReservation,
	defaultReservation,
} from "../../../mocks/modules/integration/data";
import { expiredPassportScan } from "../../../mocks/modules/ocr/data";

describe("Allow expired document on scan", () => {
	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);
	const getStore = (element) => cy.window().its(`app.$store.state.${element}`);

	const timeout = 30000;
	const request = {
		status: 200,
		endpoint: "*/brands/:id/scan",
		method: "post",
	};

	const reservationRequest = {
		method: "get",
		status: 200,
		endpoint: "*/brands/:id/reservations",
	};
	defaultConfig.data.allow_expired_documents = true;

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			defaultConfig,
			false,
		);

		cy.startServiceWorker();
		cy.startFlow("child");
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});

	it("Clicking the button without filling in the data should not follow the flow.", () => {
		cy.overrideEndpointResponse(reservationRequest, notFoundReservation, false);
		cy.get('[data-test="searchButton"]').should("be.disabled");
		cy.get('[data-test="searchButton"]').click({ force: true });
		cy.url({ timeout }).should("include", "search");
	});

	it("When wrong booking data is entered, an error should be displayed.", () => {
		cy.fillSearchForm(
			Cypress.env("errorReservationId"),
			Cypress.env("errorReservationName"),
		);

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");

		getTranslations("es", "search.reservationNotFound").then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});

		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("Failing multiple times the booking search form should let you continue and not show you an error", () => {
		getStore("brand.config.max_attempts_reservation").then((failTimes) => {
			for (let i = 0; i < failTimes; i++) {
				cy.wait(1000);
				cy.get('[data-test="searchButton"]').click();

				if (i < failTimes - 1) {
					cy.get("[data-test=closeModal]", { timeout }).should("be.visible");

					cy.get('[data-test="closeModal"]').filter(":visible").click();
				}
			}
		});
	});

	it("Click on exit button and clicking on `No' should return you to normal flow", () => {
		cy.get('[data-test="closeModal"]').filter(":visible").click();

		cy.get('[data-test="exit-button"]', { timeout }).click();
		cy.get('[data-test="header-exit-modal"]', { timeout }).should("be.visible");
		cy.get('[data-test="close-modal"]', { timeout }).click();
		cy.get('[data-test="header-exit-modal"]', { timeout }).should(
			"not.be.visible",
		);
	});

	it("Click on exit button and clicking on `Yes' should return you to Gdpr page", () => {
		cy.get('[data-test="exit-button"]', { timeout }).click();
		cy.get('[data-test="header-exit-modal"]', { timeout }).should("be.visible");
		cy.get('[data-test="exit-app"]', { timeout }).click();

		// We are on gpdr page
		cy.expect('[data-test="gdprText"]', { timeout: 60000 }).not.to.be.empty;
		cy.get('[data-test="acceptConditionsButton"]', {
			timeout: 60000,
		}).click();

		cy.url({ timeout: 60000 }).should("include", "search");
	});

	it("Can search for another reservation if exited", () => {
		cy.overrideEndpointResponse(reservationRequest, defaultReservation, false);

		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select guest", () => {
		//Select reservation
		cy.get('[data-test="adult"]:not(:disabled)', { timeout }).first().click();

		cy.url({ timeout }).should("include", "scan");
	});

	it("Scan document", () => {
		cy.overrideEndpointResponse(request, expiredPassportScan);
		cy.uploadFile("expiredPortugalPassport.png", timeout);
	});

	it("If all documents are uploaded, redirect to validate data page", () => {
		cy.url({ timeout }).should("include", "validate-data");
	});

	it("Inputs are filled correctly with scanned data", () => {
		cy.get('[data-test="name"]').should("have.value", "Name");
		cy.get('[data-test="surname"]').should("have.value", "Surname");
		cy.get("#birthday_date").should("have.value", "01-01-2000");
		cy.get('[data-test="gender"]').should("have.value", "male");
		cy.get('[data-test="document_type"]').should("have.value", "passport");
		cy.get('[data-test="document_number"]').should("have.value", "12345678A");
		cy.get('[data-test="nationality"]').should("have.value", "España");
	});

	it("Document expiry date can be lower than today or check-in date", () => {
		// Check-in date is 2022-02-14
		cy.get("#date_of_issue").should("have.value", "01-01-2015");
		cy.get("#date_of_expiry").should("have.value", "01-01-2020").click();
		cy.get('[data-test="date_of_issue_error"]', { timeout }).should(
			"not.be.visible",
		);
		cy.get('[data-test="date_of_expiry_error"]', { timeout }).should(
			"not.be.visible",
		);

		cy.get("#date_of_expiry").clear().type("31-12-2019");

		cy.get('[data-test="date_of_expiry_error"]', { timeout }).should(
			"not.be.visible",
		);

		cy.get("#date_of_issue").clear().type("05-01-2022");
		cy.get('[data-test="date_of_expiry_error"]', { timeout }).should(
			"not.be.visible",
		);
	});
});
