import "cypress-file-upload";
import { defaultBrand } from "../../../mocks/modules/brand/data";
import { passportWithAddress } from "../../../mocks/modules/ocr/data";
import {
	spanishAltSuggestion,
	spanishAltSelection,
} from "../../../mocks/modules/aws/data";

describe("Validate user form", () => {
	const timeout = 30000;
	const request = {
		status: 200,
		endpoint: "*/brands/:id/scan",
		method: "post",
	};
	before(() => {
		cy.startServiceWorker();

		cy.startFlow(defaultBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("Fill reservation form", () => {
		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout })
			.first()
			.click();
	});

	it("Scan document", () => {
		cy.overrideEndpointResponse(request, passportWithAddress);
		cy.uploadFile("goodPassport.png");
	});

	it("If all documents are uploaded, redirect to validate data page", () => {
		cy.url({ timeout }).should("include", "validate-data");
	});

	it("Show validate form and expect scanned values", () => {
		cy.get('[data-test="name"]').should("have.value", "Especimen");

		cy.get('[data-test="surname"]').should("have.value", "Testing");
		cy.get('[data-test="second_surname"]').should(
			"have.value",
			"SecondSurname",
		);
		cy.get("#birthday_date").should("have.value", "01-01-2000");
		cy.get('[data-test="gender"]').should("have.value", "male");
		cy.get('[data-test="document_type"]').should("have.value", "passport");
		cy.get('[data-test="document_number"]').should("have.value", "88888889A");
		cy.get("#date_of_issue").should("have.value", "01-01-2015");
		cy.get("#date_of_expiry").should("have.value", "01-01-2025");
		cy.get('[data-test="nationality"]').should("have.value", "España");
		cy.get('[data-test="residence_country"]').should("have.value", "España");
		cy.get('[data-test="address"]').should(
			"have.value",
			"Calle Miguel Forteza I Piña, 07007, Illes Balears",
		);
		cy.get('[data-test="municipality"]').should("have.value", "Palma");
		cy.get('[data-test="postal_code"]').should("have.value", "07007");
		cy.get('[data-test="CCAA"]').should("have.value", "04");
		cy.get('[data-test="province"]').should("have.value", "07");
	});

	it("Overwrite fields with different data", () => {
		cy.overrideEndpointResponse(
			{
				method: "post",
				status: 200,
				endpoint:
					"https://places.geo.eu-west-1.amazonaws.com/places/v0/indexes/*/search/suggestions",
			},
			spanishAltSuggestion,
		);

		cy.overrideEndpointResponse(
			{
				method: "post",
				status: 200,
				endpoint:
					"https://places.geo.eu-west-1.amazonaws.com/places/v0/indexes/*/search/text",
			},
			spanishAltSelection,
		);

		cy.get('[data-test="name"]').clear().type("Name");
		cy.get('[data-test="surname"]').clear().type("Surname");
		cy.get('[data-test="second_surname"]').clear().type("AnotherSn");
		cy.get("#birthday_date").clear().type("25-08-1995");
		cy.get('[data-test="gender"]').select("female");
		cy.get('[data-test="document_number"]').clear().type("12345678B");
		cy.get("#date_of_issue").clear().type("21-12-2021");
		cy.get("#date_of_expiry").clear().type("23-07-2030");
		cy.get('[data-test="nationality"]').clear().type("Afgan");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();
		cy.get('[data-test="document_type"]').select("passport");
		cy.get('[data-test="address"]')
			.clear()
			.type("Calle Villaverde Madrid", { delay: 200 });
		if (cy.get('[data-test="address-list"]').children()) {
			cy.get('[data-test="address-option"]', { timeout }).first().click();
		}
		cy.get('[data-test="validate-data-button"]', { timeout }).click();
	});

	it("Differences modal should show all the differences detected", () => {
		if (cy.get('[data-test="modalError', { timeout }).should("be.visible")) {
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Nombre: Especimen → Name",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Primer apellido: Testing → Surname",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Segundo apellido: SecondSurname → AnotherSn",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Fecha de nacimiento: 01-01-2000 → 25-08-1995",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Número de documento: 88888889A → 12345678B",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Fecha de expedición: 01-01-2015 → 21-12-2021",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Fecha de caducidad: 01-01-2025 → 23-07-2030",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Nacionalidad: España → Afganistán",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Dirección: Calle Miguel Forteza I Piña, 07007, Illes Balears → Calle Villaverde",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Municipio: Palma → Nuevo Baztán",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Código postal: 07007 → 28514",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Comunidad autónoma: Illes Balears → Comunidad de Madrid",
			);
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Provincia: Illes Balears → Madrid",
			);
		}
	});
});
