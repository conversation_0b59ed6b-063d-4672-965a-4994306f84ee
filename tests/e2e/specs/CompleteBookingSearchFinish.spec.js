import { finishedReservation } from "../../../mocks/modules/integration/data";
import { defaultBrand } from "../../../mocks/modules/brand/data";
describe("Search for a completed reservation", () => {
	const timeout = 30000;
	const request = {
		method: "get",
		status: 200,
		endpoint: "*/brands/:id/reservations",
	};

	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});

	it("If more than one reservation is found, a date should be requested.", () => {
		cy.overrideEndpointResponse(request, finishedReservation);
		cy.fillSearchForm(
			Cypress.env("completedReservationId"),
			Cypress.env("completedReservationName"),
			Cypress.env("completedReservationCheckinDate"),
		);

		cy.wait(1000);
		cy.get('[data-test="closeModal"]', { timeout }).should("be.visible");
		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("Entering a date on which there is no booking should show an error and not allow you to continue.", () => {
		cy.get('[data-test="check_in"]').type("1970-01-01");

		cy.get('[data-test="searchButton"]').click();

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");

		getTranslations("es", "search.reservationNotFound").then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});

		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("Entering a date on which there is a booking should show that one to review and select it", () => {
		cy.get('[data-test="check_in"]').type("{selectall}18");
		cy.get('[data-test="check_in"]').type("11");
		cy.get('[data-test="check_in"]').type("2020");
		cy.get('[data-test="searchButton"]').click();
		cy.get('[data-test="continueReservation"]', { timeout }).should(
			"be.visible",
		);
	});

	it("Entering a reservation with a completed check-in should take you to the confirmation page.", () => {
		cy.visit(`http://localhost:8080/${defaultBrand.data.id}/search`);
		cy.get('[data-test="acceptConditionsButton"]').filter(":visible").click();

		cy.overrideEndpointResponse(request, finishedReservation);
		cy.fillSearchForm(
			Cypress.env("completedReservationId"),
			Cypress.env("completedReservationName"),
		);

		cy.wait(1000);
		cy.get('[data-test="closeModal"]', { timeout }).should("be.visible");
		cy.get('[data-test="closeModal"]').filter(":visible").click();

		cy.get('[data-test="check_in"]').type(
			`${Cypress.env("completedReservationDate")}`,
		);

		cy.get('[data-test="searchButton"]').click();
		cy.url({ timeout }).should("include", "confirmation");
	});
});
