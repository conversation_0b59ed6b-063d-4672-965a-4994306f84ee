import "cypress-file-upload";
import { paymentsConfig } from "../../../../mocks/modules/brand/data";
describe("Validate user form", () => {
	const cypressEnv = Cypress.env("HL_ENV") || "testing";
	const envConfig = Cypress.env(cypressEnv);
	const timeout = 30000;
	paymentsConfig.data.disable_send_documents_page = true;

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			paymentsConfig,
			false,
		);
		cy.startServiceWorker();

		cy.startFlow(paymentsConfig.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("Fill reservation form", () => {
		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout })
			.first()
			.click();
	});

	it("Scan document", () => {
		cy.uploadFile("goodPassport.png");
	});

	it("If all documents are uploaded, redirect to validate data page", () => {
		cy.url({ timeout }).should("include", "validate-data");
	});

	it("Show validate form", () => {
		cy.get('[data-test="address"]').clear();
		// Type first letter
		cy.get('[data-test="address"]', { timeout }).type("a");

		// Continue flow
		cy.get('[data-test="address"]').clear();
		cy.get('[data-test="address"]', {
			timeout,
		}).type("Carrer miquel forteza y piña 3", { delay: 50 });

		cy.wait(1000);

		cy.get('[data-test="address-option"]', { timeout }).first().click();
		cy.wait(1000);
		cy.get('[data-test="address"]').should(
			"have.value",
			"Carrer Miquel Forteza i Pinya",
		);
		cy.get('[data-test="street_number"]').should(
			"have.value",
			"3",
		);
		cy.get('[data-test="municipality"]').should("have.value", "Palma");
		cy.get('[data-test="postal_code"]', { timeout }).should(
			"have.value",
			"07007",
		);
	});

	it("If all ok, go next page", () => {
		cy.get('[data-test="validate-data-button"]').click();

		if (cy.get('[data-test="modalError', { timeout }).should("be.visible")) {
			cy.get('[data-test="modalError', { timeout }).should(
				"contain",
				"Francia → España",
			);
			cy.get('[data-test="accept-differences-modal"]', { timeout })
				.filter(":visible")
				.click();
		}
		//Redirect from optional route return error, ignore error
		Cypress.on("uncaught:exception", () => {
			return false;
		});

		cy.url({ timeout }).should("include", "documents");
	});

	// TODO: Uncomment once payments is enabled
	// it("Bypass to payments, without showing send-documents view since disable_send_documents_page is true", () => {
	// 	cy.readDocuments(envConfig.documents, timeout);
	// 	cy.get("canvas")
	// 		.click()
	// 		.trigger("mousedown", { which: 1, x: 50, y: 50 })
	// 		.trigger("mousemove", { which: 1, x: 50, y: 60 })
	// 		.trigger("mouseup");
	// 	cy.get('[data-test="continue-button"]').click();
	// 	cy.bypassToPayments(timeout);
	// });

	// it("Payment Page", () => {
	// 	// Different inputs should be visible
	// 	cy.get('[data-test="reservation-selector-0"]').should("be.visible");
	// 	cy.get('[data-test="localizer-selector"]').should("be.visible");
	// 	cy.get('[data-test="no-payment"]').should("be.visible");

	// 	cy.get('[data-test="reservation-selector-0"]').click();

	// 	cy.get('[data-test="payment-button"]').click();
	// });

	// it("Payment proform page", () => {
	// 	cy.url({ timeout }).should("include", "payment-proform");
	// 	cy.get(".proform-detail").should("be.visible");

	// 	cy.get('[data-test="proform-payment-button"]').click();
	// 	// Todo: Full flow including payment
	// });
});
