import { defaultConfig } from "../../../mocks/modules/brand/data";
import { emptyDocuments } from "../../../mocks/modules/checkin/data";

describe("Do full process without scan document", () => {
	const timeout = 60000;
	defaultConfig.data.optional_scan = true;
	before(() => {
		cy.overrideEndpointResponse(
			{
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
				status: "200",
			},
			defaultConfig,
		);

		cy.overrideEndpointResponse(
			{
				endpoint: "*/brands/:id/documents",
				method: "get",
				status: "200",
			},
			emptyDocuments,
		);

		cy.startServiceWorker();
		cy.startFlow("optional");
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("Fill reservation form", () => {
		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout })
			.first()
			.click();
	});

	it("Scan document manually", () => {
		cy.doManualProcess(timeout);
	});

	it("Show validate form", () => {
		//Hide Validate data modal
		cy.get('[data-test="name"]', { timeout }).clear();
		// Type first letter
		cy.get('[data-test="name"]').type("F");
		// Continue flow
		cy.get('[data-test="name"]').clear();
		cy.get('[data-test="name"]').type("Fake name");
		cy.get('[data-test="surname"]').clear();
		cy.get('[data-test="surname"]').type("Fake surame");
		cy.get('[data-test="birthday_date"]').type("21-07-1998");
		cy.get('[data-test="gender"]').select("male");
		cy.get('[data-test="document_type"]').select("identity_card");
		cy.get('[data-test="document_number"]').clear();
		cy.get('[data-test="document_number"]').type("123456789A");
		cy.get('[data-test="date_of_issue"]').type("25-12-2015");
		cy.get('[data-test="date_of_expiry"]').type("25-12-2026");

		cy.get('[data-test="nationality"]').type("Rus");
		// Should show 3 results
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(0)
			.should("contain", "Bielorrusia");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(1)
			.should("contain", "Brunei Darussalam");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(2)
			.should("contain", "Chipre");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(3)
			.should("contain", "Rusia");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(4)
			.should("not.exist");

		cy.get('[data-test="nationality"]').type("ia");
		// Should show 2 results
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(0)
			.should("contain", "Bielorrusia");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(1)
			.should("contain", "Rusia");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(2)
			.should("not.exist");

		// Delete 2 letters to test the 3rd result appears again
		cy.get('[data-test="nationality"]').type("{backspace}{backspace}");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(0)
			.should("contain", "Bielorrusia");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(1)
			.should("contain", "Brunei Darussalam");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(2)
			.should("contain", "Chipre");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(3)
			.should("contain", "Rusia");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(4)
			.should("not.exist");

		cy.get('[data-test="nationality"]').clear().type("España");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(0)
			.should("contain", "España");
		cy.get('[data-test="nationality-option"]', { timeout })
			.eq(1)
			.should("not.exist");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		// If we fill the natonality and the residence country was not filled, fill the
		// residence country with the same value as the nationality
		cy.get('[data-test="residence_country"]').should("have.value", "España");

		cy.get('[data-test="address"]').clear();
		cy.get('[data-test="address"]', {
			timeout,
		}).type("Carrer miquel forteza y piña 3", { delay: 50 });
		cy.wait(1000);
		if (cy.get('[data-test="address-list"]').children()) {
			cy.get('[data-test="address-option"]', { timeout }).first().click();
			cy.wait(1000);
			cy.get('[data-test="address"]').should(
				"have.value",
				"Carrer Miquel Forteza i Pinya",
			);
			cy.get('[data-test="street_number"]').should(
				"have.value",
				"3",
			);
			cy.get('[data-test="municipality"]').should("have.value", "Palma");
			cy.get('[data-test="postal_code"]', { timeout })
				.invoke("val")
				.should("not.be.empty");
			cy.get('[data-test="CCAA"]').should("have.value", "04");
			cy.get('[data-test="province"]').should("have.value", "07");
		} else {
			cy.get('[data-test="municipality"]').type("Palma");
			cy.get('[data-test="postal_code"]').type("123456");
			cy.get('[data-test="CCAA"]').select("04");
			cy.get('[data-test="province"]').select("07");
		}

		// Wait to avoid error when search button
		cy.wait(500);
	});

	it("If ccaa changes, zip should be cleared and province may be filled", () => {
		cy.get('[data-test="CCAA"]', { timeout }).select("18");
		cy.get('[data-test="postal_code"]', { timeout }).should("have.value", "");
		cy.get('[data-test="CCAA"]', { timeout }).should("have.value", "18");
		cy.get('[data-test="province"]', { timeout }).should("have.value", "51");
	});

	it("If zip changes, ccaa and province should change", () => {
		cy.get('[data-test="postal_code"]', { timeout }).clear();
		cy.get('[data-test="postal_code"]', { timeout }).type("48002");
		cy.get('[data-test="CCAA"]', { timeout }).should("have.value", "16");
		cy.get('[data-test="province"]', { timeout }).should("have.value", "48");
	});

	it("If all ok, go next page", () => {
		cy.get('[data-test="validate-data-button"]', { timeout }).click();

		//Redirect from optional route return error, ignore error
		Cypress.on("uncaught:exception", () => {
			return false;
		});

		cy.url({ timeout }).should("include", "phone-form");
	});
	it("FinishFlowAfterCompleteGuestData", () => {
		cy.bypassToConfirmation(timeout);
	});
});
