import "cypress-file-upload";
import { emptyScan } from "../../../mocks/modules/ocr/data";

describe("Validate user form", () => {
	const timeout = 60000;

	const request = {
		code: 200,
		method: "post",
		endpoint: "*/brands/:id/scan",
	};
	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		cy.startServiceWorker();
		cy.startFlow();
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("Fill reservation form", () => {
		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout })
			.first()
			.click();
	});

	it("Show sensible data modal", () => {
		cy.overrideEndpointResponse(request, emptyScan, false);
		cy.uploadFile("spanishIdCard.png");
		cy.get('[data-test="sensible-data-modal"]', { timeout }).should(
			"be.visible",
		);
	});

	it("A Schengen Zone modal appears if filled in as DNI", () => {
		cy.get('[data-test="document_type"]').select("identity_card");
		cy.get('[data-test="nationality"]').type("Pakistan");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		cy.get('[data-test="sensible-modal-button"]', { timeout })
			.filter(":visible")
			.click();

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");
		getTranslations("es", "scan.error.schengenZoneError").then(
			(translation) => {
				cy.get('[data-test="modalBody"]').should("contain", translation);
			},
		);
		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("Sent to validate-data if the sensitive data modal is correctly filled in", () => {
		cy.get('[data-test="fileInput"]').attachFile("spanishIdCard.png");
		cy.get('[data-test="fileInput"]').trigger("input", { force: true });

		cy.get('[data-test="sensible-data-modal"]', { timeout }).should(
			"be.visible",
		);

		cy.get('[data-test="document_type"]').select("passport");

		cy.get('[data-test="sensible-modal-button"]', { timeout })
			.filter(":visible")
			.click();

		cy.url({ timeout }).should("include", "validate-data");

		cy.get('[data-test="document_type"]').should("have.value", "passport");
	});
});
