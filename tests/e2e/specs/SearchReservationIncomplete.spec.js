import { reservationLogicError } from "../../../mocks/modules/integration/data";
import { defaultBrand } from "../../../mocks/modules/brand/data";
describe("test behaviour when reservation comes with 200 but with no data - already on pre check state", () => {
	const timeout = 3000;
	const request = {
		status: 200,
		endpoint: "*/brands/:id/reservations",
		method: "get",
	};

	before(() => {
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});
	it("fills reservation form and redirect to Confirmation view", () => {
		cy.overrideEndpointResponse(request, reservationLogicError);
		cy.get('[data-test="reservation_code"]').type("testErrorReservation");
		cy.get('[data-test="last_name"]').type("randomPayload");
		cy.get('[data-test="searchButton"]').click();
		cy.url({ timeout }).should("include", "confirmation");
	});

	it("should render only valid messages", () => {
		cy.get('[data-test="confirmationCheckText"]').should("be.visible");
		cy.get('[data-test="qr-code"]').should("not.be.visible");
		cy.get('[data-test="btn-section"]').should("not.be.visible");
	});
});
