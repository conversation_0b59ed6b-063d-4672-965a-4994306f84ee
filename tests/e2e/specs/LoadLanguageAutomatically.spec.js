import { defaultBrand } from "../../../mocks/modules/brand/data";
import {
	langReservation
} from "../../../mocks/modules/integration/data";

describe("Automatic language change for guests occurs in reception mode", ()=>{
  let originalLanguage;

	const request = {
		method: "get",
		status: 200,
		endpoint: "*/brands/:id/reservations",
	};

  before(() => {
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);

    cy.window().then((win) => {
      originalLanguage = Object.keys(win.app?.$root?.$i18n._localeChainCache);
    });
    
	});


	after(() => {
		cy.restartServiceWorkerHandlers();
	});


  it("language doesn't change when we aren't in reception even if guest has lang attribute set to a language", () =>{

    cy.overrideEndpointResponse(request,langReservation,false);
    cy.log("originalLang:", originalLanguage);
		cy.get('[data-test="reservation_code"]').type('today');
    cy.get('[data-test="searchButton"]').click();

    // Pause execution to wait for the search button click to take effect
    cy.wait(1000);

    return cy.window().then((win) => {
      const currentLanguage = Object.keys(win.app?.$root?.$i18n._localeChainCache);
      expect(currentLanguage.length).to.equal(originalLanguage.length);
    });

  })

})