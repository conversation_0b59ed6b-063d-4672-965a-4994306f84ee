import { defaultBrand } from "../../../mocks/modules/brand/data";
import { allPaxReservation } from "../../../mocks/modules/integration/data";

const timeout = 60000;
describe("Flow with all possible pax types", () => {
  before(() => {
    cy.startServiceWorker();
    cy.startFlow(defaultBrand.data.id);
  });
  after(() => {
    cy.restartServiceWorkerHandlers();
  });

  it("Filling the reservation form should send you to status page", () => {
    cy.overrideEndpointResponse(
      {
        method: "get",
        status: 200,
        endpoint: "*/brands/:id/reservations"
      },
      allPaxReservation
    );

    cy.fillSearchForm(
      Cypress.env("allPaxReservationId"),
      Cypress.env("allPaxReservationName")
    );

    cy.get('[data-test="continueReservation"', { timeout }).click();
    cy.url({ timeout }).should("include", "status");
  });

  it("Selecting an adult pax should send you to the same flow as an adult", () => {
    cy.get('[data-test="adult"]:not(:disabled)', { timeout })
      .first()
      .click();
    cy.url({ timeout }).should("include", "scan");
  });

  it("Clicking on go back should send you to the status page", () => {
    cy.goBackToStatus(timeout);
  });

  it("Selecting a pax with pax_type null should be treated like an adult", () => {
    cy.get('[data-test="adult"]:not(:disabled)', { timeout })
      .first()
      .click();
    cy.url({ timeout }).should("include", "scan");

    cy.goBackToStatus(timeout);
  });

  it("Selecting a junior pax should send you to the same flow as a child", () => {
    cy.get('[data-test="junior"]:not(:disabled)', { timeout })
      .first()
      .click();
    cy.url({ timeout }).should("include", "child-form");

    cy.goBackToStatus(timeout);
  });

  it("Selecting a child pax should send you to the child-form page", () => {
    cy.get('[data-test="child"]:not(:disabled)', { timeout })
      .first()
      .click();
    cy.url({ timeout }).should("include", "child-form");

    cy.goBackToStatus(timeout);
  });

  it("Selecting a baby pax should send you to the child-form page", () => {
    cy.get('[data-test="baby"]:not(:disabled)', { timeout })
      .first()
      .click();
    cy.url({ timeout }).should("include", "child-form");
  });

  it("If user tries to submit a not adult guest with adult age, error modal should pop up", () => {
    cy.submitOldChild();

    cy.get('[data-test="modalError"]').filter(":visible");

    cy.get('[data-test="closeModal"]')
      .filter(":visible")
      .click();
  });

  it("Submitting correct child should send you to Confirmation", () => {
    cy.get('[data-test="child-birthday-input"]').type("{selectall}{backspace}");

    cy.submitChild();

    cy.url({ timeout }).should("include", "confirmation");
  });
});
