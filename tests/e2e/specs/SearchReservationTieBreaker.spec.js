import {
	notFoundReservation,
	multipleReservations,
} from "../../../mocks/modules/integration/data";
import { localizerResFilterConfig } from "../../../mocks/modules/brand/data";
import { defaultBrand } from "../../../mocks/modules/brand/data";
describe("Search page with multiple options. User will find a reservation submitting last_name and check_in and reservation_code will be used as a filter", () => {
	const timeout = 30000;
	const request = {
		method: "get",
		status: 200,
		endpoint: "*/brands/:id/reservations",
	};

	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			localizerResFilterConfig,
			false,
		);
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);
	});

	after(() => {
		cy.restartServiceWorkerHandlers();
	});

	it("Select reservation_code option. Submit a value that does not match any reservation", () => {
		cy.overrideEndpointResponse(request, notFoundReservation, false);
		cy.get('[data-test="reservation_form"]').eq(1).click();
		cy.get('[data-test="reservation_code"]').type(
			Cypress.env("errorReservationId"),
		);

		cy.get('[data-test="searchButton"]').click();
		cy.wait(500);

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");

		getTranslations("es", "search.reservationNotFound").then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});

		cy.get('[data-test="closeModal"]').filter(":visible").click();
		cy.get('[data-test="reservation_form"]').eq(0).click();
	});

	it("Change to last_name and check_in search option. If more than one reservation is found, check_out and first_name should be requested, does NOT ask again for check_in date", () => {
		cy.overrideEndpointResponse(request, multipleReservations, false);
		cy.get('[data-test="last_name"]').first().clear();
		cy.get('[data-test="last_name"]')
			.first()
			.type(Cypress.env("multipleLocalizersReservationName"));
		cy.get('[data-test="check_in"]').type(
			Cypress.env("multipleLocalizersReservationCheckinDate"),
		);

		cy.get('[data-test="searchButton"]').click();
		cy.wait(500);

		cy.get('[data-test="closeModal"]', { timeout }).should("be.visible");
		cy.get('[data-test="closeModal"]').filter(":visible").click();
		cy.get('[data-test="check_out"]').type(Cypress.env("multipleLocalizersReservationCheckoutDate"),
		);
		cy.get('[data-test="searchButton"]').click();

		cy.get('[data-test="closeModal"]', { timeout }).should("be.visible"); 
	
		cy.get('[data-test="closeModal"]').filter(":visible").click();

		cy.get('[data-test="first_name"]').type("not-existing");
		cy.get('[data-test="searchButton"]').click();

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");
		

		getTranslations("es", "search.reservationNotFound").then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		}); 

		cy.get('[data-test="closeModal"]').filter(":visible").click();

		cy.get('[data-test="first_name"]').clear();
		cy.get('[data-test="first_name"]').type(
			Cypress.env("multipleLocalizersReservationFirstName"),
		);

		cy.get('[data-test="searchButton"]').click();
	
		cy.get('[data-test="closeModal"]', { timeout }).should("be.visible");
		cy.get('[data-test="closeModal"]').filter(":visible").click();
		
	});

	it("Submitting localizer that does not exist should show an error and not allow you to continue.", () => {
		cy.get('[data-test="reservation_code"]').type("not-existing");
		cy.get('[data-test="searchButton"]').click();

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");

		getTranslations("es", "search.reservationNotFound").then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});

		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("Submitting a correct localizer should return the rooms", () => {
		cy.get('[data-test="reservation_code"]').clear();
		cy.get('[data-test="reservation_code"]').type("reserv9");
		cy.get('[data-test="searchButton"]').click();

		cy.url({ timeout }).should("include", "reservations");
	});
});
