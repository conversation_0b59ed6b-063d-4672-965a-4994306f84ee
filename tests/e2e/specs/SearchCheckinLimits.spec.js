import {
	localizerResFilterConfigWithNoTimeLimits,
	localizerResFilterConfigWithTimeLimits,
} from "../../../mocks/modules/brand/data";
import { defaultBrand } from "../../../mocks/modules/brand/data";

describe("Search page with multiple options. User will find a reservation submitting last_name and check_in and reservation_code will be used as a filter", () => {
	it("errorMessage must show if allowTimeLimit is active", () => {
		Cypress.env("TZ", "GMT+2");
		const newCurrentDate = new Date("2023-06-08T12:00:00Z").getTime();
		cy.clock(newCurrentDate);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			localizerResFilterConfigWithTimeLimits,
			false,
		);
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);

		cy.get('[data-test="check_in"]').type("08-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("05-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("10-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("11-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("04-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("10-06-1999");
		cy.wait(500);
		cy.get(".error-message").should("exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("08-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");
	});

	it("errorMessage never may have to show if allow_time_limits is false", () => {
		Cypress.env("TZ", "GMT+2");
		const newCurrentDate = new Date("2023-06-08T12:00:00Z").getTime();
		cy.clock(newCurrentDate);
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			localizerResFilterConfigWithNoTimeLimits,
			false,
		);
		cy.startServiceWorker();
		cy.startFlow(defaultBrand.data.id);

		cy.get('[data-test="check_in"]').type("08-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("05-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("10-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("11-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("04-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("10-06-1999");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");

		cy.get('[data-test="check_in"] input[type="text"]')
			.clear()
			.type("08-06-2023");
		cy.wait(500);
		cy.get(".error-message").should("not.exist");
	});
});
