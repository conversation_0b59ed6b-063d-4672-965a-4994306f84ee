import "cypress-file-upload";
import {
	defaultBrand,
	advancedScanConfig,
} from "../../../mocks/modules/brand/data";
import {
	errorScan,
	passportScan,
	identityCardScan,
} from "../../../mocks/modules/ocr/data";
import {
	foreignSuggestion,
	foreignSelection,
} from "../../../mocks/modules/aws/data";
import {
	defaultReservation,
	multipleRoomsReservation,
	similarReservation,
} from "../../../mocks/modules/integration/data";

describe("Full Process test", () => {
	const cypressEnv = Cypress.env("HL_ENV") || "testing";
	const envConfig = Cypress.env(cypressEnv);
	const timeout = 30000;
	const request = {
		method: "post",
		status: 200,
		endpoint: "*/brands/:id/scan",
	};

	const getTranslations = (locale, translation) =>
		cy.window().its(`app._i18n._vm.messages.${locale}.${translation}`);

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			advancedScanConfig,
			false,
		);

		cy.startServiceWorker();

		cy.startFlow(defaultBrand.data.id);
	});
	after(() => {
		cy.restartServiceWorkerHandlers();
	});

	it("Fill reservation form", () => {
		//Search reservations
		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName"),
		);

		cy.get('[data-test="continueReservation"]', { timeout }).click();
	});

	it("Select reservation", () => {
		//Select reservation
		cy.get('[data-test="guestList"]:not(:disabled)', { timeout }).eq(1).click();
	});

	it("Redirect to advanced scan when click on scan button", () => {
		cy.get('[data-test="fileButton"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
		cy.url({ timeout }).should("include", "advanced-scan");
	});

	it("Uploading an expired document should popup a modal and redirect to status page", () => {
		cy.overrideEndpointResponse(request, errorScan("Document Expired"));
		cy.get('[data-test="makePhotoButton"]', { timeout }).click();

		getTranslations("es", "scan.error.expiredDocument").then((translation) => {
			cy.get('[data-test="modalBody"]').should("contain", translation);
		});
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();

		cy.url({ timeout }).should("include", "status");
	});

	it("Uploading an image that is not a document should not let you continue.", () => {
		cy.overrideEndpointResponse(
			request,
			errorScan("RequireValuesNotFoundError"),
		);
		cy.get('[data-test="adult"]:not(:disabled)', { timeout }).eq(1).click();
		cy.get('[data-test="fileButton"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
		cy.get('[data-test="makePhotoButton"]', { timeout }).click();

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");
		getTranslations("es", "scan.error.noDataRequiredError").then(
			(translation) => {
				cy.get('[data-test="modalBody"]').should("contain", translation);
			},
		);

		cy.get('[data-test="closeModal"]').filter(":visible").click();
	});

	it("User clicks on document image, modal should pop up with", () => {
		cy.get('[data-test="deletePhotoButton"]', { timeout }).click();

		cy.overrideEndpointResponse(request, passportScan);

		cy.get('[data-test="makePhotoButton"]', { timeout }).click();

		cy.wait(1000);
		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "scan");
		cy.wait(1000);

		cy.get('[data-test="showDocumentModal"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
	});

	it("Clicking on 'Delete' button should clear docuemnt", () => {
		cy.get('[data-test="deleteDocument"]', { timeout }).click();
	});

	it("Scan document without doocument type and nationality should redirect to sensible data page", () => {
		identityCardScan.data.document_type = null;
		identityCardScan.data.nationality = null;

		cy.overrideEndpointResponse(request, identityCardScan);
		cy.get('[data-test="fileButton"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
		cy.get('[data-test="makePhotoButton"]', { timeout }).click();

		cy.url({ timeout }).should("include", "sensible-data");
	});

	it("Fill sensible data form with identity card out of Schengen Zone should pop up a modal and redirect to scan page", () => {
		cy.get('[data-test="document_type"]').select("identity_card");
		cy.get('[data-test="nationality"]').type("Rus");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		cy.get('[data-test="sensible-modal-button"]', { timeout }).click();

		cy.get('[data-test="modalError"]', { timeout }).should("be.visible");
		getTranslations("es", "scan.error.schengenZoneError").then(
			(translation) => {
				cy.get('[data-test="modalBody"]').should("contain", translation);
			},
		);

		cy.get('[data-test="closeModal"]').filter(":visible").click();

		cy.url({ timeout }).should("include", "scan");
	});

	it("Fill sensible data correctly should redirect to scan page with the document", () => {
		identityCardScan.data.document_type = null;
		identityCardScan.data.nationality = null;
		cy.overrideEndpointResponse(request, identityCardScan);

		cy.get('[data-test="fileButton"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
		cy.get('[data-test="makePhotoButton"]', { timeout }).click();

		cy.get('[data-test="document_type"]').select("identity_card");
		cy.get('[data-test="nationality"]').type("Esp");
		cy.get('[data-test="nationality-option"]', { timeout }).first().click();

		cy.get('[data-test="sensible-modal-button"]', { timeout }).click();

		cy.url({ timeout }).should("include", "scan");
		cy.get('[data-test="showDocumentModal"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
		cy.get('[data-test="deleteDocument"]', { timeout }).click();
	});

	it("If all documents are uploaded, redirect to validate data page", () => {
		cy.overrideEndpointResponse(request, passportScan);

		cy.get('[data-test="fileButton"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();
		cy.get('[data-test="makePhotoButton"]', { timeout }).click();

		cy.url({ timeout }).should("include", "validate-data");
	});

	it("If guest return to scan page and all documents are uploaded, click on 'Continue' and go next", () => {
		cy.get('[data-test="go-back"]').click();
		cy.url({ timeout }).should("include", "scan");

		cy.get('[data-test="showDocumentModal"]', { timeout }).click();
		cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();

		cy.get('[data-test="continue-button"]', { timeout }).click();
		cy.url({ timeout }).should("include", "validate-data");
	});
});
