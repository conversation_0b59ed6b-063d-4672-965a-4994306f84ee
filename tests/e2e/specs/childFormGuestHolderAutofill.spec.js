import "cypress-file-upload";
import { defaultBrand, childFormConfig } from "../../../mocks/modules/brand/data";

describe("Full Process child with GuestHolder information", () => {
	const timeout = 30000;

	before(() => {
		cy.overrideEndpointResponse(
			{
				status: 200,
				endpoint: "*/brands/:id/products/:id/configuration",
				method: "get",
			},
			childFormConfig,
			false
		);

		cy.startServiceWorker();
	});

	it("starts flow, sets guestHolder, and enters child form", () => {
		cy.startFlow(defaultBrand.data.id);

		cy.fillSearchForm(
			Cypress.env("partialReservationId"),
			Cypress.env("partialReservationName")
		);
		
		cy.get('[data-test="continueReservation"]', { timeout }).click();
		cy.url().should("include", "status");
		cy.get('[data-test="child"]:not(:disabled)', { timeout }).first().click();
	});

	it("fills child form using guestHolder data", () => {
		cy.setGuestHolder({
			email: "<EMAIL>",
			telephone: { value: "666666666", dialCode: "+34", countryCode: "ES" },
			address: {
				street: "Test Street",
				street_number: "22",
				city: "Test City",
				postal_code: "12345",
				country: "ESP",
				CCAA: "07",
				province: "07",
				region: "Islas Baleares",
				subregion: "Mallorca"
			}
		});
		
		cy.reload();
		
		cy.get('[data-test="child-email-input"]', { timeout }).should('exist');

		cy.get('[data-test="child-email-input"]')
			.should('be.visible')
			.and("have.value", "<EMAIL>");
		
		cy.get('[data-test="child-telephone-input"] input')
			.should('be.visible')
			.and("have.value", "666 66 66 66");
		
		cy.get('[data-test="child-address-input"]')
			.should('be.visible')
			.and("have.value", "Test Street");
		
		cy.get('[data-test="child-municipality-input"]')
			.should('be.visible')
			.and("have.value", "Test City");
		
		cy.get('[data-test="child-postal_code-input"]')
			.should('be.visible')
			.and("have.value", "12345");

		cy.get('[data-test*="child-birthday-input"]').type("11112024");

	});

	after(() => {
		cy.restartServiceWorkerHandlers();
	});
});
