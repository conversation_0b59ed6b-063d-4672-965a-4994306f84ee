import "cypress-file-upload";
import { defaultConfig } from "../../../mocks/modules/brand/data";
import { childPassport } from "../../../mocks/modules/ocr/data";
describe("Validate user form", () => {
  const cypressEnv = Cypress.env("HL_ENV") || "testing";
  const envConfig = Cypress.env(cypressEnv);
  const timeout = 60000;
  defaultConfig.data.scan_children_like_adults = true;
  defaultConfig.data.children_sign_documents = true;

  const request = {
    status: 200,
    endpoint: "*/brands/:id/scan",
    method: "post"
  };
  before(() => {
    cy.overrideEndpointResponse(
      {
        status: 200,
        endpoint: "*/brands/:id/products/:id/configuration",
        method: "get"
      },
      defaultConfig,
      false
    );

    cy.startServiceWorker();
    cy.startFlow("child");
  });
  after(() => {
    cy.restartServiceWorkerHandlers();
  });
  it("Fill reservation form", () => {
    //Search reservations
    cy.fillSearchForm(
      Cypress.env("partialReservationId"),
      Cypress.env("partialReservationName")
    );

    cy.get('[data-test="continueReservation"]', { timeout }).click();
  });

  it("Select reservation", () => {
    //Select reservation
    cy.get('[data-test="child"]:not(:disabled)', { timeout })
      .first()
      .click();
  });

  it("Scan document", () => {
    cy.overrideEndpointResponse(request, childPassport);
    cy.uploadFile("childPassport.jpeg");
  });

	it("If all documents are uploaded, redirect to validate data page", () => {
		cy.url({ timeout }).should("include", "validate-data");
	});

  it("Show validate form", () => {
    cy.get('[data-test="document_number"]').clear();
    // Type first number
    cy.get('[data-test="document_number"]').type("1");
    cy.get('[data-test="document_number"]').clear();
    cy.get('[data-test="document_number"]').type("123456789A");
    cy.get('[data-test="address"]').clear();

    cy.get('[data-test="surname"]').clear();
    cy.get('[data-test="surname"]').type("Fake surame");
    cy.get('[data-test="gender"]').select("female");
    cy.get('[data-test="document_number"]').clear();
    cy.get('[data-test="document_number"]').type("123456789A");
    cy.get('[data-test="nationality"]').clear();
    cy.get('[data-test="nationality"]', { timeout }).type("Irlanda");
    cy.get('[data-test="nationality-option"]', { timeout })
      .first()
      .click();
    cy.get('[data-test="address"]').clear();
    cy.get('[data-test="address"]', { timeout }).type("Amiens Street, IRL", {
      delay: 50
    });
    cy.wait(1000);

    cy.get('[data-test="address-option"]', { timeout })
      .first()
      .click();
    cy.wait(1000);
    cy.get('[data-test="postal_code"]', { timeout })
      .invoke("val")
      .should("not.be.empty");

    cy.get('[data-test="validate-data-button"]').click();

    //Redirect from optional route return error, ignore error
    Cypress.on("uncaught:exception", () => {
      return false;
    });

    if (cy.get('[data-test="modalError', { timeout }).should("be.visible")) {
      cy.get('[data-test="accept-differences-modal"]', { timeout })
        .filter(":visible")
        .click();
    }

    cy.url({ timeout }).should("include", "documents");
  });

  it("FinishFlowAfterCompleteGuestData", () => {
    cy.readDocuments(envConfig.documents, timeout);
  });
});
