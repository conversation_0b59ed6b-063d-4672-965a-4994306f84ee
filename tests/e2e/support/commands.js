// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
import { worker, rest } from "../../../mocks/testBrowser";
const cypressEnv = Cypress.env("HL_ENV") || "testing";
const envConfig = Cypress.env(cypressEnv);
import Amplify, { Auth } from "aws-amplify";
import APIConfiguration from "../../../src/AWS/api-amplify-configuration";
import GeoConfiguration from "../../../src/AWS/geo-amplify-configuration";
Cypress.Commands.add("startFlow", (processMode, startOnGdpr = true) => {
	const selectBrandId = (processMode) => {
		switch (processMode) {
			case "optional":
				return envConfig.optionalBrandId;
			case "child":
				return envConfig.childBrandId;
			default:
				/*
          If you pass a number it means that you want to force a specific brand, so we return it 
          (for example, when mocking an id to test the brand at chain level).
        */
				if (typeof processMode === "number") {
					return processMode;
				}

				return envConfig.brandId;
		}
	};

	const brandId = selectBrandId(processMode);

	cy.viewport("iphone-6+");
	cy.clearLocalStorage();

	cy.visit(`/${brandId}`, {
		onBeforeLoad(win) {
			Object.defineProperty(win.navigator, "language", { value: "es-ES" });
			Object.defineProperty(win.navigator, "languages", { value: ["es"] });
			Object.defineProperty(win.navigator, "accept_languages", {
				value: ["es"],
			});
		},
		headers: {
			"Accept-Language": "es",
		},
	});

	if (startOnGdpr) {
		cy.expect('[data-test="gdprText"]', { timeout: 60000 }).not.to.be.empty;

		cy.get('[data-test="acceptConditionsButton"]', {
			timeout: 60000,
		}).click();
		cy.url({ timeout: 60000 }).should("include", "search");
	}
});

const LOCAL_STORAGE_MEMORY = {};

Cypress.Commands.add("saveLocalStorage", async () => {
	await Object.keys(localStorage).forEach((key) => {
		LOCAL_STORAGE_MEMORY[key] = localStorage[key];
	});
});

Cypress.Commands.add("restoreLocalStorage", async () => {
	await Object.keys(LOCAL_STORAGE_MEMORY).forEach((key) => {
		localStorage.setItem(key, LOCAL_STORAGE_MEMORY[key]);
	});
});
Cypress.Commands.add("loginByCognito", (username, password) => {
	return loginToCognito(username, password);
});

Cypress.Commands.add("logMessage", (message) => {
	Cypress.log({
		message: `💢 ${message}`,
	});
});

const loginToCognito = (username, password) => {
	const amplifyConfig = {
		Auth: envConfig.awsAuthConfig,
		API: APIConfiguration,
		geo: GeoConfiguration,
	};
	Amplify.configure(amplifyConfig);
	const signIn = Auth.signIn({ username, password });
	Cypress.log({
		displayName: "COGNITO LOGIN",
		message: [`🔐 Authenticating | ${username}`],
		autoEnd: true,
	});

	cy.wrap(signIn).then((cognitoResponse) => {
		const keyPrefixWithUsername = `${cognitoResponse.keyPrefix}.${cognitoResponse.username}`;

		window.localStorage.setItem(
			`${keyPrefixWithUsername}.idToken`,
			cognitoResponse.signInUserSession.idToken.jwtToken,
		);

		window.localStorage.setItem(
			`${keyPrefixWithUsername}.accessToken`,
			cognitoResponse.signInUserSession.accessToken.jwtToken,
		);

		window.localStorage.setItem(
			`${keyPrefixWithUsername}.refreshToken`,
			cognitoResponse.signInUserSession.refreshToken.token,
		);

		window.localStorage.setItem(
			`${keyPrefixWithUsername}.clockDrift`,
			cognitoResponse.signInUserSession.clockDrift,
		);

		window.localStorage.setItem(
			`${cognitoResponse.keyPrefix}.LastAuthUser`,
			cognitoResponse.username,
		);

		const authorizedBrandsResponse =
			cognitoResponse.attributes["custom:brand_id"]?.split(",");

		//We get autocheckin storage on JSON format in order to modify it
		const authorizedBrandsStore = JSON.parse(
			window.localStorage.getItem("hlCheckin"),
		);
		//we introduce cognito authorized brands into the store variable
		authorizedBrandsStore.app.authorizedBrands = authorizedBrandsResponse;
		//New Store contains authorized brands
		const newStore = JSON.stringify(authorizedBrandsStore);
		window.localStorage.setItem("hlCheckin", newStore);

		window.localStorage.setItem("amplify-authenticator-authState", "signedIn");

		cy.visit(`/${envConfig.brandId}`);
		cy.wait(3000);
	});
};

Cypress.Commands.add("startReceptionFlow", () => {
	cy.viewport("iphone-6+");
	cy.clearLocalStorage();
	cy.visit("/login", {
		onBeforeLoad(win) {
			Object.defineProperty(win.navigator, "language", { value: "es-ES" });
			Object.defineProperty(win.navigator, "languages", { value: ["es"] });
			Object.defineProperty(win.navigator, "accept_languages", {
				value: ["es"],
			});
		},
		headers: {
			"Accept-Language": "es",
		},
	});
});

Cypress.Commands.add("fillSearchForm", (reservationId, surname) => {
	cy.get('[data-test="reservation_code"]').clear();
	cy.get('[data-test="reservation_code"]').type(reservationId);
	cy.get('[data-test="last_name"]').clear();
	cy.get('[data-test="last_name"]').type(surname);
	cy.get('[data-test="searchButton"]').click();
});

Cypress.Commands.add("readDocuments", (documents, timeout) => {
	cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();

	cy.get('[data-test="document-content"]')
		.contains("{{documentCount}}")
		.should("not.be.visible");

	cy.get('[data-test="documents-index"]')
		.should("contain", "1/2")
		.filter(":visible");

	cy.get('[data-test="close-document-button"]')
		.should("contain", "Leído, siguiente")
		.filter(":visible")
		.click();

	cy.get('[data-test="document-content"]')
		.contains("{{documentCount}}")
		.should("not.be.visible");

	cy.get('[data-test="documents-index"]')
		.should("contain", "2/2")
		.filter(":visible");

	cy.get('[data-test="close-document-button"]')
		.should("contain", "Leído, firmar")
		.filter(":visible")
		.click();

	cy.url({ timeout }).should("include", "signature");
});

Cypress.Commands.add("signsDocuments", (timeout) => {
	cy.get("canvas")
		.click()
		.trigger("mousedown", { which: 1, x: 50, y: 50 })
		.trigger("mousemove", { which: 1, x: 50, y: 60 })
		.trigger("mouseup");
	cy.get('[data-test="continue-button"]').click();
	cy.url({ timeout }).should("include", "send-documents");

	cy.get('[data-test="continue-button"]').click();
});
Cypress.Commands.add("bypassToConfirmation", (timeout) => {
	//bypass phone form
	cy.url({ timeout }).should("include", "phone-form");
	cy.get('[data-test="continue-button"]').click();

	//bypass comments
	cy.url({ timeout }).should("include", "comments");
	cy.get('[data-test="continue-button"]').click();
	cy.url({ timeout }).should("include", "confirmation");
});

Cypress.Commands.add("bypassToPayments", (timeout) => {
	//bypass phone form
	cy.url({ timeout }).should("include", "phone-form");
	cy.get('[data-test="continue-button"]').click();

	cy.url({ timeout }).should("include", "payment");
});

/**
 * Command to wrap endpoint override,
 *
 * @params request {method, enpoint, status}
 * @params response
 */
Cypress.Commands.add(
	"overrideEndpointResponse",
	(request, response, once = true) => {
		cy.window().then(() => {
			worker.use(
				rest[request.method](request.endpoint, (req, res, ctx) => {
					// Ternary to check if only needs to be override once
					return once
						? res.once(ctx.status(request.status), ctx.json(response))
						: res(ctx.status(request.status), ctx.json(response));
				}),
			);
		});
	},
);
Cypress.Commands.add("startServiceWorker", () => {
	cy.window().then(() => {
		worker.start();
		cy.wait(3000);
		console.log("start worker!");
	});
});

Cypress.Commands.add("restartServiceWorkerHandlers", () => {
	cy.window().then(() => {
		worker.resetHandlers();
		console.log("stop worker!");
	});
});

Cypress.Commands.add("uploadFile", (file, timeout) => {
	cy.get('[data-test="uploadDocument"]', { timeout }).click();

	cy.get('[data-test="closeModal"]', { timeout }).filter(":visible").click();

	cy.get('[data-test="fileInput"]').attachFile(file);
	cy.get('[data-test="fileInput"]').trigger("input", { force: true });
});

Cypress.Commands.add("doManualProcess", (timeout = 60000) => {
	//Click button to complete manual information
	cy.get('[data-test="manualProcess-button"]', {
		timeout,
	}).click();

	//Close modal and go to validate data
	cy.get('[data-test="goToCompleteDataManually"]', { timeout })
		.filter(":visible")
		.click();
});

Cypress.Commands.add("goBackToStatus", (timeout = 60000) => {
	cy.get('[data-test="go-back"]', { timeout }).first().click();
	cy.url({ timeout }).should("include", "status");
});

Cypress.Commands.add("submitOldChild", () => {
	cy.get('[data-test="child-data-submit"]:disabled');

	cy.get('[data-test="child-birthday-input"]').type("01-01-1990");

	cy.get('[data-test="child-data-submit"]').first().click();
});

Cypress.Commands.add("submitChild", () => {
	cy.get('[data-test="child-data-submit"]:disabled');

	cy.get('[data-test="child-birthday-input"]').type("01-01-2006");

	cy.get('[data-test="child-data-submit"]').first().click();
});

Cypress.Commands.add("setGuestHolder", (guestData = {}) => {
	cy.window().then((win) => {
		const existingGuest = {
			uuid: "holder-uuid",
			name: "GuestHolder",
			selected: false,
			pax_type: "AD",
			validated: false,
			processCompleted: false,
			holder: true,
			...guestData,
		};

		win.app.$store.commit("guest/setGuestToList", [existingGuest], false);
	});
});