---
title: Autocheckin Demo
layout: layout.html
eleventyNavigation:
  key: Autocheckin Demo
  order: 3
---

# Autocheckin Demo

## How to enable

In order to enable a demo version of Autocheckin, you need to modify `VUE_APP_BRANDS_DEMO={BRAND_ID}` in `.env` file.

- Make sure that you have enabled the Autocheckin product for at least one brand in the Hotelinking App's private.

![Auto private config](/src/assets/images/docs/autocheckin-demo/private-aci-config.png)

- Replace `BRAND_ID`, with the id that you need. Value must be a number. You can set multiple brands splitting with `,` without spaces.
  - Valid values: 
    - `"1"`
    - `"1,2,3"`
    - `1`
    - `1,2,3`
  - Not valid: 
    - `"1, 2, 3"`
    - `["1", "2", "3"]`
    - `[1, 2, 3]`

- Save the file and run `npm run serve`.

- Go to browser and look for `http://localhost:8080/{yourDemoBrandID}`

## Enabling Autocheckin-Demo Diagram

```mermaid
sequenceDiagram
  participant Autocheckin
  participant Node Process
  participant MSW
  critical Start node server
    Autocheckin-->Node Process: Set environment data
  end
  Autocheckin->>Node Process: User introduces {brand_id} in url
  alt brand_id does not match with a demo brand
    Node Process->>Autocheckin: Default autocheckin
  else brand_id matches with a demo brand
    Node Process->>MSW: Require handlers from MSW
    MSW->>Autocheckin: Enable mocks for demo autocheckin
  end
  Note left of Autocheckin: Continue autocheckin flow
```

## Mocked endpoints

- Search reservation
  - **GET** `{baseUrl}/integrations/brands/:brand_id/reservations?{queryStringParams}`
  - Returns a **200 response** with an overriden mocked demo reservation with specified data in the queryStringParams (reservation_code and last_name)
  - Check-in / check-out days are always 3 days ahead today.
<br/>

- Pre check of guest
  - **POST** `{baseUrl}/integrations/brands/:brand_id/reservations/pre-check`
  - Returns a **204 response**. It updates the guestList store with the guest in the payload, so it looks like the guest did validate