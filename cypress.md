---
title: Cypress
layout: layout.html
eleventyNavigation:
  key: Cypress
---

```sh
Current port: 8080
```


```sh
npm run test:e2e
```


There are two modes on autocheckin running for tests:

<b>Normal mode: </b>
In order to run normal mode tests you must have disabled VUE_APP_RECEPTION_HOST

like: `# VUE_APP_RECEPTION_HOST=localhost:8080`

<b>Reception mode:</b>
In order to run reception mode tests you must have disabled VUE_APP_RECEPTION_HOST

like:`VUE_APP_RECEPTION_HOST=localhost:8080`

`You can't run both at the same time right now, if you have reception mode enabled other ones won't work`

`Nowadays reception mode only activates when VUE_APP_RECEPTION_HOST and our current host(local) are equal. If you already have that port in use you can change cypress.json, package.json and .env.test in order to make it run:`
- On cypress.json swap baseUrl to the port you want to use
- On .env.test swap VUE_APP_RECEPTION_HOST to the port you want to use
- On package.json add your new port like this: `"test:e2e": "vue-cli-service test:e2e --mode test --port 3000"`

---
Aditional info:
Into reception mode test there's a real api request, cognito login on first page. This one changes dynamically on pipeline but on local you'll have to set it manually.
In order to do that you'll have to change test:e2e script variables `"test:e2e": "CYPRESS_HL_ENV=testing vue-cli-service test:e2e --mode test",`
- `CYPRESS_HL_ENV` will set which Cypress variables is the test getting, those ones are set on cypress.json
  - You can choose between 'testing/dev', 'beta', 'prod'
  - Every stack will redirect to it's brand when login completes successfully
- `--mode` will choose which .env will be on use.
  - You can choose between 'test(default)','dev','beta','production'

`This is necessary only when you want to run code tests from other stacks, pointing to that specific stack.`