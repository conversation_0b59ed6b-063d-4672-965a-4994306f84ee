
image: node:14.15.4
options:
  max-time: 15

definitions:
  caches:
    npm: $HOME/.npm
    cypress: $HOME/.cache/Cypress

  steps:
    - step: &deploy
        name: Deploy
        deployment: test
        oidc: true
        script:
          - pipe: atlassian/aws-s3-deploy:2.0.0
            variables:
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              S3_BUCKET: $S3_STATIC_BUCKET
              LOCAL_PATH: "dist"
              DELETE_FLAG: "true"
          - pipe: atlassian/aws-cloudfront-invalidate:0.10.1
            variables:
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              DISTRIBUTION_ID: $DISTRIBUTION_ID
    - step: &install
        name: "Install"
        image: cypress/base:14
        caches:
          - npm
          - node
          - cypress
        script:
          - npm install
          - export BRANCH=$(git branch | grep \* | cut -d ' ' -f2)
          - >
            if [ "$BRANCH" == "master" ]; then
              BUILD_ENV="prod"
            elif [ "$BRANCH" == "beta" ]; then
              BUILD_ENV="beta"
            else
              BUILD_ENV="dev"
            fi
          - echo "$BUILD_ENV" > ./build.txt
        artifacts:
          - build.txt
 
  e2eTests: &testsAndBuildParallel
    - parallel:
      - step:
          name: AllowExpiredDocumentScan E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/AllowExpiredDocumentScan.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: ChainCheckin E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/ChainCheckin.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: CompleteBookingSearchFinish E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/CompleteBookingSearchFinish.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: DifferencesModal E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/DifferencesModal.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: fullProcess E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/fullProcess.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: fullProcessChild E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/fullProcessChild.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: childFormGuestHolderAutofill E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/childFormGuestHolderAutofill.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: fullProcessReception E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          variables:
            VUE_APP_RECEPTION_HOST: "localhost:3000"
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - VUE_APP_RECEPTION_HOST=localhost:3000 npm run serve:$BUILD_ENV:reception & node_modules/wait-on/bin/wait-on http://localhost:3000
            - CYPRESS_HL_ENV=$BUILD_ENV npx cypress run --config baseUrl=http://localhost:3000/ --spec "**/fullProcessReception.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: fullProcessEnglish E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/fullProcessUSDateFormat.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: LoadLanguageAutomatically E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/LoadLanguageAutomatically.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: SearchReservationIncomplete E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/SearchReservationIncomplete.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: Demo Mode E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/DemoMode.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: ManyPaxTypesFlow E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/ManyPaxTypesFlow.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: OptionalScanFullProcess E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/OptionalScanFullProcess.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: ScanAskSensibleData E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/ScanAskSensibleData.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: PaymentProcess E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/PaymentProcess.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: SearchReservationTieBreaker E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/SearchReservationTieBreaker.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: SearchCheckinLimits E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - |
              NEW_DATE=$(node -e "new Date('2023-06-08T00:00:00Z').getTime()")
              CYPRESS_HL_ENV=testing CYPRESS_NEW_DATE=$NEW_DATE npx cypress run --spec "**/SearchCheckinLimits.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: AdvancedScan E2E test
          caches:
            - npm
            - node
            - cypress
          image: cypress/base:14
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run serve:$BUILD_ENV & node_modules/wait-on/bin/wait-on http://localhost:8080
            - CYPRESS_HL_ENV=testing npx cypress run --spec "**/advancedScan.spec.js"
          artifacts:
            - tests/e2e/videos/**
      - step:
          name: Unit tests
          caches:
            - npm
            - node
          script:
            - npm run test:unit -- --silent
          artifacts:
            - tests/unit/**
      - step:
          name: Build
          caches:
            - npm
            - node
          script:
            - BUILD_ENV=$(cat ./build.txt)
            - npm run build:$BUILD_ENV
          artifacts:
            - dist/**

pipelines:
  pull-requests:
    "**":
      - step:
          <<: *install
      - <<: *testsAndBuildParallel
      - step:
          name: Deploy to preview and create PR comment
          deployment: test
          oidc: true
          script:
            - pipe: atlassian/aws-s3-deploy:2.0.0
              variables:
                AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $S3_PREVIEWS_BUCKET/$BITBUCKET_REPO_SLUG-$BITBUCKET_PR_ID
                LOCAL_PATH: dist
                DELETE_FLAG: "true"
            - curl -X POST -H "Content-Type:application/json" "https://${BITBUCKET_USERNAME}:${BB_APP_PASSWORD}@api.bitbucket.org/2.0/repositories/${BITBUCKET_WORKSPACE}/${BITBUCKET_REPO_SLUG}/pullrequests/${BITBUCKET_PR_ID}/comments/" -d "{\"content\":{\"raw\":\"[Check the preview build!](https://${BITBUCKET_REPO_SLUG}-${BITBUCKET_PR_ID}.hlkpreviews.com/76)\"}}"
            - pipe: atlassian/aws-cloudfront-invalidate:0.10.1
              variables:
                AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                DISTRIBUTION_ID: $PREVIEWS_DISTRIBUTION_ID
                PATHS: "/${BITBUCKET_REPO_SLUG}-${BITBUCKET_PR_ID}"
                
  branches:
    develop:
      - step:
          <<: *install
      - <<: *testsAndBuildParallel
      - step:
          <<: *deploy
          name: Deploying to dev

    master:
      - step:
          <<: *install
      - <<: *testsAndBuildParallel
      - step:
          <<: *deploy
          name: Deploying to production
          deployment: production
