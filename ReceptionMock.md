---
title: Reception Mock
layout: layout.html
eleventyNavigation:
  key: Reception Mock
  order: 1
---


# Reception Mock

## How to enable mock on reception

First of all you'll have to enable reception mode on your current .env stack, {local, dev, beta, prod}

You can do it by:
`VUE_APP_MOCK=false`
ex: `VUE_APP_MOCK=true`

/or/

`VUE_APP_BRANDS_DEMO=""`
ex: `VUE_APP_BRANDS_DEMO="1,2,3,4,5"`

`These two ways of mocking have different behaviour, check autocheckin-mock.md before.`

`VUE_APP_RECEPTION_HOST=localhost:3000`

First disclaimer:
As you can see in https://hotelinking.atlassian.net/wiki/spaces/Autocheckin/pages/2622062826/Autocheckin+de+recepci+n reception mode has to be set on true, on "reception signature property", that means you'll have to enable it on the mock configuration response to make it work, also reception mode has a cognito user that allows some brands to access that mode, you'll have to follow before link steps to create a new user or use an existing one; these users are attached with brands to use this mode, so gonna have to configure which brands you are going to use aswell.

Futhermore if you using all api request mocks remember to change brand_id responses to the one you are using, some of them needs that brand id for looking for the data.

`if you activate use VUE_APP_BRANDS_DEMO instead of VUE_APP_MOCKS you'll be using demoBrowser.js instead of testHandlers.js`

For example, you want to use local brand id:1 as a mock.
- Change all brand_id that MSW is using to brand_id 1 on every mocked response by testHandlers.js repository. 
- Change your .env to VUE_APP_MOCK=true(to mock all brands) or VUE_APP_BRANDS_DEMO="1"(to mock only brand 1)
- Change VUE_APP_RECEPTION_HOST=localhost:3000
- Open your browser on localhost:3000 with cache and storage clean
- This will open a login interface
  - Create an cognito user on amazon following before link steps or use an existing one and check which brands have permission to acceed reception mode.
