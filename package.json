{"name": "vue-checkin-new", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve:reception": "vue-cli-service serve --port 3000", "build": "vue-cli-service build --report --skip-plugins pwa,workbox", "test:unit": "vue-cli-service test:unit --silent", "test:e2e": "CYPRESS_HL_ENV=testing vue-cli-service test:e2e --mode test", "lint:rome": "rome check ./src ./tests --verbose", "lint:eslint": "eslint --ext .vue --ignore-path .gitignore --fix src", "lint": "npm run lint:rome && npm run lint:eslint", "build:beta": "vue-cli-service build --modern --mode beta --skip-plugins pwa,workbox", "build:dev": "vue-cli-service build --modern --mode dev --skip-plugins pwa,workbox", "build:prod": "vue-cli-service build --modern --css extract --skip-plugins pwa,workbox", "serve:beta": "vue-cli-service serve --mode beta", "serve:dev": "vue-cli-service serve --mode dev", "serve:dev:reception": "vue-cli-service serve --mode dev --port 3000", "serve:beta:reception": "vue-cli-service serve --mode beta --port 3000", "serve:prod": "vue-cli-service serve --mode production", "serve:prod:reception": "vue-cli-service serve --mode production --port 3000", "prepare": "husky install", "docs:build": "npx rimraf docs && npx eleventy", "docs:serve": "npx eleventy --serve"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["rome format --write"]}, "dependencies": {"@aws-amplify/api": "^4.0.57", "@aws-amplify/ui-components": "^1.9.40", "@fingerprintjs/fingerprintjs": "^3.2.0", "@splidejs/splide": "^2.4.21", "@splidejs/vue-splide": "^0.3.5", "autoprefixer": "^9.8.8", "aws-amplify": "^4.3.14", "aws-sdk": "^2.868.0", "browser-image-compression": "^1.0.17", "canvg": "^3.0.7", "date-fns-tz": "^2.0.0", "flush-promises": "^1.0.2", "handlebars": "^4.7.8", "jspdf": "^2.3.0", "lodash": "^4.17.20", "moment": "^2.27.0", "moment-timezone": "^0.5.34", "postcode-validator": "^3.7.0", "postcss": "^7.0.35", "qrcode.vue": "^1.7.0", "stacktrace-js": "^2.0.2", "string-similarity": "^4.0.2", "trim-canvas": "^0.1.2", "vue": "^2.6.11", "vue-clickaway": "^2.2.2", "vue-i18n": "^8.22.4", "vue-router": "^3.2.0", "vue-signature-pad": "^2.0.2", "vue-tel-input": "^5.11.0", "vue2-animate": "^2.1.4", "vue2-datepicker": "^3.9.0", "vuelidate": "^0.7.7", "vuex": "^3.4.0", "vuex-persist-indexeddb": "^0.1.3", "vuex-persistedstate": "^4.0.0-beta.3"}, "devDependencies": {"@11ty/eleventy": "^1.0.2", "@11ty/eleventy-navigation": "^0.3.5", "@11ty/eleventy-plugin-syntaxhighlight": "^4.1.0", "@babel/plugin-proposal-optional-chaining": "^7.14.2", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "^5.11.1", "@kevingimbel/eleventy-plugin-mermaid": "^2.0.0", "@testing-library/jest-dom": "^5.16.2", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-e2e-cypress": "~4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-unit-jest": "^4.5.15", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "^4.1.1", "@vue/test-utils": "^1.2.1", "babel-eslint": "^10.1.0", "babel-plugin-transform-imports": "^2.0.0", "cypress-file-upload": "^5.0.6", "eslint": "^6.7.2", "eslint-plugin-jest": "^26.1.3", "eslint-plugin-rome": "0.0.0", "eslint-plugin-vue": "^6.2.2", "flush-promises": "^1.0.2", "husky": "^7.0.4", "jest-canvas-mock": "^2.5.2", "jest-transform-stub": "^2.0.0", "markdown-it-anchor": "^8.6.5", "moment-locales-webpack-plugin": "^1.2.0", "msw": "^0.39.2", "node-sass": "^4.12.0", "rome": "12.1.2", "sass-loader": "^8.0.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.4", "url-loader": "^4.1.1", "vue-jest": "^3.0.7", "vue-svg-loader": "^0.16.0", "vue-template-compiler": "^2.6.11", "wait-on": "^5.3.0"}, "msw": {"workerDirectory": "public"}}