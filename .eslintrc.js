module.exports = {
	root: true,
	env: {
		node: true,
	},
	extends: ["plugin:vue/essential", "eslint:recommended"],
	parserOptions: {
		parser: "babel-eslint",
	},
	rules: {
		// "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
		"no-console": "off",
		"no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
	},
	// this adds the jest config for eslint only on files in unit tests
	overrides: [
		Object.assign(
			{
				files: ["**/tests/unit/**/*.spec.js"],
				env: { jest: true, "jest/globals": true },
				plugins: ["jest"],
			},
			require("eslint-plugin-jest").configs.recommended,
		),
	],
};
