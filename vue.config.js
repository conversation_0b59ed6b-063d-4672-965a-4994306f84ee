const fs = require("fs");
const path = require("path");
const MomentLocalesPlugin = require("moment-locales-webpack-plugin");
// const BundleAnalyzerPlugin =
// 	require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

module.exports = {
	lintOnSave: true,
	pluginOptions: {
		s3Deploy: {
			registry: undefined,
			awsProfile: process.env.VUE_APP_S3D_AWS_PROFILE,
			region: process.env.VUE_APP_REGION,
			bucket: process.env.VUE_APP_S3D_BUCKET,
			createBucket: false,
			staticHosting: false,
			assetPath: "dist",
			assetMatch: "**",
			deployPath: "/autocheckin/",
			acl: "public-read",
			pwa: false,
			enableCloudfront: true,
			cloudfrontId: process.env.VUE_APP_S3D_CLOUDFRONT_ID,
			cloudfrontMatchers: "/*",
			uploadConcurrency: 5,
			pluginVersion: "3.0.0",
			gzip: true,
		},
	},
	productionSourceMap: false,
	chainWebpack: (config) => {
		const svgRule = config.module.rule("svg");
		svgRule.uses.clear();

		svgRule
			.oneOf("custom")
			.test(/\.svg$/)
			.exclude.add(/node_modules/)
			.end()
			.use("vue-svg-loader")
			.loader("vue-svg-loader")
			.end();

		svgRule
			.oneOf("packages")
			.test(/\.svg$/)
			.include.add(path.resolve(__dirname, "node_modules"))
			.end()
			.use("url-loader")
			.loader("url-loader")
			.end();
	},
	configureWebpack: (config) => {
		if (process.env.HTTPS_MODE === "true") {
			config.devServer = {
				host: "0.0.0.0",
				https: {
					port: 8085,
					https: true,
					hotOnly: false,
					key: fs.readFileSync("./certs/localhost.key"),
					cert: fs.readFileSync("./certs/localhost.crt"),
					ca: fs.readFileSync("./certs/rootCA.crt"),
				},
			};
		}

		config.plugins = [
			...config.plugins,
			new MomentLocalesPlugin(),
			// new BundleAnalyzerPlugin(),
		];
	},
};
