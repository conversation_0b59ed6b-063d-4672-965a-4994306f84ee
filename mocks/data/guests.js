export const defaultGuestList = [
  {
    pms_id: "HUES-MOCK",
    validated: true,
    pax_type: "AD",
    first_name: "<PERSON><PERSON>",
    last_name: "<PERSON><PERSON><PERSON>",
    email: null,
    gender: "male",
    birthday: "1986-07-17",
    nationality: "34__",
    document_id: "MOCK_DNI",
    address: "Mock_Address",
    city: "Mock_City",
    province: null,
    postal_code: "",
    telephone: "",
    birth_country: "34__",
    residence_country: "España"
  },
  {
    pms_id: null,
    validated: false,
    pax_type: "AD",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null
  },
  {
    pms_id: null,
    validated: false,
    pax_type: "AD",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null
  },
  {
    pms_id: null,
    validated: false,
    pax_type: "AD",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null
  },
  {
    pms_id: null,
    validated: false,
    pax_type: "CH",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null
  }
];

export const unorderedGuestList = [
  {
    pms_id: "HUES-744365",
    position: null,
    validated: false,
    pax_type: "AD",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: null,
    holder: true
  },
  {
    pms_id: null,
    position: null,
    validated: false,
    pax_type: "CH",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: null,
    holder: false
  },
  {
    pms_id: null,
    position: null,
    validated: false,
    pax_type: "CH",
    first_name: "niño",
    last_name: "niño",
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: "niño niño",
    holder: false
  },
  {
    pms_id: "HUES-744349",
    position: null,
    validated: true,
    pax_type: "AD",
    email: null,
    gender: "female",
    birthday: "2003-02-11",
    nationality: "ESP",
    document_id: "safdsadsa",
    address: "Carrer Miquel Forteza I Pinya Illes Balears",
    city: "Illes Balears",
    province: null,
    postal_code: "07007",
    telephone: "",
    birth_country: "ESP",
    residence_country: "España",
    processCompleted: true,
    name: "Test",
    surname: "Fsadsa",
    full_name: "Test Fsadsa",
    holder: false
  },
  {
    pms_id: "HUES-744365",
    position: null,
    validated: false,
    pax_type: "AD",
    first_name: "Pepe",
    last_name: "Viyuela",
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: "Pepe Viyuela",
    holder: false
  },
  {
    pms_id: "HUES-744366",
    position: null,
    validated: true,
    pax_type: "JR",
    first_name: "Fortunio",
    last_name: "Morales",
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: true,
    full_name: "Fortunio Morales",
    holder: false
  }
];

export const orderedGuestList = [
  {
    pms_id: "HUES-744365",
    position: null,
    validated: false,
    pax_type: "AD",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: null,
    holder: true
  },
  {
    pms_id: "HUES-744349",
    position: null,
    validated: true,
    pax_type: "AD",
    email: null,
    gender: "female",
    birthday: "2003-02-11",
    nationality: "ESP",
    document_id: "safdsadsa",
    address: "Carrer Miquel Forteza I Pinya Illes Balears",
    city: "Illes Balears",
    province: null,
    postal_code: "07007",
    telephone: "",
    birth_country: "ESP",
    residence_country: "España",
    processCompleted: true,
    name: "Test",
    surname: "Fsadsa",
    full_name: "Test Fsadsa",
    holder: false
  },
  {
    pms_id: "HUES-744366",
    position: null,
    validated: true,
    pax_type: "JR",
    first_name: "Fortunio",
    last_name: "Morales",
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: true,
    full_name: "Fortunio Morales",
    holder: false
  },
  {
    pms_id: "HUES-744365",
    position: null,
    validated: false,
    pax_type: "AD",
    first_name: "Pepe",
    last_name: "Viyuela",
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: "Pepe Viyuela",
    holder: false
  },
  {
    pms_id: null,
    position: null,
    validated: false,
    pax_type: "CH",
    first_name: "niño",
    last_name: "niño",
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: "niño niño",
    holder: false
  },
  {
    pms_id: null,
    position: null,
    validated: false,
    pax_type: "CH",
    first_name: null,
    last_name: null,
    email: null,
    gender: null,
    birthday: null,
    nationality: null,
    document_id: null,
    address: null,
    city: null,
    province: null,
    postal_code: null,
    telephone: null,
    birth_country: null,
    residence_country: null,
    processCompleted: false,
    full_name: null,
    holder: false
  }
];
