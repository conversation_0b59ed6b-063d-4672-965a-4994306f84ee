export default {
  data: {
    active: true,
    identification: {
      reservation_inputs: [
        [
          {
            name: "booking",
            type: "integer"
          },
          {
            name: "surname",
            type: "string"
          }
        ],
        [
          {
            name: "email",
            type: "string"
          }
        ]
      ],
      reservation_filters: [
        [
          {
            name: "pms_boarding_date",
            type: "date"
          }
        ]
      ]
    },
    max_attempts_reservation: 10,
    child_required_identity_documents_age: 12,
    max_attempts_child: 3,
    max_attempts_document: 3,
    partial_checkin: false,
    room_type_selection: true,
    telephone: true,
    telephone_notifications: false,
    comments: false,
    signed_documents: true,
    // this will probably change
    validate_data_scan: [
      {
        name: "name",
        type: "string"
      },
      {
        name: "birthday_date",
        type: "date"
      },
      {
        name: "nationality",
        type: "string"
      },
      {
        name: "address",
        type: "string"
      },
      {
        name: "postal_code",
        type: "string"
      }
    ]
  }
};
