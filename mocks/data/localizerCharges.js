export default {
  data: {
    localizer: "XIS003",
    reservations: [
      {
        res_id: "**********",
        invoices: [
          {
            id: "PROF-179601",
            guest_id: "HUES-742420",
            name: "CARLOS FANTI",
            total: 661,
            received: 270,
            remain: 391,
            taxes: 60.09,
            currency: "EUR",
            products: [
              {
                start_date: "2020-10-30T00:00:00+01:00",
                end_date: "2020-11-03T00:00:00+01:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 10,
                total: 270,
                taxes: 24.55
              },
              {
                start_date: "2021-07-05T00:00:00+02:00",
                end_date: "2021-07-11T00:00:00+02:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 14,
                total: 378,
                taxes: 34.36
              },
              {
                start_date: "2021-07-06T00:00:00+02:00",
                end_date: "2021-07-06T00:00:00+02:00",
                product_code: "0299",
                description: "VARIOS",
                currency: "EUR",
                quantity: 1,
                total: 13,
                taxes: 1.18
              }
            ],
            transactions: [
              {
                date: "2021-07-05T00:00:00+02:00",
                amount: 10,
                currency: null
              },
              {
                date: "2021-07-06T00:00:00+02:00",
                amount: 260,
                currency: null
              }
            ]
          },
          {
            id: "PROF-179602",
            guest_id: "HUES-742420",
            name: "CARLOS FANTI",
            total: 661,
            received: 270,
            remain: 391,
            taxes: 60.09,
            currency: "EUR",
            products: [
              {
                start_date: "2020-10-30T00:00:00+01:00",
                end_date: "2020-11-03T00:00:00+01:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 10,
                total: 270,
                taxes: 24.55
              },
              {
                start_date: "2021-07-05T00:00:00+02:00",
                end_date: "2021-07-11T00:00:00+02:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 14,
                total: 378,
                taxes: 34.36
              },
              {
                start_date: "2021-07-06T00:00:00+02:00",
                end_date: "2021-07-06T00:00:00+02:00",
                product_code: "0299",
                description: "VARIOS",
                currency: "EUR",
                quantity: 1,
                total: 13,
                taxes: 1.18
              }
            ],
            transactions: [
              {
                date: "2021-07-05T00:00:00+02:00",
                amount: 10,
                currency: null
              },
              {
                date: "2021-07-06T00:00:00+02:00",
                amount: 260,
                currency: null
              }
            ]
          }
        ]
      },
      {
        res_id: "**********",
        invoices: [
          {
            id: "PROF-179603",
            guest_id: "HUES-742420",
            name: "CARLOS FANTI",
            total: 661,
            received: 270,
            remain: 391,
            taxes: 60.09,
            currency: "EUR",
            products: [
              {
                start_date: "2020-10-30T00:00:00+01:00",
                end_date: "2020-11-03T00:00:00+01:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 10,
                total: 270,
                taxes: 24.55
              },
              {
                start_date: "2021-07-05T00:00:00+02:00",
                end_date: "2021-07-11T00:00:00+02:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 14,
                total: 378,
                taxes: 34.36
              },
              {
                start_date: "2021-07-06T00:00:00+02:00",
                end_date: "2021-07-06T00:00:00+02:00",
                product_code: "0299",
                description: "VARIOS",
                currency: "EUR",
                quantity: 1,
                total: 13,
                taxes: 1.18
              }
            ],
            transactions: [
              {
                date: "2021-07-05T00:00:00+02:00",
                amount: 10,
                currency: null
              },
              {
                date: "2021-07-06T00:00:00+02:00",
                amount: 260,
                currency: null
              }
            ]
          },
          {
            id: "PROF-179604",
            guest_id: "HUES-742420",
            name: "CARLOS FANTI",
            total: 661,
            received: 270,
            remain: 391,
            taxes: 60.09,
            currency: "EUR",
            products: [
              {
                start_date: "2020-10-30T00:00:00+01:00",
                end_date: "2020-11-03T00:00:00+01:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 10,
                total: 270,
                taxes: 24.55
              },
              {
                start_date: "2021-07-05T00:00:00+02:00",
                end_date: "2021-07-11T00:00:00+02:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 14,
                total: 378,
                taxes: 34.36
              },
              {
                start_date: "2021-07-06T00:00:00+02:00",
                end_date: "2021-07-06T00:00:00+02:00",
                product_code: "0299",
                description: "VARIOS",
                currency: "EUR",
                quantity: 1,
                total: 13,
                taxes: 1.18
              }
            ],
            transactions: [
              {
                date: "2021-07-05T00:00:00+02:00",
                amount: 10,
                currency: null
              },
              {
                date: "2021-07-06T00:00:00+02:00",
                amount: 260,
                currency: null
              }
            ]
          }
        ]
      },
      {
        res_id: "**********",
        invoices: [
          {
            id: "PROF-179603",
            guest_id: "HUES-742420",
            name: "CARLOS FANTI",
            total: 661,
            received: 270,
            remain: 391,
            taxes: 60.09,
            currency: "EUR",
            products: [
              {
                start_date: "2020-10-30T00:00:00+01:00",
                end_date: "2020-11-03T00:00:00+01:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 10,
                total: 270,
                taxes: 24.55
              },
              {
                start_date: "2021-07-05T00:00:00+02:00",
                end_date: "2021-07-11T00:00:00+02:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 14,
                total: 378,
                taxes: 34.36
              },
              {
                start_date: "2021-07-06T00:00:00+02:00",
                end_date: "2021-07-06T00:00:00+02:00",
                product_code: "0299",
                description: "VARIOS",
                currency: "EUR",
                quantity: 1,
                total: 13,
                taxes: 1.18
              }
            ],
            transactions: [
              {
                date: "2021-07-05T00:00:00+02:00",
                amount: 10,
                currency: null
              },
              {
                date: "2021-07-06T00:00:00+02:00",
                amount: 260,
                currency: null
              }
            ]
          },
          {
            id: "PROF-179604",
            guest_id: "HUES-742420",
            name: "CARLOS FANTI",
            total: 661,
            received: 270,
            remain: 391,
            taxes: 60.09,
            currency: "EUR",
            products: [
              {
                start_date: "2020-10-30T00:00:00+01:00",
                end_date: "2020-11-03T00:00:00+01:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 10,
                total: 270,
                taxes: 24.55
              },
              {
                start_date: "2021-07-05T00:00:00+02:00",
                end_date: "2021-07-11T00:00:00+02:00",
                product_code: "0100",
                description: "ALOJAMIENTO",
                currency: "EUR",
                quantity: 14,
                total: 378,
                taxes: 34.36
              },
              {
                start_date: "2021-07-06T00:00:00+02:00",
                end_date: "2021-07-06T00:00:00+02:00",
                product_code: "0299",
                description: "VARIOS",
                currency: "EUR",
                quantity: 1,
                total: 13,
                taxes: 1.18
              }
            ],
            transactions: [
              {
                date: "2021-07-05T00:00:00+02:00",
                amount: 10,
                currency: null
              },
              {
                date: "2021-07-06T00:00:00+02:00",
                amount: 260,
                currency: null
              }
            ]
          }
        ]
      }
    ]
  }
};
