import brand from "./modules/brand/handlers";
import checkin from "./modules/checkin/handlers";
import aws from "./modules/aws/handlers";
import integration from "./modules/integration/handlers";
import ocr from "./modules/ocr/handlers";
import languages from "./modules/languages/handlers";
import payment from "./modules/payment/handlers";
/*
  We define the default responses when mocks are activated for each endpoint. 
  If necessary, they can be overwritten in each of the tests.
*/
export const handlers = [
  ...brand,
  ...checkin,
  ...aws,
  ...integration,
  ...ocr,
  ...languages,
  ...payment,
];
