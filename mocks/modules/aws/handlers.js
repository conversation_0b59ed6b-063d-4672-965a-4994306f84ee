import { rest } from "msw";

import { defaultSuggestion, defaultSelection } from "./data";

export default [
	rest.post(
		"https://places.geo.eu-west-1.amazonaws.com/places/v0/indexes/*/search/suggestions",
		(req, res, ctx) => {
			return res(ctx.status(200), ctx.json(defaultSuggestion));
		},
	),
	rest.post(
		"https://places.geo.eu-west-1.amazonaws.com/places/v0/indexes/*/search/text",
		(req, res, ctx) => {
			return res(ctx.status(200), ctx.json(defaultSelection));
		},
	),
	rest.post("https://firehose.eu-west-1.amazonaws.com/", (req, res, ctx) => {
		return res(ctx.status(200));
	}),
];
