import { rest } from "msw";
import { defaultReservation, charges, receptionReservation } from "./data";

export default [
	rest.get("*/brands/:id/reservations", (req, res, ctx) => {
		return res(ctx.status(200), ctx.json(defaultReservation));
	}),
	rest.post("*/brands/:id/reservations/pre-check", (req, res, ctx) => {
		return res(ctx.status(204));
	}),
	rest.get(
		"*/integrations/brands/:id/reservations/charges",
		(req, res, ctx) => {
			return res(ctx.status(200), ctx.json(charges));
		},
	),
];
