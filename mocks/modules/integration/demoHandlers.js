import { rest } from "msw";
import { demoReservation, demoCharges, completedReservation, multipleReservations } from "./data";
import receptionUtils from "@/utils/receptionUtils";


export default [
	rest.get("*/brands/:id/reservations", (req, res, ctx) => {
		if (!receptionUtils.setReceptionMode()) {
			const localizer = req.url.searchParams.get("reservation_code");
			const lastName = req.url.searchParams.get("last_name");
			const firstName = req.url.searchParams.get("first_name");


			return res(
				ctx.status(200),
				ctx.json({
					data: [
						{
							...demoReservation.data[0],
							res_localizer: localizer,
							guests: [
								{
									...demoReservation.data[0].guests[0],
									last_name: lastName,
									name: firstName,
									holder: true,
								},
								...demoReservation.data[0].guests,
							],
						},
					],
				}),
			);
		}

		return res(ctx.status(200), ctx.json(completedReservation));
	}),

	rest.post("*/brands/:id/reservations/pre-check", (req, res, ctx) => {
		return res(ctx.status(204));
	}),

	rest.get(
		"*/integrations/brands/:id/reservations/charges",
		(req, res, ctx) => {
			return res(ctx.status(200), ctx.json(demoCharges));
		},
	),
];

// rest.get("*/brands/:id/reservations", (req, res, ctx) => {

// 	return res(
// 		ctx.status(200),
// 		ctx.json(
// 			multipleReservations
// 		),
// 	);
// })
