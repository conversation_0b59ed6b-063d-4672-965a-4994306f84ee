import { rest } from "msw";
import {
	defaultBrand,
	customConfig,
	defaultBrandGdpr,
	defaultConfig,
} from "./data";
export default [
	rest.get("*/brands/:id/info", (req, res, ctx) => {
		return res(ctx.status(200), ctx.json(defaultBrand));
	}),
	rest.get("*/brands/:id/products/:id/configuration", (req, res, ctx) => {
		return res(ctx.status(200), ctx.json(customConfig));
	}),
	rest.get("*/brands/:id/gdpr", (req, res, ctx) => {
		return res(ctx.status(200), ctx.json(defaultBrandGdpr));
	}),
];
