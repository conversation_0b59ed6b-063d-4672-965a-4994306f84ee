import { ccaaList, provinces } from "../../../src/utils/ccaa";
import { countries } from "../../../src/utils/countries";

export const defaultBrand = {
	data: {
		id: 304,
		name: "Hotel Liabeny",
		logo: "https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/logo/original-1612508161361.jpg",
		background_image:
			"https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/background/original.jpg",
		background_color: "#f2bf08",
		email: "<EMAIL>",
		street: "Parc bit",
		city: "Palma, Spain",
		place_name: "Palma",
		place_country: "ES",
		place_adm_area: "PM",
		stars: 4,
		sending_email: "",
		stay_time: 7,
		time_zone: "Europe/Paris",
		products: [
			{
				id: 209,
				product_id: 21,
				active: 1,
				name: "autocheckin",
				active_by_name: {
					autocheckin: 1,
				},
			},
			{
				id: 222,
				product_id: 23,
				active: 1,
				name: "payments",
				active_by_name: {
					payments: 1,
				},
			},
			{
				id: 259,
				product_id: 24,
				active: 1,
				name: "configuration",
				active_by_name: {
					configuration: 1,
				},
			},
		],
	},
};
export const chainBrand = {
	data: {
		id: 1,
		name: "Chain Test",
		logo: "https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/logo/original-1612508161361.jpg",
		children: [
			{
				id: 2,
				name: "Hotel 2",
				logo: "https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/logo/original-1612508161361.jpg",
				background_image:
					"https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/background/original.jpg",
				background_color: "#f2bf08",
				email: "<EMAIL>",
				street: "Parc bit",
				city: "Palma, Spain",
				place_name: "Palma",
				place_country: "ES",
				place_adm_area: "PM",
				stars: 4,
				sending_email: "",
				stay_time: 7,
				time_zone: "Europe/Paris",
				products: [
					{
						id: 209,
						product_id: 21,
						active: 1,
						name: "autocheckin",
						active_by_name: {
							autocheckin: 1,
						},
					},
					{
						id: 222,
						product_id: 23,
						active: 1,
						name: "payments",
						active_by_name: {
							payments: 1,
						},
					},
					{
						id: 259,
						product_id: 24,
						active: 1,
						name: "configuration",
						active_by_name: {
							configuration: 1,
						},
					},
				],
			},
			{
				id: 3,
				name: "Hotel 3",
				logo: "https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/logo/original-1612508161361.jpg",
				background_image:
					"https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/background/original.jpg",
				background_color: "#f2bf08",
				email: "<EMAIL>",
				street: "Parc bit",
				city: "Palma, Spain",
				place_name: "Palma",
				place_country: "ES",
				place_adm_area: "PM",
				stars: 4,
				sending_email: "",
				stay_time: 7,
				time_zone: "Europe/Paris",
				products: [
					{
						id: 209,
						product_id: 21,
						active: 1,
						name: "autocheckin",
						active_by_name: {
							autocheckin: 1,
						},
					},
					{
						id: 222,
						product_id: 23,
						active: 1,
						name: "payments",
						active_by_name: {
							payments: 1,
						},
					},
					{
						id: 259,
						product_id: 24,
						active: 1,
						name: "configuration",
						active_by_name: {
							configuration: 1,
						},
					},
				],
			},
			{
				id: 304,
				name: "Hotel 4",
				logo: "https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/logo/original-1612508161361.jpg",
				background_image:
					"https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/background/original.jpg",
				background_color: "#f2bf08",
				email: "<EMAIL>",
				street: "Parc bit",
				city: "Palma, Spain",
				place_name: "Palma",
				place_country: "ES",
				place_adm_area: "PM",
				stars: 4,
				sending_email: "",
				stay_time: 7,
				time_zone: "Europe/Paris",
				products: [
					{
						id: 209,
						product_id: 21,
						active: 0,
						name: "autocheckin",
						active_by_name: {
							autocheckin: 0,
						},
					},
					{
						id: 222,
						product_id: 23,
						active: 1,
						name: "payments",
						active_by_name: {
							payments: 1,
						},
					},
					{
						id: 259,
						product_id: 24,
						active: 1,
						name: "configuration",
						active_by_name: {
							configuration: 1,
						},
					},
				],
			},
			{
				id: 5,
				name: "Hotel 5",
				logo: "https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/logo/original-1612508161361.jpg",
				background_image:
					"https://images.hotelinking.com/brands/d92fdb86-8432-4eaa-9c7c-4df30fdae7ac/images/background/original.jpg",
				background_color: "#f2bf08",
				email: "<EMAIL>",
				street: "Parc bit",
				city: "Palma, Spain",
				place_name: "Palma",
				place_country: "ES",
				place_adm_area: "PM",
				stars: 4,
				sending_email: "",
				stay_time: 7,
				time_zone: "Europe/Paris",
				products: [],
			},
		],
	},
};
export const defaultConfig = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
						active: "true",
					},
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
						active: "true",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "false",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "false",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "false",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "false",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "false",
						name: "document_number",
						type: "text",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "false",
						name: "nationality",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "true",
						fill_from_holder: "false",
						name: "residence_country",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "false",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "false",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "CCAA",
						type: "select",
						options: [...ccaaList],
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "province",
						type: "select",
						options: [...provinces],
					},
					{
						position: 17,
						active: "false",
						fill_from_holder: "false",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		disable_send_documents_page: false,
		scan_children_like_adults: false,
		children_sign_documents: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,
		time_limit_checkin: 0,
		custom_confirmation_text: false,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
		allow_expired_documents: false,
		disable_address_autocomplete: false,
		send_signed_documents_to_reception: true,
		reception_signature: true,
		scan_on_reception: false,
		child_data_with_holder: false,
		arrival_time: true,
		not_allow_passports_from_country_brand: false,
	},
};

export const fillFromHolderConfig = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
						active: "true",
					},
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
						active: "true",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "true",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "true",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "true",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "true",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "true",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "true",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "true",
						name: "document_number",
						type: "text",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "true",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "true",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "true",
						name: "nationality",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "true",
						fill_from_holder: "true",
						name: "residence_country",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "true",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "true",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "true",
						required: "true",
						name: "CCAA",
						type: "select",
						options: [...ccaaList],
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "province",
						type: "select",
						options: [...provinces],
					},
					{
						position: 17,
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
					{
						position: 17,
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "region",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "subregion",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		scan_children_like_adults: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,
		time_limit_checkin: 0,
		custom_confirmation_text: false,
		send_signed_documents_to_reception: false,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
		allow_expired_documents: false,
		reception_signature: false,
		disable_address_autocomplete: false,
	},
};

export const paymentsConfig = {
	data: {
		...defaultConfig.data,
		paymentsActive: 1,
	},
};

export const advancedScanConfig = {
	data: {
		...defaultConfig.data,
		advanced_scan: true,
	},
};

export const defaultBrandGdpr = {
	data: {
		restricted_portal: true,
		first_eprivacy_page:
			"<div><p>Para conectarse a nuestra wifi privada necesitamos saber primero si está o no alojado como cliente. </p><p>Según su respuesta, le solicitaremos una serie de datos personales para completar la conexión.</p></div>",
		second_eprivacy_page:
			'<div>En el proceso de conexión a la wifi que va a comenzar, Company nameee recopila y trata sus datos personales como responsable y según lo establecido en nuestra <a class="privacy"> Politica de Privacidad</a>. <br>Recogemos los datos para garantizar la seguridad de la conexión, siendo la base legal para ello el interés legítimo del responsable en prevenir accesos no autorizados, la detección de incidentes y la prevención de ataques. Si consiente, los datos recogidos serán cedidos, únicamente, al establecimiento en que se encuentra y que le permite acceder a esta wifi. La finalidad es que el mismo pueda enviarle comunicaciones comerciales relacionadas con los servicios contratados, siendo la base legal para ello el contrato que ha suscrito con el establecimiento. Puede contactarnos y ejercer sus derechos de acceso, rectificación, supresión, limitación del tratamiento, oposición y <NAME_EMAIL> o según lo establecido en nuestra <a class="privacy"> Politica de Privacidad</a>. <br> </div>',
		legal_text:
			'<div> <h2>Política de Privacidad</h2> <p>Última actualización: <strong>21 de Mayo de 2018</strong></p> <hr> <h4>1.- Identificación y descripción</h4> <p><strong>1.1.- Identificación</strong></p> <p>Los datos que se recopilan mediante el uso de este servicio son incorporados a un registro de actividades de tratamiento del que es responsable Company nameee, con domicilio en Company addresss.</p> <p> NIF: Company NIFFF<br> Correo electrónico: <EMAIL> </p> <p><strong>1.2.- Descripción</strong></p> <p>La aceptación de la política de privacidad de Company nameee (en adelante “Política de Privacidad”), es condición necesaria para la conexión al siguiente servicio de wifi privada (en adelante el “Servicio”).</p> <p>Esta Política de Privacidad regula la recopilación, tratamiento y uso de tu información personal y no personal como usuario del Servicio, a partir de la fecha de entrada en vigor que aparece en el encabezado.</p> <div>Company nameee cede al establecimiento en el que te encuentras alojado, o visitando por otro motivo, la información personal recopilada cuando usas el Servicio. Indicamos los detalles de tal cesión en esta Política de Privacidad y especialmente en el apartado 7.</div> <p>Declaras que toda la información que proporciones para acceder al Servicio, antes y durante su utilización, es verdadera, completa y precisa.</p> <h4>2.- Información recopilada</h4> <p>El Servicio para usarse requiere de un registro, a través de un formulario o tu perfil de Facebook. La información (personal o no personal) recopilada por el Servicio puede variar en función de eso.</p> <p>La información personal y no personal recopilada por el Servicio nos llegará por tres vías: 1) la recopilada automáticamente 2) la que nos proporciones voluntariamente y 3) la proporcionada por terceros.</p> <p><strong>2.1.- La recopilada automáticamente</strong></p> <p>Esta información consistirá en:</p> <ul> <li>La recopilada mediante cookies o mecanismos similares almacenados en tu dispositivo, siempre con tu consentimiento. Consulta nuestra <a href="http://app.hotelinking.com/cookies-policy" target="_blank">Política de Cookies</a> para más información.</li> <li>La IP desde la que se realiza la conexión, el tipo de dispositivo usado y sus características, tu MAC, la versión del sistema operativo, el tipo de navegador, el idioma, la fecha, el lugar desde el que haces la conexión, la hora de la solicitud o la red móvil empleada, entre otros.</li> <li>Datos de uso del Servicio y posibles errores detectados durante su utilización.</li> </ul> <p><strong>2.2.- La que proporcionas voluntariamente</strong></p> <p>Esta información consistirá en:</p> <div><ul><li>La requerida por Company nameee&nbsp;para completar el registro y usar la Wifi privada si completas el formulario y estás alojado en el establecimiento: número de habitación, nombre y primer apellido, género, correo electrónico y fecha de nacimiento.</li></ul></div><ul> <li><br>Todos los datos tienen carácter obligatorio.</li> </ul> <p>Si fueras menor de  años, para registrarte y usar el Servicio deberás indicarnos expresamente que dispones de la autorización de un representante legal, como tus padres. De lo contrario no podrás completar el registro.</p> <div><ul><li>La requerida por Company nameee&nbsp;para completar el registro y usar la Wifi privada si usas tu perfil de Facebook para ello, ya estés o no alojado en el hotel: en este caso recibiremos los datos de tu perfil público de Facebook, consistentes en tu nombre, género, nombre de usuario e identificador de usuario (número de cuenta), intervalo de edad, idioma, país, correo electrónico, foto del perfil, foto de portada y redes.</li></ul></div><ul>  <li>La información, personal o no, que pudieran contener los mensajes enviados a través de los canales de contacto establecidos en el Servicio, por ejemplo tu nombre, alias o correo electrónico.</li> </ul> <p><strong>2.3.- La proporcionada por terceros</strong></p> <p>Esta información consistirá en:</p> <ul> <li>La proporcionada por Facebook si utilizas tu perfil para completar el registro en el Servicio.</li> <li>La proporcionada por proveedores de hardware de la red de comunicación wifi.</li> </ul> <h4>3.- Edad</h4> <p>En cuanto al uso del Servicio, declaras que tienes al menos  años y que dispones de la capacidad legal necesaria para vincularte por este acuerdo y utilizar el sitio de conformidad con sus términos y condiciones, que comprendes y reconoces en su totalidad.</p> <p>Si fueras menor de  años, para registrarte y usar el Servicio deberás indicarnos expresamente que dispones de la autorización de un representante legal, como tus padres. De lo contrario no podrás completar el registro.</p> <h4>4.- Derechos y finalidades</h4> <div>Los datos personales que nos facilites quedan incorporados y serán tratados por Company nameee, con el fin de poder atender tus peticiones, prestar el servicio solicitado, controlar su calidad, garantizar la seguridad y prestación de las comunicaciones.</div> <div>Puedes ejercer en cualquier momento los derechos de acceso, rectificación, supresión, limitación de tu tratamiento, oposición y portabilidad de tus datos de carácter personal mediante correo electrónico dirigido a: <EMAIL> O la dirección postal:Company addresss</div> <p>En ambos casos deberás identificarte con tu nombre y apellidos, además de una copia de tu DNI o ID nacional.</p> <p><a href="https://www.aepd.es/reglamento/derechos/index.html" target="_blank">Aquí</a> puedes encontrar los diferentes modelos para ejercer tales derechos.</p> <p>En el caso de que hayas otorgado el consentimiento para alguna finalidad específica, tienes derecho a retirar el consentimiento en cualquier momento, sin que ello afecte a la licitud del tratamiento basado en el consentimiento previo a su retirada.</p> <p>Además, si consideras que hay un problema con la forma en que el Servicio está manejando tus datos, puedes dirigir tus reclamaciones a la <a href="https://www.aepd.es/agencia/contacto.html" target="_blank">autoridad de protección de datos</a> que corresponda, siendo la <a href="http://www.agpd.es/portalwebAGPD/index-ides-idphp.php" target="_blank">Agencia Española de Protección de Datos</a> la indicada en el caso de España.</p> <h4>5.- Uso de los datos y base legal</h4> <div>Company nameee usará los datos recopilados para:</div> <ul> <li>Administrar, proporcionar y actualizar el Servicio (siendo la base legal para ello el interés legítimo del responsable en prevenir accesos no autorizados, la detección de incidentes y la prevención de ciberataques).</li> <li>Procesar tu registro de usuario (siendo la base legal tu consentimiento).</li> <li>Atender las cuestiones que plantees (siendo la base legal el interés legítimo del responsable en comunicarse con el usuario respecto al uso de su Servicio).</li> <li>Proponerte mediante correo electrónico la participación en futuras actividades comerciales del Servicio, siempre y cuando lo acabes autorizando (siendo la base legal el interés legítimo del responsable en materia de prospección comercial).</li> <li>Planificar y desarrollar las actividades del Servicio, como por ejemplo el envío de comunicaciones comerciales por parte de terceros colaboradores como los establecimientos (siendo la base legal para ello tu consentimiento específico o tu contrato con el alojamiento, según el caso).</li> <li>Ayudar en el mantenimiento de la seguridad del Servicio, investigar actividades ilícitas, hacer cumplir nuestros términos y condiciones y ayudar a los cuerpos y fuerzas de seguridad del estado en el marco de sus eventuales investigaciones (siendo la base legal para ello el interés legítimo del responsable en prevenir accesos no autorizados, la detección de incidentes y la prevención de ciberataques).</li> </ul> <div>Asimismo, Company nameee podrá utilizar la información de los usuarios en forma de datos agregados y anónimos para mostrarlos a terceros. También podrá compartir estadísticas y la información demográfica sobre los usuarios y su utilización del Servicio con terceros. Nada de esto permitirá a esos terceros identificarte personalmente.</div> <p><strong>5.1.- En mails y formularios de contacto</strong></p> <p>La web del Servicio cuenta con un cifrado SSL que permite el envío seguro de tus datos personales a través de formularios de contacto de tipo estándar.</p> <p>Los datos personales recogidos serán objeto de tratamiento automatizado e incorporados a los correspondientes ficheros del registro de actividad y de los que el Servicio es titular.</p> <p>En ese sentido: nos llegará tu IP, que será usada para comprobar el origen del mensaje con objeto de ofrecerte recomendaciones adecuadas (por ejemplo presentar la información en el idioma correcto) y para detectar posibles irregularidades (por ejemplo posibles intentos de ciberataque al Servicio), así como datos relativos a tu ISP.</p> <p><strong>5.2.- En redes sociales</strong></p> <p>El Servicio cuenta con perfiles en algunas de las principales redes sociales de Internet, reconociéndose responsable del tratamiento en relación con los datos publicados en los mismos (por ejemplo, fotos subidas por el Servicio en las que aparecen caras de personas).</p> <p>El tratamiento que el Servicio llevará a cabo con los datos dentro de cada una de las referidas redes será, como máximo, el que la red social permita a los perfiles corporativos. Así pues, el Servicio podrá informar, cuando la ley no lo prohíba, a nuestros seguidores por cualquier vía que la red social permita sobre sus actividades u ofertas, así como prestar un servicio personalizado de atención al cliente.</p> <p>En ningún caso el Servicio extraerá datos de las redes sociales, a menos que se obtuviera puntual y expresamente el consentimiento del usuario para ello.</p> <p>Cuando, debido a la propia naturaleza de las redes sociales, el ejercicio efectivo de los derechos de protección de datos del seguidor quede supeditado a la modificación del perfil personal de este, el Servicio te ayudará y aconsejará a tal fin en la medida de sus posibilidades.</p> <h4>6.- Conservación de los datos</h4> <p>A continuación se indican por cuánto tiempo se conservan los datos tratados por el Servicio:</p> <ul> <li>Los datos desagregados serán conservados sin plazo de supresión.</li> <li>Los datos de los usuarios registrados serán conservados el mínimo necesario, pudiendo mantenerse hasta 5 años, según el art. 1964 del Código Civil (acciones personales sin plazo especial).</li> <li>Los datos de los suscriptores a las comunicaciones comerciales aceptadas, ya sean usuarios alojados o no, serán conservados desde que el usuario se suscribe hasta que se da de baja.</li> <li>Los datos de usuarios subidos por el Servicio a páginas y perfiles en redes sociales se conservarán desde que el usuario ofrece su consentimiento hasta que lo retira</li> </ul> <h4>7.- Datos cedidos y compartidos con terceros</h4> <div>Company nameee cederá los datos recopilados en el registro al Servicio. Dicha cesión se realizará al hotel en el que el usuario se aloje o encuentre en el momento de utilizar el servicio de wifi privada.</div> <p>El hotel únicamente podrá usar los datos cedidos para:</p> <ul> <li>Enviar comunicaciones comerciales al usuario alojado (de acuerdo a la base legal del contrato que le vincula con el hotel).</li> <li>Enviar comunicaciones comerciales al usuario no alojado, si el mismo lo consiente en el momento del registro (en cuyo caso la base legal sería el consentimiento)</li> <li>Garantizar la seguridad y estabilidad del servicio de wifi privada ofrecido (siendo la base legal para ello el interés legítimo del responsable en prevenir accesos no autorizados, la detección de incidentes y la prevención de ciberataques)</li> </ul> <div>Además, hay terceros que como encargados del tratamiento gestionan parte del Servicio y acceden por ello a datos recopilados por Company nameee.</div> <div>A ellos Company nameee les exige que cumplan esta Política de Privacidad en lo que les resulte aplicable, además de tener la suya propia, y las correspondientes previsiones contractuales.</div> <div>Company nameee podrá compartir, usar o preservar con terceros alguna de la información personal recopilada:</div> <ul><li>Para prestar el Servicio:<br>Los proveedores de servicios que prestan funciones en nuestro nombre, como el alojamiento, la analítica comercial, el servicio de atención cliente o marketing. Estos proveedores de servicios pueden recopilar y tener acceso a la información que les sea necesaria para desempeñar sus funciones, pero no tienen permitido compartir o utilizar la información para ningún otro propósito.</li> <li>Los proveedores de servicios que prestan funciones en nuestro nombre, como el alojamiento, la analítica comercial, el servicio de atención cliente o marketing. Estos proveedores de servicios pueden recopilar y tener acceso a la información que les sea necesaria para desempeñar sus funciones, pero no tienen permitido compartir o utilizar la información para ningún otro propósito.<br>a) Si creemos que es razonablemente necesario para satisfacer cualquier ley, proceso legal o interés legítimo. En cualquier caso, sólo proporcionaremos la información estrictamente requerida <br> </li> <li>Para cooperar con las autoridades competentes:<br> Si creemos que es razonablemente necesario para satisfacer cualquier ley, proceso legal o interés legítimo. En cualquier caso, sólo proporcionaremos la información estrictamente requerida <br> </li> </ul> <h4>8.- Medidas de seguridad</h4> <div>Company nameee adopta todas las medidas técnicas y organizativas necesarias para proteger la seguridad e integridad de la información personal y no personal recopilada. Tanto frente a accesos no autorizados como su alteración, pérdida o destrucción accidentales.</div> <div>En todo caso, Company nameee no puede garantizar la seguridad absoluta de la información recopilada, por lo que debes colaborar y utilizar en todo momento el sentido común sobre la información compartida.</div> <p>Entiendes y reconoces que, incluso después de su eliminación, la información personal y no personal puede permanecer visible en caché o si otros usuarios la han copiado o almacenado.</p> <h4>9.- Cambios en la Política de Privacidad</h4> <p>Podremos actualizar esta Política de Privacidad en el futuro.</p> <p>Te informaremos sobre sus cambios enviando un aviso a la dirección de correo electrónico facilitada y/o colocando un aviso en un lugar prominente de nuestra web o del propio servicio.</p> <h4>10.- Contacto</h4> <p>Si tienes dudas sobre esta Política de Privacidad, contacta con nosotros en:</p> <div>Email : <EMAIL></div> <div>Dirección: Company addresss</div> </div>',
	},
};
export const customConfig = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "false",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "false",
						name: "document_number",
						type: "text",
						required: "true",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "nationality",
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "residence_country",
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "CCAA",
						type: "select",
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "province",
						type: "select",
					},
					{
						position: 17,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		scan_children_like_adults: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,
		time_limit_checkin: 0,
		custom_confirmation_text: false,
		send_signed_documents_to_reception: true,
		reception_signature: true,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
	},
};
export const noResidenceCountryConfig = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "false",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "false",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "false",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "false",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "false",
						name: "document_number",
						type: "text",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "false",
						name: "nationality",
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "false",
						fill_from_holder: "false",
						name: "residence_country",
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "false",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "false",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "CCAA",
						type: "select",
						options: [...ccaaList],
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "province",
						type: "select",
						options: [...provinces],
					},
					{
						position: 17,
						active: "false",
						fill_from_holder: "false",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "false",
						fill_from_holder: "false",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		scan_children_like_adults: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,
		time_limit_checkin: 0,
		custom_confirmation_text: false,
		send_signed_documents_to_reception: false,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
		allow_expired_documents: false,
	},
};

export const reservationHolderConfig = {
	data: {
		reservation_holder_not_modifiable: true,
		...defaultConfig.data,
	},
};

export const localizerResFilterConfig = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
						active: "true",
					},
					{
						name: "check_in",
						type: "date",
						active: "true",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
						active: "true",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "false",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "false",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "false",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "false",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "false",
						name: "document_number",
						type: "text",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "false",
						name: "nationality",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "true",
						fill_from_holder: "false",
						name: "residence_country",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "false",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "false",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "CCAA",
						type: "select",
						options: [...ccaaList],
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "province",
						type: "select",
						options: [...provinces],
					},
					{
						position: 17,
						active: "false",
						fill_from_holder: "false",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		scan_children_like_adults: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,
		time_limit_checkin: 0,
		custom_confirmation_text: false,
		send_signed_documents_to_reception: false,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
		allow_expired_documents: false,
		reception_signature: false,
		disable_address_autocomplete: false,
	},
};
export const localizerResFilterConfigWithTimeLimits = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
						active: "true",
					},
					{
						name: "check_in",
						type: "date",
						active: "true",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
						active: "true",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "false",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "false",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "false",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "false",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "false",
						name: "document_number",
						type: "text",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "false",
						name: "nationality",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "true",
						fill_from_holder: "false",
						name: "residence_country",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "false",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "false",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "CCAA",
						type: "select",
						options: [...ccaaList],
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "province",
						type: "select",
						options: [...provinces],
					},
					{
						position: 17,
						active: "false",
						fill_from_holder: "false",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		scan_children_like_adults: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,

		custom_confirmation_text: false,
		send_signed_documents_to_reception: false,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
		allow_expired_documents: false,
		reception_signature: false,
		disable_address_autocomplete: false,
		time_limit_checkin: "2",
		close_time_limit_checkin: "-3",
		activate_time_limit: true,
	},
};
export const localizerResFilterConfigWithNoTimeLimits = {
	data: {
		active: true,
		identification: {
			child_form: [
				[
					{
						active: "false",
						required: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "false",
						required: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
				],
			],
			reservation_inputs: [
				[
					{
						name: "last_name",
						type: "text",
						minLength: "2",
						maxLength: "50",
						active: "true",
					},
					{
						name: "check_in",
						type: "date",
						active: "true",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
						active: "true",
					},
				],
			],
			reservation_filters: [
				[
					{
						name: "check_in",
						type: "date",
					},
				],
				[
					{
						name: "check_out",
						type: "date",
					},
				],
				[
					{
						name: "first_name",
						type: "text",
					},
				],
				[
					{
						name: "reservation_code",
						type: "text",
					},
				],
			],
			validate_data_scan: [
				[
					{
						position: 1,
						active: "true",
						fill_from_holder: "false",
						name: "name",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 2,
						active: "true",
						fill_from_holder: "false",
						name: "surname",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 3,
						active: "true",
						fill_from_holder: "false",
						name: "second_surname",
						required: "false",
						type: "text",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 4,
						active: "true",
						fill_from_holder: "false",
						name: "birthday_date",
						type: "date",
					},
					{
						position: 5,
						active: "true",
						fill_from_holder: "false",
						name: "gender",
						type: "select",
						options: ["male", "female"],
					},
					{
						position: 6,
						active: "true",
						fill_from_holder: "false",
						name: "document_type",
						type: "select",
						options: ["identity_card", "passport"],
					},
					{
						position: 7,
						active: "true",
						fill_from_holder: "false",
						name: "document_number",
						type: "text",
						minLength: "4",
						maxLength: "20",
					},
					{
						position: 8,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_issue",
						type: "date",
					},
					{
						position: 9,
						active: "true",
						fill_from_holder: "false",
						name: "date_of_expiry",
						type: "date",
					},
					{
						position: 10,
						active: "true",
						fill_from_holder: "false",
						name: "nationality",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 11,
						active: "true",
						fill_from_holder: "false",
						name: "residence_country",
						options: [...countries],
						type: "autocomplete",
						countryInput: true,
					},
					{
						position: 12,
						active: "true",
						fill_from_holder: "false",
						name: "address",
						type: "autocomplete",
						minLength: "5",
						maxLength: "500",
					},
					{
						position: 14,
						active: "true",
						fill_from_holder: "false",
						name: "postal_code",
						type: "text",
						minLength: "3",
						maxLength: "20",
					},
					{
						position: 15,
						active: "true",
						fill_from_holder: "false",
						required: "true",
						name: "CCAA",
						type: "select",
						options: [...ccaaList],
					},
					{
						position: 16,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "province",
						type: "select",
						options: [...provinces],
					},
					{
						position: 17,
						active: "false",
						fill_from_holder: "false",
						required: "false",
						name: "telephone",
						type: "phone",
						minLength: "2",
						maxLength: "50",
					},
					{
						position: 18,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						position: 13,
						active: "true",
						fill_from_holder: "false",
						required: "false",
						name: "municipality",
						type: "text",
						minLength: "2",
						maxLength: "100",
					},
				],
			],
		},
		max_attempts_reservation: 3,
		child_required_identity_documents_age: 18,
		optional_scan: true,
		max_attempts_child: 3,
		max_attempts_document: 3,
		partial_checkin: true,
		room_type_selection: false,
		max_attempts_telephone: 3,
		telephone: true,
		telephone_notifications: true,
		comments: true,
		signed_documents: true,
		scan_children_like_adults: false,
		custom_scan_text: false,
		send_identity_documents_to_PMS: true,

		custom_confirmation_text: false,
		send_signed_documents_to_reception: false,
		max_attempts_validate_telephone_code: 3,
		max_attempts_email_validation: 3,
		allow_expired_documents: false,
		reception_signature: false,
		disable_address_autocomplete: false,
		time_limit_checkin: "2",
		close_time_limit_checkin: "-3",
		activate_time_limit: false,
	},
};

export const commentsConfig = {
	data: {
		show_comments_only_on_holder: true,
		...defaultConfig.data,
	},
};

export const childFormConfig = {
	...defaultConfig,
	data: {
		...defaultConfig.data,
		scan_children_like_adults: false,
		children_sign_documents: true,
		identification: {
			...defaultConfig.data.identification,
			child_form: [
				[
					{
						active: "true",
						required: "true",
						name: "birthday",
						type: "date",
					},
					{
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "telephone",
						type: "phone",
					},
					{
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "email",
						type: "email",
					},
					{
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "address",
						type: "autocomplete",
					},
					{
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "postal_code",
						type: "text",
					},
					{
						active: "true",
						fill_from_holder: "true",
						required: "false",
						name: "municipality",
						type: "text",
					}
				]
			]
		}
	}
};

