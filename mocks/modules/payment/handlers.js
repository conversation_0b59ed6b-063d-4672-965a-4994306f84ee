import { rest } from "msw";
import { integrations, defaultOrder } from "./data";
export default [
	rest.get("*/payments/brands/:id/integrations", (req, res, ctx) => {
		return res(ctx.status(200), ctx.json(integrations));
	}),
	rest.get("*/payments/brands/:id/orders/:id", (req, res, ctx) => {
		return res(ctx.status(200), ctx.json(defaultOrder));
	}),
	rest.post(
		"*/payments/brands/:id/integrations/:id/orders",
		(req, res, ctx) => {
			return res(ctx.status(200), ctx.json(integrations));
		},
	),
];
