export const integrations = {
  data: [
    {
      id: 2,
      brand_id: 1,
      method_id: 1,
      platform_id: 1,
      platform: {
        id: 1,
        name: "redsys",
        config_schema:
          '{"description":"Config schema for Redsys","type":"object","properties":{"Ds_Merchant_MerchantCode":{"description":"Number of commerce given by Bank","type":"string"},"Ds_Merchant_Terminal":{"description":"Terminal number provided by Bank","type":"string"},"Clave_Comercio":{"description":"Commerce key","type":"string"},"required":["Ds_Merchant_MerchantCode","Ds_Merchant_Terminal","Clave_Comercio"]}}',
        available_methods: [
          {
            id: 1,
            name: "card"
          },
          {
            id: 2,
            name: "paypal"
          },
          {
            id: 3,
            name: "bizum"
          }
        ]
      },
      method: {
        id: 1,
        name: "card"
      }
    }
  ]
};

export const defaultOrder = {
  data: {
    id: "0DD861A2-E899-4AE2-A3E0-3CC48D3AE9EB",
    token:
      "8165e4ae9cbfb3a4c93b1b1b367d4ed59e0d2ce52846ac3a8e7f376474c38914f0d3b04ee3213ebee37d00cd61a15c7816d76d06709542a06bc70e051beef5be",
    clientId: "41D26741-822A-4D72-8689-3B61C1356EAA",
    status: "CREATED",
    service: "REDSYSPSD2",
    transaction: {
      created: "2022-05-13T09:46:48+0200",
      amount: "12000",
      authorization: "460205",
      error: "",
      method: "card",
      cardLastNumbers: "0004",
      bank: "SERVIRED, SOCIEDAD ESPANOLA DE MEDIOS DE PAGO, S.A."
    }
  }
};

export const paymentOrder = {
  data: {
    clientId: null,
    form: `\n            <form action="https://sis-t.redsys.es:25443/sis/realizarPago" method="post" id="redsys_form" name="redsys_form" >\n                <input type="hidden" name="Ds_MerchantParameters" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>\n                <input type="hidden" name="Ds_Signature" value="mTI26kwn3S2VFJqD+SbNP02t4AHzn2z46o8+1JAVibI="/>\n                <input type="hidden" name="Ds_SignatureVersion" value="HMAC_SHA256_V1"/>\n                <input type="submit" name="btn_submit" id="btn_submit" value="Send"  >\n            </form>\n        `,
    id: 1663087958, // Todo: Make this work to test payment
    service: "redsys",
    status: "created"
  }
};
