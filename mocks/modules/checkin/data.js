export const defaultDocuments = {
	data: [
		{
			id: 7,
			brand_id: 76,
			name: "Parte de policia",
			active: 1,
			extension: "pdf",
		},
		{
			id: 7,
			brand_id: 76,
			name: "Parte de policia",
			active: 1,
			extension: "pdf",
		},
	],
};

export const emptyDocuments = {
	data: [],
};

export const translatedGuestDocuments = {
	data: {
		id: 7,
		brand_id: 76,
		name: "Parte de policia",
		active: 1,
		extension: "pdf",
		document_translations: [
			{
				id: 12,
				language_id: 2,
				title: "Parte de policía",
				content:
					"<p>Parte nº <span style='display:none'>{{documentCount}}</span></p><p>Estimado/a dasdsad,</p><p>Por requerimiento de la autoridad policial del Estado Español, es necesario que lea y firme este documento en relación a su estancia en este establecimiento.</p><p>Su estancia desde el día 2022-02-05 hasta el día 2022-02-07, con un total de pernoctaciones de  2, con código de reserva 145299-005, confirma que su nombre es dasdsad y apellido  dasdasd, con nacionalidad ESP, nacido el día 1996-02-05, con número de documento  8515626912962 y fecha de expedición 2022-03-15.Su información será remitida a las autoridades correspondientes en el día de hoy.</p><p>Atentamente,</p><p>La Dirección del hotel</p>",
			},
		],
	},
};

export const signedDocuments = {
	data: {
		brandId: 76,
		reservationId: "145299-005",
		key: "a70aae8f876c1959e49cd228b271b0f7",
		bucket: "dev-autocheckin.hotelinking.com",
		files: [
			{
					documentCount: 1,
					documentId: 1,
					fileName: "parte-de-policia.pdf",
					url: "https://dev-autocheckin.hotelinking.com/a70aae8f876c1959e49cd228b271b0f7/parte-de-policia.pdf",
			},
			{
					documentCount: 1,
					documentId: 2,
					fileName: "politica-de-privacidad.pdf",
					url: "https://dev-autocheckin.hotelinking.com/a70aae8f876c1959e49cd228b271b0f7/politica-de-privacidad.pdf",
			},
		]
	},
};
