import { rest } from "msw";
import {
  defaultDocuments,
  signedDocuments,
  translatedGuestDocuments
} from "./data";
export default [
  rest.get("*/brands/:id/documents", (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(defaultDocuments));
  }),
  rest.post("*/brands/:id/documents", (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(signedDocuments));
  }),
  rest.get("*/guest-documents/:id", (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(translatedGuestDocuments));
  })
];
