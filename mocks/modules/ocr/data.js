import { ERROR_CODE_MAP } from "../../../src/errorCodes";

export const emptyScan = {
	valid: true,
	data: {
		document_type: null,
		document_subtype: null,
		nationality: null,
		document_number: null,
		side: "front",
		surname: null,
		name: null,
		birthday_date: "1980-01-01T00:00:00",
		personal_number: null,
		place_of_birth: null,
		gender: null,
		height: null,
		date_of_issue: "2021-06-02T00:00:00",
		date_of_expiry: "2031-06-02T00:00:00",
		issuing_country: null,
		issuing_institution: null,
		address: {
			street_name: null,
			house_number: null,
			postcode: null,
			province: null,
		},
		signature: null,
		identity_document: null,
		second_surname: null,
	},
};

export const identityCardScan = {
	valid: true,
	data: {
		document_type: "identity_card",
		document_subtype: "D",
		nationality: "ESP",
		document_number: "99999999R",
		side: "front",
		surname: "<PERSON>",
		name: "<PERSON><PERSON>ime<PERSON>",
		birthday_date: "1990-01-01",
		personal_number: null,
		place_of_birth: null,
		gender: null,
		height: null,
		date_of_issue: "2015-01-01T00:00:00",
		date_of_expiry: "2025-01-01T00:00:00",
		issuing_country: null,
		issuing_institution: null,
		address: {
			street_name: null,
			house_number: null,
			postcode: null,
			province: null,
		},
		signature: "a looong long very log base64",
		second_surname: null,
	},
};

export const passportScan = {
	valid: true,
	data: {
		document_type: "passport",
		document_subtype: null,
		nationality: "FRA",
		document_number: "85987451E",
		side: "front",
		surname: "Pahlan",
		name: "Mokhles",
		birthday_date: "1990-02-18T00:00:00",
		place_of_birth: "Algerie",
		gender: "male",
		date_of_issue: "2015-12-25T00:00:00",
		date_of_expiry: "2025-12-24T00:00:00",
		issuing_country: "FRA",
		issuing_institution: "                     Prefecture",
		address: {
			street_name: null,
			house_number: null,
			postcode: null,
			province: null,
		},
		signature:
			"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",
		second_surname: null,
	},
};

export const passportScanWithoutSignature = {
	valid: true,
	data: {
		document_type: "passport",
		document_subtype: null,
		nationality: "FRA",
		document_number: "85987451E",
		side: "front",
		surname: "Pahlan",
		name: "Mokhles",
		birthday_date: "1990-02-18T00:00:00",
		place_of_birth: "Algerie",
		gender: "male",
		date_of_issue: "2015-12-25T00:00:00",
		date_of_expiry: "2025-12-24T00:00:00",
		issuing_country: "FRA",
		issuing_institution: "                     Prefecture",
		address: {
			street_name: null,
			house_number: null,
			postcode: null,
			province: null,
		},
		signature: null,
		second_surname: null,
	},
}
export const childPassport = {
	valid: true,
	data: {
		document_type: "passport",
		document_subtype: null,
		nationality: "IRL",
		side: "front",
		document_number: null,
		surname: null,
		name: "Cillian",
		birthday_date: "2014-05-16T00:00:00",
		personal_number: null,
		place_of_birth: null,
		gender: null,
		height: null,
		date_of_issue: "2018-10-05T00:00:00",
		date_of_expiry: "2023-10-04T00:00:00",
		issuing_country: "IRL",
		issuing_institution: null,
		address: {
			street_name: null,
			house_number: null,
			postcode: null,
			province: null,
		},
		signature: null,
		second_surname: null,
	},
};

export const expiredPassportScan = {
	valid: true,
	data: {
		document_type: "passport",
		document_subtype: null,
		nationality: "ESP",
		side: "front",
		document_number: "12345678A",
		surname: "Surname",
		name: "Name",
		birthday_date: "2000-01-01",
		personal_number: null,
		place_of_birth: null,
		gender: "male",
		height: null,
		date_of_issue: "2015-01-01T00:00:00",
		date_of_expiry: "2020-01-01T00:00:00",
		issuing_country: null,
		issuing_institution: null,
		address: {
			street_name: null,
			house_number: null,
			postcode: null,
			province: null,
		},
		signature: "a looong long very log base64",
		second_surname: null,
	},
};

export const passportWithAddress = {
	valid: true,
	data: {
		document_type: "passport",
		document_subtype: "D",
		nationality: "ESP",
		side: "front",
		document_number: "88888889A",
		surname: "Testing",
		name: "Especimen",
		birthday_date: "2000-01-01",
		personal_number: null,
		place_of_birth: null,
		gender: "male",
		height: null,
		date_of_issue: "2015-01-01T00:00:00",
		date_of_expiry: "2025-01-01T00:00:00",
		issuing_country: "ESP",
		issuing_institution: null,
		address: {
			street_name: "C. Miguel Forteza I Piña",
			house_number: "3",
			postcode: "07007",
			province: "Illes Balears",
		},
		signature: "a looong long very log base64",
		second_surname: "SecondSurname",
	},
};

export const errorScan = (errorName) => {
	const code = getErrorCode(errorName);
	return {
		error: {
			type: getErrorType(code),
			message: errorName,
			code,
		},
	};
};

function getErrorCode(message) {
	return ERROR_CODE_MAP[message] || "OCR_1_1";
}
function getErrorType(errorCode) {
	const firstNumber = errorCode.split("_")[1][0];
	switch (firstNumber) {
		case "1":
			return "Internal Error";
		case "2":
			return "External Error";
		case "3":
			return "Logic Error";
		default:
			return "Unknown Error Type";
	}
}
